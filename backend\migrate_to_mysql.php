<?php

// Check if vendor/autoload.php exists, if not, include the necessary files manually
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';

    // Load environment variables
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
} else {
    // Load environment variables manually
    if (file_exists(__DIR__ . '/.env')) {
        $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value);
        }
    }
}

// Include Database class manually
require_once __DIR__ . '/src/Database.php';

use App\Database;

class DatabaseMigrator
{
    private $sqliteConnection;
    private $mysqlConnection;

    public function __construct()
    {
        $this->connectToSQLite();
        $this->connectToMySQL();
    }

    private function connectToSQLite()
    {
        try {
            $dbPath = __DIR__ . '/database.sqlite';
            if (!file_exists($dbPath)) {
                throw new Exception("SQLite database file not found: {$dbPath}");
            }
            
            $dsn = "sqlite:{$dbPath}";
            $this->sqliteConnection = new PDO($dsn, null, null, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            echo "✅ Connected to SQLite database\n";
        } catch (PDOException $e) {
            throw new Exception("SQLite connection failed: " . $e->getMessage());
        }
    }

    private function connectToMySQL()
    {
        try {
            $host = $_ENV['DB_HOST'] ?? 'localhost';
            $dbname = $_ENV['DB_NAME'] ?? 'email_marketing';
            $username = $_ENV['DB_USER'] ?? 'root';
            $password = $_ENV['DB_PASS'] ?? '';

            // First, connect without database to create it if it doesn't exist
            $dsn = "mysql:host={$host};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            ]);

            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ MySQL database '{$dbname}' created/verified\n";

            // Now connect to the specific database
            $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
            $this->mysqlConnection = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]);
            echo "✅ Connected to MySQL database\n";
        } catch (PDOException $e) {
            throw new Exception("MySQL connection failed: " . $e->getMessage());
        }
    }

    public function migrate()
    {
        echo "\n🚀 Starting database migration from SQLite to MySQL...\n\n";

        try {
            // Clear existing tables
            echo "🧹 Dropping existing MySQL tables...\n";
            $this->clearExistingData();
            echo "✅ Existing tables dropped\n\n";

            // Create MySQL tables
            echo "📋 Creating MySQL tables...\n";
            Database::createTables();
            echo "✅ MySQL tables created successfully\n\n";

            // Migrate data
            $this->migrateUsers();
            $this->migrateTemplates();
            $this->migrateContacts();
            $this->migrateContactLists();
            $this->migrateContactListMembers();
            $this->migrateUploads();
            $this->migrateCampaigns();
            $this->migrateCampaignRecipients();
            $this->migrateEmailLogs();

            echo "\n🎉 Migration completed successfully!\n";
            echo "📊 Migration Summary:\n";
            $this->printMigrationSummary();

        } catch (Exception $e) {
            echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    private function clearExistingData()
    {
        $tables = ['email_logs', 'campaign_recipients', 'campaigns', 'uploads',
                  'contact_list_members', 'contact_lists', 'contacts', 'templates', 'users'];

        // Disable foreign key checks
        $this->mysqlConnection->exec("SET FOREIGN_KEY_CHECKS = 0");

        foreach ($tables as $table) {
            try {
                $this->mysqlConnection->exec("DROP TABLE IF EXISTS {$table}");
                echo "   Dropped {$table}\n";
            } catch (PDOException $e) {
                // Table might not exist yet, ignore
                echo "   Skipped {$table} (doesn't exist)\n";
            }
        }

        // Re-enable foreign key checks
        $this->mysqlConnection->exec("SET FOREIGN_KEY_CHECKS = 1");
    }

    private function migrateUsers()
    {
        echo "👥 Migrating users...\n";

        $stmt = $this->sqliteConnection->query("SELECT * FROM users");
        $users = $stmt->fetchAll();

        if (empty($users)) {
            echo "   No users to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO users (id, name, email, password, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        foreach ($users as $user) {
            $insertStmt->execute([
                $user['id'],
                $user['name'],
                $user['email'],
                $user['password'],
                $user['created_at'],
                $user['updated_at']
            ]);
        }

        echo "   ✅ Migrated " . count($users) . " users\n";
    }

    private function migrateTemplates()
    {
        echo "📄 Migrating templates...\n";

        $stmt = $this->sqliteConnection->query("SELECT * FROM templates");
        $templates = $stmt->fetchAll();

        if (empty($templates)) {
            echo "   No templates to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO templates (id, user_id, name, description, html_content, components, settings, thumbnail, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($templates as $template) {
            $insertStmt->execute([
                $template['id'],
                $template['user_id'],
                $template['name'],
                $template['description'],
                $template['html_content'],
                $template['components'] ?? null,
                $template['settings'] ?? null,
                $template['thumbnail'] ?? null,
                $template['created_at'],
                $template['updated_at']
            ]);
        }

        echo "   ✅ Migrated " . count($templates) . " templates\n";
    }

    private function migrateContacts()
    {
        echo "📧 Migrating contacts...\n";
        
        $stmt = $this->sqliteConnection->query("SELECT * FROM contacts");
        $contacts = $stmt->fetchAll();
        
        if (empty($contacts)) {
            echo "   No contacts to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO contacts (id, user_id, email, name, status, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($contacts as $contact) {
            $insertStmt->execute([
                $contact['id'],
                $contact['user_id'],
                $contact['email'],
                $contact['name'],
                $contact['status'],
                $contact['created_at'],
                $contact['updated_at']
            ]);
        }

        echo "   ✅ Migrated " . count($contacts) . " contacts\n";
    }

    private function migrateContactLists()
    {
        echo "📋 Migrating contact lists...\n";
        
        $stmt = $this->sqliteConnection->query("SELECT * FROM contact_lists");
        $lists = $stmt->fetchAll();
        
        if (empty($lists)) {
            echo "   No contact lists to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO contact_lists (id, user_id, name, description, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        foreach ($lists as $list) {
            $insertStmt->execute([
                $list['id'],
                $list['user_id'],
                $list['name'],
                $list['description'],
                $list['created_at'],
                $list['updated_at']
            ]);
        }

        echo "   ✅ Migrated " . count($lists) . " contact lists\n";
    }

    private function migrateContactListMembers()
    {
        echo "👥 Migrating contact list members...\n";
        
        $stmt = $this->sqliteConnection->query("SELECT * FROM contact_list_members");
        $members = $stmt->fetchAll();
        
        if (empty($members)) {
            echo "   No contact list members to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO contact_list_members (id, contact_list_id, contact_id, created_at) 
            VALUES (?, ?, ?, ?)
        ");

        foreach ($members as $member) {
            $insertStmt->execute([
                $member['id'],
                $member['contact_list_id'],
                $member['contact_id'],
                $member['created_at']
            ]);
        }

        echo "   ✅ Migrated " . count($members) . " contact list members\n";
    }

    private function migrateUploads()
    {
        echo "📁 Migrating uploads...\n";
        
        $stmt = $this->sqliteConnection->query("SELECT * FROM uploads");
        $uploads = $stmt->fetchAll();
        
        if (empty($uploads)) {
            echo "   No uploads to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO uploads (id, user_id, filename, original_name, file_path, file_size, mime_type, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($uploads as $upload) {
            $insertStmt->execute([
                $upload['id'],
                $upload['user_id'],
                $upload['filename'],
                $upload['original_name'],
                $upload['file_path'],
                $upload['file_size'],
                $upload['mime_type'],
                $upload['created_at']
            ]);
        }

        echo "   ✅ Migrated " . count($uploads) . " uploads\n";
    }

    private function migrateCampaigns()
    {
        echo "📢 Migrating campaigns...\n";
        
        $stmt = $this->sqliteConnection->query("SELECT * FROM campaigns");
        $campaigns = $stmt->fetchAll();
        
        if (empty($campaigns)) {
            echo "   No campaigns to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO campaigns (id, user_id, name, subject, template_id, status, scheduled_at, sent_at, 
                                 total_recipients, sent_count, delivered_count, opened_count, clicked_count, 
                                 bounced_count, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($campaigns as $campaign) {
            $insertStmt->execute([
                $campaign['id'],
                $campaign['user_id'],
                $campaign['name'],
                $campaign['subject'],
                $campaign['template_id'],
                $campaign['status'],
                $campaign['scheduled_at'],
                $campaign['sent_at'],
                $campaign['total_recipients'],
                $campaign['sent_count'],
                $campaign['delivered_count'],
                $campaign['opened_count'],
                $campaign['clicked_count'],
                $campaign['bounced_count'],
                $campaign['created_at'],
                $campaign['updated_at']
            ]);
        }

        echo "   ✅ Migrated " . count($campaigns) . " campaigns\n";
    }

    private function migrateCampaignRecipients()
    {
        echo "📨 Migrating campaign recipients...\n";

        $stmt = $this->sqliteConnection->query("SELECT * FROM campaign_recipients");
        $recipients = $stmt->fetchAll();

        if (empty($recipients)) {
            echo "   No campaign recipients to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO campaign_recipients (id, campaign_id, contact_id, status, sent_at, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        foreach ($recipients as $recipient) {
            $insertStmt->execute([
                $recipient['id'],
                $recipient['campaign_id'],
                $recipient['contact_id'],
                $recipient['status'],
                $recipient['sent_at'],
                $recipient['created_at']
            ]);
        }

        echo "   ✅ Migrated " . count($recipients) . " campaign recipients\n";
    }

    private function migrateEmailLogs()
    {
        echo "📊 Migrating email logs...\n";

        $stmt = $this->sqliteConnection->query("SELECT * FROM email_logs");
        $logs = $stmt->fetchAll();

        if (empty($logs)) {
            echo "   No email logs to migrate\n";
            return;
        }

        $insertStmt = $this->mysqlConnection->prepare("
            INSERT INTO email_logs (id, campaign_id, contact_id, status, sent_at, delivered_at,
                                  opened_at, clicked_at, error_message, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($logs as $log) {
            $insertStmt->execute([
                $log['id'],
                $log['campaign_id'],
                $log['contact_id'],
                $log['status'],
                $log['sent_at'],
                $log['delivered_at'],
                $log['opened_at'],
                $log['clicked_at'],
                $log['error_message'],
                $log['created_at'],
                $log['updated_at']
            ]);
        }

        echo "   ✅ Migrated " . count($logs) . " email logs\n";
    }

    private function printMigrationSummary()
    {
        $tables = ['users', 'templates', 'contacts', 'contact_lists', 'contact_list_members',
                  'uploads', 'campaigns', 'campaign_recipients', 'email_logs'];

        foreach ($tables as $table) {
            $stmt = $this->mysqlConnection->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $stmt->fetch()['count'];
            echo "   {$table}: {$count} records\n";
        }
    }
}

// Run the migration
try {
    $migrator = new DatabaseMigrator();
    $migrator->migrate();
} catch (Exception $e) {
    echo "\n💥 Migration Error: " . $e->getMessage() . "\n";
    exit(1);
}
