<?php

namespace App\Controllers;

class DashboardController extends BaseController
{
    public function getStats(): void
    {
        $user = $this->requireAuth();
        
        try {
            // Get total counts
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM templates WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $totalTemplates = $stmt->fetch()['count'];
            
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM contacts WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $totalContacts = $stmt->fetch()['count'];
            
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM campaigns WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $totalCampaigns = $stmt->fetch()['count'];
            
            // Get emails sent and analytics
            $stmt = $this->db->prepare("
                SELECT
                    COALESCE(SUM(sent_count), 0) as total_sent,
                    COALESCE(SUM(delivered_count), 0) as total_delivered,
                    COALESCE(SUM(opened_count), 0) as total_opened,
                    COALESCE(SUM(clicked_count), 0) as total_clicked,
                    COALESCE(SUM(bounced_count), 0) as total_bounced
                FROM campaigns
                WHERE user_id = ? AND status IN ('sent', 'partially_sent')
            ");
            $stmt->execute([$user['id']]);
            $emailStats = $stmt->fetch();

            $emailsSent = (int)$emailStats['total_sent'];
            $emailsDelivered = (int)$emailStats['total_delivered'];
            $emailsOpened = (int)$emailStats['total_opened'];
            $emailsClicked = (int)$emailStats['total_clicked'];
            $emailsBounced = (int)$emailStats['total_bounced'];

            // Calculate rates
            $deliveryRate = $emailsSent > 0 ? round(($emailsDelivered / $emailsSent) * 100, 2) : 0;
            $openRate = $emailsDelivered > 0 ? round(($emailsOpened / $emailsDelivered) * 100, 2) : 0;
            $clickRate = $emailsDelivered > 0 ? round(($emailsClicked / $emailsDelivered) * 100, 2) : 0;
            $bounceRate = $emailsSent > 0 ? round(($emailsBounced / $emailsSent) * 100, 2) : 0;
            
            // Get recent activity
            $recentActivity = $this->getRecentActivity($user['id'], 5);
            
            // Get campaign performance
            $campaignPerformance = $this->getCampaignPerformance($user['id'], 5);
            
            // Get contact growth (mock data for now)
            $contactGrowth = $this->getContactGrowth($user['id'], 30);
            
            $this->successResponse([
                'totalTemplates' => (int)$totalTemplates,
                'totalContacts' => (int)$totalContacts,
                'totalCampaigns' => (int)$totalCampaigns,
                'emailsSent' => $emailsSent,
                'emailsDelivered' => $emailsDelivered,
                'emailsOpened' => $emailsOpened,
                'emailsClicked' => $emailsClicked,
                'emailsBounced' => $emailsBounced,
                'deliveryRate' => $deliveryRate,
                'openRate' => $openRate,
                'clickRate' => $clickRate,
                'bounceRate' => $bounceRate,
                'recentActivity' => $recentActivity,
                'campaignPerformance' => $campaignPerformance,
                'contactGrowth' => $contactGrowth
            ]);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch dashboard stats: ' . $e->getMessage(), 500);
        }
    }
    
    public function getActivity(): void
    {
        $user = $this->requireAuth();
        $limit = (int)($_GET['limit'] ?? 10);
        
        try {
            $activity = $this->getRecentActivity($user['id'], $limit);
            $this->successResponse($activity);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch activity: ' . $e->getMessage(), 500);
        }
    }
    
    public function getCampaigns(): void
    {
        $user = $this->requireAuth();
        $limit = (int)($_GET['limit'] ?? 5);
        
        try {
            $campaigns = $this->getCampaignPerformance($user['id'], $limit);
            $this->successResponse($campaigns);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch campaigns: ' . $e->getMessage(), 500);
        }
    }
    
    public function getContactGrowthData(): void
    {
        $user = $this->requireAuth();
        $days = (int)($_GET['days'] ?? 30);
        
        try {
            $growth = $this->getContactGrowth($user['id'], $days);
            $this->successResponse($growth);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch contact growth: ' . $e->getMessage(), 500);
        }
    }
    
    private function getRecentActivity(int $userId, int $limit): array
    {
        $activity = [];
        
        // Get recent templates
        $stmt = $this->db->prepare("
            SELECT 'template_created' as type, name as title, created_at as timestamp
            FROM templates 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        $templates = $stmt->fetchAll();
        
        foreach ($templates as $template) {
            $activity[] = [
                'id' => 'template_' . uniqid(),
                'type' => 'template_created',
                'title' => 'Template Created',
                'description' => "Created template: {$template['title']}",
                'timestamp' => $template['timestamp'],
                'icon' => 'template'
            ];
        }
        
        // Get recent campaigns
        $stmt = $this->db->prepare("
            SELECT 'campaign_sent' as type, name as title, sent_at as timestamp, sent_count
            FROM campaigns 
            WHERE user_id = ? AND status = 'sent'
            ORDER BY sent_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        $campaigns = $stmt->fetchAll();
        
        foreach ($campaigns as $campaign) {
            $activity[] = [
                'id' => 'campaign_' . uniqid(),
                'type' => 'campaign_sent',
                'title' => 'Campaign Sent',
                'description' => "Sent campaign: {$campaign['title']} to {$campaign['sent_count']} contacts",
                'timestamp' => $campaign['timestamp'],
                'icon' => 'send'
            ];
        }
        
        // Get recent contacts
        $stmt = $this->db->prepare("
            SELECT 'contact_added' as type, name as title, created_at as timestamp
            FROM contacts 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        $contacts = $stmt->fetchAll();
        
        foreach ($contacts as $contact) {
            $activity[] = [
                'id' => 'contact_' . uniqid(),
                'type' => 'contact_added',
                'title' => 'Contact Added',
                'description' => "Added contact: {$contact['title']}",
                'timestamp' => $contact['timestamp'],
                'icon' => 'user'
            ];
        }
        
        // Sort by timestamp and limit
        usort($activity, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        return array_slice($activity, 0, $limit);
    }
    
    private function getCampaignPerformance(int $userId, int $limit): array
    {
        $stmt = $this->db->prepare("
            SELECT id, name, total_recipients as sent, sent_count as delivered, 
                   opened_count as opened, clicked_count as clicked, bounced_count as bounced,
                   sent_at
            FROM campaigns 
            WHERE user_id = ? AND status IN ('sent', 'partially_sent')
            ORDER BY sent_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        $campaigns = $stmt->fetchAll();
        
        $performance = [];
        foreach ($campaigns as $campaign) {
            $sent = (int)$campaign['sent'];
            $opened = (int)$campaign['opened'];
            $clicked = (int)$campaign['clicked'];
            
            $performance[] = [
                'id' => (int)$campaign['id'],
                'name' => $campaign['name'],
                'sent' => $sent,
                'delivered' => (int)$campaign['delivered'],
                'opened' => $opened,
                'clicked' => $clicked,
                'bounced' => (int)$campaign['bounced'],
                'openRate' => $sent > 0 ? round(($opened / $sent) * 100, 2) : 0,
                'clickRate' => $sent > 0 ? round(($clicked / $sent) * 100, 2) : 0,
                'sentAt' => $campaign['sent_at']
            ];
        }
        
        return $performance;
    }
    
    private function getContactGrowth(int $userId, int $days): array
    {
        $growth = [];
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        // Generate daily growth data
        for ($i = $days; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            
            // Get total contacts up to this date
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total,
                       SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                       SUM(CASE WHEN status = 'unsubscribed' THEN 1 ELSE 0 END) as unsubscribed
                FROM contacts 
                WHERE user_id = ? AND DATE(created_at) <= ?
            ");
            $stmt->execute([$userId, $date]);
            $data = $stmt->fetch();
            
            $growth[] = [
                'date' => $date,
                'total' => (int)$data['total'],
                'active' => (int)$data['active'],
                'unsubscribed' => (int)$data['unsubscribed']
            ];
        }
        
        return $growth;
    }
}
