<?php

namespace App\Controllers;

use App\Services\EmailService;

class EmailController extends BaseController
{
    private EmailService $emailService;
    
    public function __construct()
    {
        parent::__construct();
        $this->emailService = new EmailService();
    }
    
    public function sendTestEmail(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        $errors = $this->validateRequired($data, ['email', 'template_name', 'html_content']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        try {
            $success = $this->emailService->sendTestEmail(
                $data['email'],
                $data['template_name'],
                $data['html_content']
            );
            
            if ($success) {
                $this->successResponse(null, 'Test email sent successfully');
            } else {
                $this->errorResponse('Failed to send test email', 500);
            }
        } catch (\Exception $e) {
            $this->errorResponse('Email sending failed: ' . $e->getMessage(), 500);
        }
    }
    
    public function validateEmailConfig(): void
    {
        $user = $this->requireAuth();
        
        try {
            $errors = $this->emailService->validateEmailConfiguration();
            
            if (empty($errors)) {
                $connectionTest = $this->emailService->testConnection();
                
                $this->successResponse([
                    'configuration_valid' => true,
                    'connection_test' => $connectionTest,
                    'message' => $connectionTest ? 'Email configuration is valid and connection successful' : 'Email configuration is valid but connection failed'
                ]);
            } else {
                $this->successResponse([
                    'configuration_valid' => false,
                    'errors' => $errors,
                    'connection_test' => false,
                    'message' => 'Email configuration has errors'
                ]);
            }
        } catch (\Exception $e) {
            $this->errorResponse('Failed to validate email configuration: ' . $e->getMessage(), 500);
        }
    }
    
    public function sendCampaignEmails(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        $errors = $this->validateRequired($data, ['campaign_id']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        try {
            // Get campaign details
            $stmt = $this->db->prepare("
                SELECT c.*, t.html_content, t.name as template_name
                FROM campaigns c
                JOIN templates t ON c.template_id = t.id
                WHERE c.id = ? AND c.user_id = ?
            ");
            $stmt->execute([$data['campaign_id'], $user['id']]);
            $campaign = $stmt->fetch();
            
            if (!$campaign) {
                $this->errorResponse('Campaign not found', 404);
                return;
            }
            
            // Get campaign recipients
            $stmt = $this->db->prepare("
                SELECT cr.*, c.name, c.email
                FROM campaign_recipients cr
                JOIN contacts c ON cr.contact_id = c.id
                WHERE cr.campaign_id = ? AND c.status = 'active'
            ");
            $stmt->execute([$data['campaign_id']]);
            $recipients = $stmt->fetchAll();
            
            if (empty($recipients)) {
                $this->errorResponse('No active recipients found for this campaign', 400);
                return;
            }
            
            // Update campaign status to sending
            $stmt = $this->db->prepare("
                UPDATE campaigns 
                SET status = 'sending', sent_at = datetime('now'), total_recipients = ?
                WHERE id = ?
            ");
            $stmt->execute([count($recipients), $data['campaign_id']]);
            
            $sentCount = 0;
            $failedCount = 0;
            
            foreach ($recipients as $recipient) {
                $personalizations = [
                    'name' => $recipient['name'],
                    'email' => $recipient['email']
                ];
                
                $success = $this->emailService->sendCampaignEmail(
                    $recipient['email'],
                    $campaign['subject'],
                    $campaign['html_content'],
                    $personalizations
                );
                
                if ($success) {
                    $sentCount++;
                    // Update recipient status
                    $stmt = $this->db->prepare("
                        UPDATE campaign_recipients 
                        SET status = 'sent', sent_at = datetime('now')
                        WHERE id = ?
                    ");
                    $stmt->execute([$recipient['id']]);
                } else {
                    $failedCount++;
                    // Update recipient status
                    $stmt = $this->db->prepare("
                        UPDATE campaign_recipients 
                        SET status = 'failed', sent_at = datetime('now')
                        WHERE id = ?
                    ");
                    $stmt->execute([$recipient['id']]);
                }
                
                // Small delay to avoid overwhelming the SMTP server
                usleep(100000); // 0.1 second delay
            }
            
            // Update campaign final status
            $finalStatus = $failedCount === 0 ? 'sent' : ($sentCount === 0 ? 'failed' : 'partially_sent');
            $stmt = $this->db->prepare("
                UPDATE campaigns 
                SET status = ?, sent_count = ?, delivered_count = ?
                WHERE id = ?
            ");
            $stmt->execute([$finalStatus, $sentCount, $sentCount, $data['campaign_id']]);
            
            $this->successResponse([
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'total_recipients' => count($recipients),
                'status' => $finalStatus
            ], 'Campaign emails processed');
            
        } catch (\Exception $e) {
            $this->errorResponse('Failed to send campaign emails: ' . $e->getMessage(), 500);
        }
    }
}
