<?php

namespace App\Controllers;

class TemplateController extends BaseController
{
    public function index(): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM templates
                WHERE user_id = ?
                ORDER BY updated_at DESC
            ");
            $stmt->execute([$user['id']]);
            $templates = $stmt->fetchAll();

            // Decode JSON fields for each template
            foreach ($templates as &$template) {
                if ($template['components']) {
                    $template['components'] = json_decode($template['components'], true);
                }
                if ($template['settings']) {
                    $template['settings'] = json_decode($template['settings'], true);
                }
            }

            $this->successResponse($templates);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch templates', 500);
        }
    }
    
    public function store(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();

        $errors = $this->validateRequired($data, ['name', 'html_content']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO templates (user_id, name, description, html_content, components, settings)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user['id'],
                $data['name'],
                $data['description'] ?? null,
                $data['html_content'],
                isset($data['components']) ? json_encode($data['components']) : null,
                isset($data['settings']) ? json_encode($data['settings']) : null
            ]);
            
            $templateId = $this->db->lastInsertId();
            
            // Get the created template
            $stmt = $this->db->prepare("SELECT * FROM templates WHERE id = ?");
            $stmt->execute([$templateId]);
            $template = $stmt->fetch();

            // Decode JSON fields
            if ($template['components']) {
                $template['components'] = json_decode($template['components'], true);
            }
            if ($template['settings']) {
                $template['settings'] = json_decode($template['settings'], true);
            }

            $this->successResponse($template, 'Template created successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to create template', 500);
        }
    }
    
    public function show(string $id): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM templates 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            $template = $stmt->fetch();
            
            if (!$template) {
                $this->errorResponse('Template not found', 404);
                return;
            }

            // Decode JSON fields
            if ($template['components']) {
                $template['components'] = json_decode($template['components'], true);
            }
            if ($template['settings']) {
                $template['settings'] = json_decode($template['settings'], true);
            }

            $this->successResponse($template);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch template', 500);
        }
    }
    
    public function update(string $id): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        try {
            // Check if template exists and belongs to user
            $stmt = $this->db->prepare("
                SELECT id FROM templates 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            
            if (!$stmt->fetch()) {
                $this->errorResponse('Template not found', 404);
                return;
            }
            
            // Build update query dynamically
            $updateFields = [];
            $updateValues = [];
            
            if (isset($data['name'])) {
                $updateFields[] = 'name = ?';
                $updateValues[] = $data['name'];
            }
            
            if (isset($data['description'])) {
                $updateFields[] = 'description = ?';
                $updateValues[] = $data['description'];
            }
            
            if (isset($data['html_content'])) {
                $updateFields[] = 'html_content = ?';
                $updateValues[] = $data['html_content'];
            }

            if (isset($data['components'])) {
                $updateFields[] = 'components = ?';
                $updateValues[] = json_encode($data['components']);
            }

            if (isset($data['settings'])) {
                $updateFields[] = 'settings = ?';
                $updateValues[] = json_encode($data['settings']);
            }

            if (empty($updateFields)) {
                $this->errorResponse('No fields to update', 400);
                return;
            }
            
            $updateValues[] = $id;
            
            $stmt = $this->db->prepare("
                UPDATE templates 
                SET " . implode(', ', $updateFields) . " 
                WHERE id = ?
            ");
            $stmt->execute($updateValues);
            
            // Get the updated template
            $stmt = $this->db->prepare("SELECT * FROM templates WHERE id = ?");
            $stmt->execute([$id]);
            $template = $stmt->fetch();

            // Decode JSON fields
            if ($template['components']) {
                $template['components'] = json_decode($template['components'], true);
            }
            if ($template['settings']) {
                $template['settings'] = json_decode($template['settings'], true);
            }

            $this->successResponse($template, 'Template updated successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to update template', 500);
        }
    }
    
    public function destroy(string $id): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                DELETE FROM templates 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            
            if ($stmt->rowCount() === 0) {
                $this->errorResponse('Template not found', 404);
                return;
            }
            
            $this->successResponse(null, 'Template deleted successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to delete template', 500);
        }
    }

    public function createPrebuiltTemplates(): void
    {
        $user = $this->requireAuth();

        try {
            $templates = $this->getPrebuiltTemplates();
            $created = [];

            foreach ($templates as $template) {
                // Check if template already exists
                $stmt = $this->db->prepare("
                    SELECT id FROM templates
                    WHERE user_id = ? AND name = ?
                ");
                $stmt->execute([$user['id'], $template['name']]);

                if (!$stmt->fetch()) {
                    $stmt = $this->db->prepare("
                        INSERT INTO templates (user_id, name, description, html_content, thumbnail)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user['id'],
                        $template['name'],
                        $template['description'],
                        $template['html_content'],
                        $template['thumbnail']
                    ]);

                    $created[] = $template['name'];
                }
            }

            $this->successResponse([
                'created_templates' => $created,
                'total_created' => count($created)
            ], count($created) > 0 ? 'Pre-built templates created successfully' : 'All templates already exist');

        } catch (\Exception $e) {
            $this->errorResponse('Failed to create pre-built templates', 500);
        }
    }

    private function getPrebuiltTemplates(): array
    {
        return [
            [
                'name' => 'Welcome Newsletter',
                'description' => 'A warm welcome email for new subscribers',
                'thumbnail' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzI1NjNFQiIvPgo8dGV4dCB4PSIxMDAiIHk9IjQwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5XZWxjb21lPC90ZXh0Pgo8cmVjdCB4PSIyMCIgeT0iNjAiIHdpZHRoPSIxNjAiIGhlaWdodD0iNjAiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHg9IjIwIiB5PSIxMzAiIHdpZHRoPSIxNjAiIGhlaWdodD0iMTAiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',
                'html_content' => '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Our Newsletter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #2563eb; color: white; padding: 40px 20px; text-align: center; }
        .content { padding: 40px 20px; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        h1 { margin: 0; font-size: 28px; }
        h2 { color: #333; font-size: 24px; }
        p { line-height: 1.6; color: #555; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Our Newsletter!</h1>
        </div>
        <div class="content">
            <h2>Hello {{name}},</h2>
            <p>Thank you for subscribing to our newsletter! We\'re excited to have you join our community of engaged readers.</p>
            <p>You can expect to receive:</p>
            <ul>
                <li>Weekly updates on industry trends</li>
                <li>Exclusive content and insights</li>
                <li>Special offers and promotions</li>
                <li>Tips and best practices</li>
            </ul>
            <p>We promise to deliver valuable content directly to your inbox and respect your privacy.</p>
            <a href="#" class="button">Get Started</a>
        </div>
        <div class="footer">
            <p>You received this email because you subscribed to our newsletter.</p>
            <p><a href="#">Unsubscribe</a> | <a href="#">Update Preferences</a></p>
        </div>
    </div>
</body>
</html>'
            ],
            [
                'name' => 'Product Announcement',
                'description' => 'Professional template for product launches and announcements',
                'thumbnail' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzEwQjk4MSIvPgo8dGV4dCB4PSIxMDAiIHk9IjQwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5OZXcgUHJvZHVjdDwvdGV4dD4KPHJlY3QgeD0iMjAiIHk9IjYwIiB3aWR0aD0iMTYwIiBoZWlnaHQ9IjYwIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIyMCIgeT0iMTMwIiB3aWR0aD0iMTYwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjRTVFN0VCIi8+Cjwvc3ZnPg==',
                'html_content' => '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exciting Product Announcement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #10b981; color: white; padding: 40px 20px; text-align: center; }
        .content { padding: 40px 20px; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; background-color: #10b981; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .product-image { width: 100%; max-width: 300px; height: 200px; background-color: #e5e7eb; margin: 20px auto; display: block; border-radius: 8px; }
        h1 { margin: 0; font-size: 32px; }
        h2 { color: #333; font-size: 26px; }
        p { line-height: 1.6; color: #555; }
        .highlight { background-color: #fef3c7; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 New Product Launch!</h1>
        </div>
        <div class="content">
            <h2>Introducing Our Latest Innovation</h2>
            <div class="product-image"></div>
            <p>We\'re thrilled to announce the launch of our newest product that will revolutionize the way you work.</p>
            <div class="highlight">
                <strong>Special Launch Offer:</strong> Get 30% off for the first 100 customers!
            </div>
            <p>Key features include:</p>
            <ul>
                <li>Advanced functionality with intuitive design</li>
                <li>Seamless integration with existing tools</li>
                <li>24/7 customer support</li>
                <li>30-day money-back guarantee</li>
            </ul>
            <p>Don\'t miss out on this limited-time opportunity to be among the first to experience our latest innovation.</p>
            <a href="#" class="button">Shop Now - 30% Off</a>
        </div>
        <div class="footer">
            <p>This offer expires in 7 days. Terms and conditions apply.</p>
            <p><a href="#">Unsubscribe</a> | <a href="#">View in Browser</a></p>
        </div>
    </div>
</body>
</html>'
            ],
            [
                'name' => 'Promotional Sale',
                'description' => 'Eye-catching template for sales and special offers',
                'thumbnail' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIzMCIgZmlsbD0iI0VGNDQ0NCIvPgo8dGV4dCB4PSIxMDAiIHk9IjQwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5TQUxFPC90ZXh0Pgo8cmVjdCB4PSIyMCIgeT0iNjAiIHdpZHRoPSIxNjAiIGhlaWdodD0iNjAiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHg9IjIwIiB5PSIxMzAiIHdpZHRoPSIxNjAiIGhlaWdodD0iMTAiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',
                'html_content' => '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limited Time Sale</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #ef4444; color: white; padding: 40px 20px; text-align: center; }
        .content { padding: 40px 20px; text-align: center; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; background-color: #ef4444; color: white; padding: 18px 36px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; font-size: 18px; }
        .discount { font-size: 48px; font-weight: bold; color: #ef4444; margin: 20px 0; }
        .timer { background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0; }
        h1 { margin: 0; font-size: 36px; }
        h2 { color: #333; font-size: 28px; }
        p { line-height: 1.6; color: #555; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 FLASH SALE 🔥</h1>
        </div>
        <div class="content">
            <h2>Limited Time Offer</h2>
            <div class="discount">50% OFF</div>
            <p>Everything must go! Don\'t miss this incredible opportunity to save big on all our products.</p>
            <div class="timer">
                <strong>⏰ Sale ends in 24 hours!</strong>
            </div>
            <p>Use code: <strong>FLASH50</strong> at checkout</p>
            <a href="#" class="button">Shop Now & Save 50%</a>
            <p><small>*Offer valid for new and existing customers. Cannot be combined with other offers.</small></p>
        </div>
        <div class="footer">
            <p>Hurry! This offer expires soon.</p>
            <p><a href="#">Unsubscribe</a> | <a href="#">Shop Online</a></p>
        </div>
    </div>
</body>
</html>'
            ],
            [
                'name' => 'Event Invitation',
                'description' => 'Professional template for event invitations and announcements',
                'thumbnail' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzc5MzNGRiIvPgo8dGV4dCB4PSIxMDAiIHk9IjQwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5FdmVudDwvdGV4dD4KPHJlY3QgeD0iMjAiIHk9IjYwIiB3aWR0aD0iMTYwIiBoZWlnaHQ9IjYwIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIyMCIgeT0iMTMwIiB3aWR0aD0iMTYwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjRTVFN0VCIi8+Cjwvc3ZnPg==',
                'html_content' => '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You\'re Invited!</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background-color: #7c3aed; color: white; padding: 40px 20px; text-align: center; }
        .content { padding: 40px 20px; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; background-color: #7c3aed; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .event-details { background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .detail-item { margin: 10px 0; }
        .detail-label { font-weight: bold; color: #374151; }
        h1 { margin: 0; font-size: 32px; }
        h2 { color: #333; font-size: 26px; }
        p { line-height: 1.6; color: #555; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 You\'re Invited! 🎉</h1>
        </div>
        <div class="content">
            <h2>Join Us for an Exclusive Event</h2>
            <p>We\'re excited to invite you to our upcoming event. This is a unique opportunity to network, learn, and connect with industry leaders.</p>
            <div class="event-details">
                <div class="detail-item">
                    <span class="detail-label">📅 Date:</span> March 15, 2024
                </div>
                <div class="detail-item">
                    <span class="detail-label">🕐 Time:</span> 6:00 PM - 9:00 PM
                </div>
                <div class="detail-item">
                    <span class="detail-label">📍 Location:</span> Grand Conference Center, Downtown
                </div>
                <div class="detail-item">
                    <span class="detail-label">👔 Dress Code:</span> Business Casual
                </div>
            </div>
            <p>What to expect:</p>
            <ul>
                <li>Keynote presentations from industry experts</li>
                <li>Networking opportunities</li>
                <li>Complimentary refreshments</li>
                <li>Door prizes and giveaways</li>
            </ul>
            <p>Space is limited, so please RSVP as soon as possible.</p>
            <a href="#" class="button">RSVP Now</a>
        </div>
        <div class="footer">
            <p>We look forward to seeing you there!</p>
            <p><a href="#">Add to Calendar</a> | <a href="#">Get Directions</a></p>
        </div>
    </div>
</body>
</html>'
            ]
        ];
    }
}
