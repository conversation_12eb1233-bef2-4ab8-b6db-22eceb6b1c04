import axios from 'axios'
import { TemplateComponent } from '../types/templateEditor'

// Get auth token from localStorage
const getAuthToken = () => {
  const authData = localStorage.getItem('auth_token')
  return authData || null
}

// Create axios instance with auth - use relative URL to leverage Vite proxy
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = getAuthToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

export interface TemplateData {
  id?: number
  name: string
  description?: string
  html_content: string
  components?: TemplateComponent[]
  settings?: {
    width?: number
    backgroundColor?: string
    fontFamily?: string
    preheaderText?: string
  }
  thumbnail?: string
  created_at?: string
  updated_at?: string
}

export interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
}

export const templateEditorApi = {
  // Get all templates
  getTemplates: async (): Promise<TemplateData[]> => {
    const response = await api.get<ApiResponse<TemplateData[]>>('/templates')
    return response.data.data || []
  },

  // Get single template
  getTemplate: async (id: string): Promise<TemplateData> => {
    try {
      const response = await api.get<ApiResponse<TemplateData>>(`/templates/${id}`)
      if (!response.data.data) {
        throw new Error('Template not found')
      }
      return response.data.data
    } catch (error) {
      // Let the component handle the error
      throw error
    }
  },

  // Create new template
  createTemplate: async (templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'>): Promise<TemplateData> => {
    const response = await api.post<ApiResponse<TemplateData>>('/templates', templateData)
    if (!response.data.data) {
      throw new Error('Failed to create template')
    }
    return response.data.data
  },

  // Update existing template
  updateTemplate: async (id: string, templateData: Partial<TemplateData>): Promise<TemplateData> => {
    const response = await api.put<ApiResponse<TemplateData>>(`/templates/${id}`, templateData)
    if (!response.data.data) {
      throw new Error('Failed to update template')
    }
    return response.data.data
  },

  // Delete template
  deleteTemplate: async (id: string): Promise<void> => {
    await api.delete(`/templates/${id}`)
  },

  // Generate HTML from components
  generateHTML: (components: TemplateComponent[]): string => {
    const renderComponent = (component: TemplateComponent): string => {
      const styles = Object.entries(component.styles || {})
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ')

      switch (component.type) {
        case 'text':
          return `<div style="${styles}">${component.content?.text || ''}</div>`

        case 'heading':
          const tag = component.content?.level || 'h2'
          return `<${tag} style="${styles}">${component.content?.text || ''}</${tag}>`

        case 'image':
          return `<img src="${component.content?.src || ''}" alt="${component.content?.alt || ''}" style="${styles}" />`

        case 'button':
          return `<a href="${component.content?.href || '#'}" style="${styles}">${component.content?.text || ''}</a>`

        case 'divider':
          return `<hr style="${styles}" />`

        case 'spacer':
          return `<div style="${styles}"></div>`

        case 'container':
          const childrenHTML = component.children?.map(renderComponent).join('') || ''
          return `<div style="${styles}">${childrenHTML}</div>`

        case 'header':
          return `
            <header style="${styles}">
              ${component.content?.logo ? `<img src="${component.content.logo}" alt="Logo" style="max-height: 40px; margin-bottom: 10px;" />` : ''}
              <h1 style="margin: 0; font-size: 24px;">${component.content?.title || 'Your Company'}</h1>
            </header>
          `

        case 'footer':
          return `
            <footer style="${styles}">
              <p style="margin: 0;">${component.content?.text || '© 2024 Your Company. All rights reserved.'}</p>
              ${component.content?.links ? component.content.links.map((link: any) => 
                `<a href="${link.href}" style="margin-right: 15px; color: inherit;">${link.text}</a>`
              ).join('') : ''}
            </footer>
          `

        case 'product-card':
          return `
            <div style="${styles}">
              ${component.content?.image ? `<img src="${component.content.image}" alt="${component.content?.title || 'Product'}" style="width: 100%; margin-bottom: 10px;" />` : ''}
              <h3 style="margin: 0 0 8px 0; font-size: 18px;">${component.content?.title || 'Product Name'}</h3>
              <p style="margin: 0 0 10px 0; color: #666;">${component.content?.description || 'Product description here...'}</p>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 20px; font-weight: bold;">${component.content?.price || '$99.99'}</span>
                <a href="#" style="background-color: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">${component.content?.buttonText || 'Buy Now'}</a>
              </div>
            </div>
          `

        case 'social-media':
          const platforms = component.content?.platforms || ['facebook', 'twitter', 'instagram']
          return `
            <div style="${styles}">
              <div style="display: flex; justify-content: center; gap: 15px;">
                ${platforms.map((platform: string) => `
                  <a href="#" style="display: inline-block; width: 32px; height: 32px; background-color: ${getSocialColor(platform)}; border-radius: 50%; text-align: center; line-height: 32px; color: white; text-decoration: none;">
                    ${getSocialIcon(platform)}
                  </a>
                `).join('')}
              </div>
            </div>
          `

        default:
          return `<div style="${styles}">Unknown component: ${component.type}</div>`
      }
    }

    const html = components.map(renderComponent).join('')
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Email Template</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto;">
            ${html}
          </div>
        </body>
      </html>
    `
  },

  // Send test email
  sendTestEmail: async (templateData: TemplateData, testEmail: string): Promise<void> => {
    const response = await fetch('/api/email/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({
        email: testEmail,
        template_name: templateData.name,
        html_content: templateData.html_content
      })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to send test email')
    }
  },

  // Export template
  exportTemplate: (templateData: TemplateData, format: 'html' | 'json' = 'html'): void => {
    let content: string
    let filename: string
    let mimeType: string

    if (format === 'html') {
      content = templateData.html_content
      filename = `${templateData.name || 'template'}.html`
      mimeType = 'text/html'
    } else {
      content = JSON.stringify(templateData, null, 2)
      filename = `${templateData.name || 'template'}.json`
      mimeType = 'application/json'
    }

    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

// Helper functions
const getSocialColor = (platform: string): string => {
  const colors: Record<string, string> = {
    facebook: '#1877f2',
    twitter: '#1da1f2',
    instagram: '#e4405f',
    linkedin: '#0077b5',
    youtube: '#ff0000',
    pinterest: '#bd081c'
  }
  return colors[platform] || '#666666'
}

const getSocialIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    facebook: 'f',
    twitter: 't',
    instagram: 'i',
    linkedin: 'in',
    youtube: 'y',
    pinterest: 'p'
  }
  return icons[platform] || '?'
}

