import React, { useState, useEffect } from 'react'
import { X, Upload, Image as ImageIcon, Trash2 } from 'lucide-react'
import { uploadService, UploadedImage } from '../../services/uploadService'

interface ImageUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onImageSelect: (imageUrl: string) => void
}

export const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  isOpen,
  onClose,
  onImageSelect
}) => {
  const [uploads, setUploads] = useState<UploadedImage[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadUploads()
    }
  }, [isOpen])

  const loadUploads = async () => {
    try {
      const uploadedImages = await uploadService.getUploads()
      setUploads(uploadedImages)
    } catch (error) {
      console.error('Failed to load uploads:', error)
    }
  }

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    setIsUploading(true)
    try {
      const uploadPromises = Array.from(files).map(file => uploadService.uploadImage(file))
      const newUploads = await Promise.all(uploadPromises)
      setUploads(prev => [...newUploads, ...prev])
    } catch (error) {
      console.error('Failed to upload images:', error)
      alert('Failed to upload images. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileUpload(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDeleteUpload = async (id: number) => {
    if (!confirm('Are you sure you want to delete this image?')) return

    try {
      await uploadService.deleteUpload(id)
      setUploads(prev => prev.filter(upload => upload.id !== id))
    } catch (error) {
      console.error('Failed to delete upload:', error)
      alert('Failed to delete image. Please try again.')
    }
  }

  const handleImageSelect = (upload: UploadedImage) => {
    onImageSelect(upload.url)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">Insert Image</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center mb-6 transition-colors ${
              dragOver
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              Drop images here or click to upload
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Supports JPEG, PNG, GIF, and WebP (max 5MB each)
            </p>
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
              id="image-upload"
            />
            <label
              htmlFor="image-upload"
              className="btn btn-primary cursor-pointer"
            >
              Choose Files
            </label>
          </div>

          {isUploading && (
            <div className="text-center py-4">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-sm text-gray-600">Uploading images...</p>
            </div>
          )}

          {/* Image Gallery */}
          <div className="max-h-96 overflow-y-auto">
            {uploads.length === 0 ? (
              <div className="text-center py-12">
                <ImageIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No images uploaded yet</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {uploads.map((upload) => (
                  <div
                    key={upload.id}
                    className="relative group border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleImageSelect(upload)}
                  >
                    <img
                      src={upload.url}
                      alt={upload.original_name}
                      className="w-full h-32 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteUpload(upload.id)
                        }}
                        className="opacity-0 group-hover:opacity-100 bg-red-600 text-white p-2 rounded-full hover:bg-red-700 transition-all"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="p-2">
                      <p className="text-xs text-gray-600 truncate">
                        {upload.original_name}
                      </p>
                      <p className="text-xs text-gray-400">
                        {(upload.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="btn btn-outline"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  )
}
