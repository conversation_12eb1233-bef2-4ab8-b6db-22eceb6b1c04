import React, { useRef, useState } from 'react'
import { useDrag } from 'react-dnd'
import { TemplateComponent } from '../../types/templateEditor'
import { ComponentRenderer } from './ComponentRenderer'
import { Move, Trash2 } from 'lucide-react'

interface DraggableComponentProps {
  component: TemplateComponent
  isSelected: boolean
  onSelect: () => void
  onUpdate: (updates: Partial<TemplateComponent>) => void
  onDelete?: (id: string) => void
  zoom: number
}

export const DraggableComponent: React.FC<DraggableComponentProps> = ({
  component,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  zoom
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const [, setIsResizing] = useState(false)
  const [, setResizeHandle] = useState<string | null>(null)

  const [{ isDragging }, drag] = useDrag({
    type: 'existing-component',
    item: {
      type: 'existing-component',
      id: component.id,
      styles: component.styles
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect()
  }

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Enable inline editing for text components
    if (component.type === 'text' || component.type === 'heading') {
      const element = e.target as HTMLElement
      if (element.contentEditable !== 'true') {
        element.contentEditable = 'true'
        element.focus()

        const handleBlur = () => {
          element.contentEditable = 'false'
          onUpdate({
            content: {
              ...component.content,
              text: element.textContent || ''
            }
          })
          element.removeEventListener('blur', handleBlur)
        }

        element.addEventListener('blur', handleBlur)
      }
    }
  }

  const handleResizeStart = (e: React.MouseEvent, handle: string) => {
    e.stopPropagation()
    setIsResizing(true)
    setResizeHandle(handle)

    const startX = e.clientX
    const startY = e.clientY
    const startWidth = parseInt(component.styles?.width || '100')
    const startHeight = parseInt(component.styles?.height || '100')

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY

      let newWidth = startWidth
      let newHeight = startHeight

      if (handle.includes('right')) {
        newWidth = Math.max(20, startWidth + deltaX)
      }
      if (handle.includes('left')) {
        newWidth = Math.max(20, startWidth - deltaX)
      }
      if (handle.includes('bottom')) {
        newHeight = Math.max(20, startHeight + deltaY)
      }
      if (handle.includes('top')) {
        newHeight = Math.max(20, startHeight - deltaY)
      }

      onUpdate({
        styles: {
          ...component.styles,
          width: `${newWidth}px`,
          height: `${newHeight}px`
        }
      })
    }

    const handleMouseUp = () => {
      setIsResizing(false)
      setResizeHandle(null)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (confirm('Are you sure you want to delete this component?')) {
      onDelete?.(component.id)
    }
  }

  drag(ref)

  return (
    <div
      ref={ref}
      className={`absolute cursor-pointer transition-all duration-200 ${
        isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
      } ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: component.position.x,
        top: component.position.y,
        zIndex: component.styles?.zIndex || 1,
        transform: `scale(${zoom})`,
        transformOrigin: 'top left'
      }}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
    >
      <ComponentRenderer 
        component={component} 
        isSelected={isSelected}
        onUpdate={onUpdate}
      />
      
      {isSelected && (
        <>
          {/* Selection outline */}
          <div className="absolute inset-0 border-2 border-blue-500 border-dashed pointer-events-none"></div>

          {/* Resize handles */}
          <div className="absolute inset-0">
            {/* Corner handles */}
            <div
              className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-nw-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'top-left')}
            ></div>
            <div
              className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-ne-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'top-right')}
            ></div>
            <div
              className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-sw-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'bottom-left')}
            ></div>
            <div
              className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-se-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'bottom-right')}
            ></div>

            {/* Edge handles */}
            <div
              className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-n-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'top')}
            ></div>
            <div
              className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-s-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'bottom')}
            ></div>
            <div
              className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-w-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'left')}
            ></div>
            <div
              className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-e-resize pointer-events-auto"
              onMouseDown={(e) => handleResizeStart(e, 'right')}
            ></div>
          </div>

          {/* Control toolbar */}
          <div className="absolute -top-8 left-0 flex space-x-1 pointer-events-auto">
            <button
              className="p-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
              title="Move"
            >
              <Move className="w-3 h-3" />
            </button>
            <button
              onClick={handleDelete}
              className="p-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
              title="Delete"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        </>
      )}
    </div>
  )
}
