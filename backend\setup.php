<?php

// Simple autoloader
spl_autoload_register(function ($class) {
    $prefix = 'App\\';
    $base_dir = __DIR__ . '/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

    if (file_exists($file)) {
        require $file;
    }
});

use App\Database;

try {
    echo "Setting up database tables...\n";
    Database::createTables();
    echo "Database setup completed successfully!\n";

    // Create a demo user
    $pdo = Database::getConnection();

    // Check if demo user already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);

    if (!$stmt->fetch()) {
        echo "Creating demo user...\n";
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
        $stmt->execute([
            'Demo User',
            '<EMAIL>',
            password_hash('password', PASSWORD_DEFAULT)
        ]);
        echo "Demo user created: <EMAIL> / password\n";
    } else {
        echo "Demo user already exists: <EMAIL> / password\n";
    }

    // Create sample template
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();

    if ($user) {
        $stmt = $pdo->prepare("SELECT id FROM templates WHERE user_id = ? AND name = ?");
        $stmt->execute([$user['id'], 'Welcome Newsletter']);

        if (!$stmt->fetch()) {
            echo "Creating sample template...\n";
            $sampleComponents = [
                [
                    'id' => 'header-1',
                    'type' => 'header',
                    'content' => [
                        'title' => 'Welcome to Our Newsletter',
                        'logo' => 'https://via.placeholder.com/120x40'
                    ],
                    'styles' => [
                        'backgroundColor' => '#2563eb',
                        'color' => '#ffffff',
                        'padding' => '20px',
                        'textAlign' => 'center'
                    ],
                    'position' => ['x' => 0, 'y' => 0]
                ],
                [
                    'id' => 'text-1',
                    'type' => 'text',
                    'content' => [
                        'text' => 'Thank you for subscribing to our newsletter! We\'re excited to share the latest updates with you.'
                    ],
                    'styles' => [
                        'fontSize' => '16px',
                        'color' => '#374151',
                        'padding' => '20px',
                        'lineHeight' => '1.6'
                    ],
                    'position' => ['x' => 0, 'y' => 100]
                ]
            ];

            $stmt = $pdo->prepare("
                INSERT INTO templates (user_id, name, description, html_content, components)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user['id'],
                'Welcome Newsletter',
                'A sample welcome newsletter template',
                '<div>Welcome Newsletter Template</div>',
                json_encode($sampleComponents)
            ]);
            echo "Sample template created\n";
        }
    }

    echo "\nSetup completed successfully!\n";
    echo "You can now:\n";
    echo "1. Start the backend server: php -S localhost:8000 -t public\n";
    echo "2. Start the frontend: cd frontend && npm run dev\n";
    echo "3. Login with: <EMAIL> / password\n";

} catch (Exception $e) {
    echo "Setup failed: " . $e->getMessage() . "\n";
    exit(1);
}
