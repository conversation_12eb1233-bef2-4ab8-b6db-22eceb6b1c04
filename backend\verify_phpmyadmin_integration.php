<?php

// Load environment variables manually
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_NAME'] ?? 'email_marketing';
$username = $_ENV['DB_USER'] ?? 'root';
$password = $_ENV['DB_PASS'] ?? '';

echo "🔍 phpMyAdmin Integration Verification\n";
echo "=====================================\n\n";

echo "📋 Database Configuration:\n";
echo "   Host: {$host}\n";
echo "   Database: {$dbname}\n";
echo "   Username: {$username}\n";
echo "   Password: " . (empty($password) ? '(empty)' : '(set)') . "\n\n";

try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    echo "✅ MySQL Connection Successful!\n\n";

    // Get database info
    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as mysql_version");
    $info = $stmt->fetch();
    echo "📊 Database Information:\n";
    echo "   Current Database: {$info['current_db']}\n";
    echo "   MySQL Version: {$info['mysql_version']}\n\n";

    // List all tables with record counts
    echo "📋 Tables and Record Counts:\n";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $stmt->fetch()['count'];
        echo "   {$table}: {$count} records\n";
    }

    echo "\n📄 Sample Data from Key Tables:\n";
    
    // Sample users
    echo "\n👥 Users:\n";
    $stmt = $pdo->query("SELECT id, name, email, created_at FROM users LIMIT 3");
    $users = $stmt->fetchAll();
    foreach ($users as $user) {
        echo "   ID: {$user['id']} | {$user['name']} | {$user['email']} | {$user['created_at']}\n";
    }

    // Sample templates
    echo "\n📄 Templates:\n";
    $stmt = $pdo->query("SELECT id, name, user_id, created_at FROM templates LIMIT 3");
    $templates = $stmt->fetchAll();
    foreach ($templates as $template) {
        echo "   ID: {$template['id']} | {$template['name']} | User: {$template['user_id']} | {$template['created_at']}\n";
    }

    // Sample contacts
    echo "\n📧 Contacts:\n";
    $stmt = $pdo->query("SELECT id, name, email, status, created_at FROM contacts LIMIT 3");
    $contacts = $stmt->fetchAll();
    foreach ($contacts as $contact) {
        echo "   ID: {$contact['id']} | {$contact['name']} | {$contact['email']} | {$contact['status']} | {$contact['created_at']}\n";
    }

    echo "\n🎯 phpMyAdmin Access Instructions:\n";
    echo "=====================================\n";
    echo "1. Open phpMyAdmin in your browser (usually http://localhost/phpmyadmin)\n";
    echo "2. Login with:\n";
    echo "   - Username: {$username}\n";
    echo "   - Password: " . (empty($password) ? '(leave empty)' : '(your MySQL password)') . "\n";
    echo "3. Select the '{$dbname}' database from the left sidebar\n";
    echo "4. You should see all the tables listed above\n";
    echo "5. Click on any table to view/edit the data\n\n";

    echo "✅ All data is accessible through phpMyAdmin!\n";
    echo "✅ You can now manage your email application database through phpMyAdmin!\n";

} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "\n🔧 Troubleshooting:\n";
    echo "1. Make sure MySQL server is running\n";
    echo "2. Verify database credentials in .env file\n";
    echo "3. Check if the database '{$dbname}' exists\n";
    echo "4. Ensure MySQL user '{$username}' has proper permissions\n";
}
