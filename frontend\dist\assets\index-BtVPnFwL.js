var px=Object.defineProperty;var gx=(n,r,s)=>r in n?px(n,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[r]=s;var _m=(n,r,s)=>gx(n,typeof r!="symbol"?r+"":r,s);(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))c(u);new MutationObserver(u=>{for(const f of u)if(f.type==="childList")for(const h of f.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&c(h)}).observe(document,{childList:!0,subtree:!0});function s(u){const f={};return u.integrity&&(f.integrity=u.integrity),u.referrerPolicy&&(f.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?f.credentials="include":u.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function c(u){if(u.ep)return;u.ep=!0;const f=s(u);fetch(u.href,f)}})();function fu(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var _o={exports:{}},sr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Am;function yx(){if(Am)return sr;Am=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function s(c,u,f){var h=null;if(f!==void 0&&(h=""+f),u.key!==void 0&&(h=""+u.key),"key"in u){f={};for(var g in u)g!=="key"&&(f[g]=u[g])}else f=u;return u=f.ref,{$$typeof:n,type:c,key:h,ref:u!==void 0?u:null,props:f}}return sr.Fragment=r,sr.jsx=s,sr.jsxs=s,sr}var Mm;function xx(){return Mm||(Mm=1,_o.exports=yx()),_o.exports}var i=xx(),Ao={exports:{}},me={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km;function vx(){if(km)return me;km=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),w=Symbol.iterator;function b(D){return D===null||typeof D!="object"?null:(D=w&&D[w]||D["@@iterator"],typeof D=="function"?D:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,C={};function S(D,X,W){this.props=D,this.context=X,this.refs=C,this.updater=W||R}S.prototype.isReactComponent={},S.prototype.setState=function(D,X){if(typeof D!="object"&&typeof D!="function"&&D!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,D,X,"setState")},S.prototype.forceUpdate=function(D){this.updater.enqueueForceUpdate(this,D,"forceUpdate")};function E(){}E.prototype=S.prototype;function k(D,X,W){this.props=D,this.context=X,this.refs=C,this.updater=W||R}var $=k.prototype=new E;$.constructor=k,T($,S.prototype),$.isPureReactComponent=!0;var z=Array.isArray,Y={H:null,A:null,T:null,S:null,V:null},Z=Object.prototype.hasOwnProperty;function se(D,X,W,F,P,ne){return W=ne.ref,{$$typeof:n,type:D,key:X,ref:W!==void 0?W:null,props:ne}}function Q(D,X){return se(D.type,X,void 0,void 0,void 0,D.props)}function I(D){return typeof D=="object"&&D!==null&&D.$$typeof===n}function oe(D){var X={"=":"=0",":":"=2"};return"$"+D.replace(/[=:]/g,function(W){return X[W]})}var ve=/\/+/g;function Re(D,X){return typeof D=="object"&&D!==null&&D.key!=null?oe(""+D.key):X.toString(36)}function te(){}function J(D){switch(D.status){case"fulfilled":return D.value;case"rejected":throw D.reason;default:switch(typeof D.status=="string"?D.then(te,te):(D.status="pending",D.then(function(X){D.status==="pending"&&(D.status="fulfilled",D.value=X)},function(X){D.status==="pending"&&(D.status="rejected",D.reason=X)})),D.status){case"fulfilled":return D.value;case"rejected":throw D.reason}}throw D}function be(D,X,W,F,P){var ne=typeof D;(ne==="undefined"||ne==="boolean")&&(D=null);var ee=!1;if(D===null)ee=!0;else switch(ne){case"bigint":case"string":case"number":ee=!0;break;case"object":switch(D.$$typeof){case n:case r:ee=!0;break;case x:return ee=D._init,be(ee(D._payload),X,W,F,P)}}if(ee)return P=P(D),ee=F===""?"."+Re(D,0):F,z(P)?(W="",ee!=null&&(W=ee.replace(ve,"$&/")+"/"),be(P,X,W,"",function($t){return $t})):P!=null&&(I(P)&&(P=Q(P,W+(P.key==null||D&&D.key===P.key?"":(""+P.key).replace(ve,"$&/")+"/")+ee)),X.push(P)),1;ee=0;var et=F===""?".":F+":";if(z(D))for(var _e=0;_e<D.length;_e++)F=D[_e],ne=et+Re(F,_e),ee+=be(F,X,W,ne,P);else if(_e=b(D),typeof _e=="function")for(D=_e.call(D),_e=0;!(F=D.next()).done;)F=F.value,ne=et+Re(F,_e++),ee+=be(F,X,W,ne,P);else if(ne==="object"){if(typeof D.then=="function")return be(J(D),X,W,F,P);throw X=String(D),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(D).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return ee}function B(D,X,W){if(D==null)return D;var F=[],P=0;return be(D,F,"","",function(ne){return X.call(W,ne,P++)}),F}function K(D){if(D._status===-1){var X=D._result;X=X(),X.then(function(W){(D._status===0||D._status===-1)&&(D._status=1,D._result=W)},function(W){(D._status===0||D._status===-1)&&(D._status=2,D._result=W)}),D._status===-1&&(D._status=0,D._result=X)}if(D._status===1)return D._result.default;throw D._result}var ie=typeof reportError=="function"?reportError:function(D){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof D=="object"&&D!==null&&typeof D.message=="string"?String(D.message):String(D),error:D});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",D);return}console.error(D)};function je(){}return me.Children={map:B,forEach:function(D,X,W){B(D,function(){X.apply(this,arguments)},W)},count:function(D){var X=0;return B(D,function(){X++}),X},toArray:function(D){return B(D,function(X){return X})||[]},only:function(D){if(!I(D))throw Error("React.Children.only expected to receive a single React element child.");return D}},me.Component=S,me.Fragment=s,me.Profiler=u,me.PureComponent=k,me.StrictMode=c,me.Suspense=y,me.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Y,me.__COMPILER_RUNTIME={__proto__:null,c:function(D){return Y.H.useMemoCache(D)}},me.cache=function(D){return function(){return D.apply(null,arguments)}},me.cloneElement=function(D,X,W){if(D==null)throw Error("The argument must be a React element, but you passed "+D+".");var F=T({},D.props),P=D.key,ne=void 0;if(X!=null)for(ee in X.ref!==void 0&&(ne=void 0),X.key!==void 0&&(P=""+X.key),X)!Z.call(X,ee)||ee==="key"||ee==="__self"||ee==="__source"||ee==="ref"&&X.ref===void 0||(F[ee]=X[ee]);var ee=arguments.length-2;if(ee===1)F.children=W;else if(1<ee){for(var et=Array(ee),_e=0;_e<ee;_e++)et[_e]=arguments[_e+2];F.children=et}return se(D.type,P,void 0,void 0,ne,F)},me.createContext=function(D){return D={$$typeof:h,_currentValue:D,_currentValue2:D,_threadCount:0,Provider:null,Consumer:null},D.Provider=D,D.Consumer={$$typeof:f,_context:D},D},me.createElement=function(D,X,W){var F,P={},ne=null;if(X!=null)for(F in X.key!==void 0&&(ne=""+X.key),X)Z.call(X,F)&&F!=="key"&&F!=="__self"&&F!=="__source"&&(P[F]=X[F]);var ee=arguments.length-2;if(ee===1)P.children=W;else if(1<ee){for(var et=Array(ee),_e=0;_e<ee;_e++)et[_e]=arguments[_e+2];P.children=et}if(D&&D.defaultProps)for(F in ee=D.defaultProps,ee)P[F]===void 0&&(P[F]=ee[F]);return se(D,ne,void 0,void 0,null,P)},me.createRef=function(){return{current:null}},me.forwardRef=function(D){return{$$typeof:g,render:D}},me.isValidElement=I,me.lazy=function(D){return{$$typeof:x,_payload:{_status:-1,_result:D},_init:K}},me.memo=function(D,X){return{$$typeof:p,type:D,compare:X===void 0?null:X}},me.startTransition=function(D){var X=Y.T,W={};Y.T=W;try{var F=D(),P=Y.S;P!==null&&P(W,F),typeof F=="object"&&F!==null&&typeof F.then=="function"&&F.then(je,ie)}catch(ne){ie(ne)}finally{Y.T=X}},me.unstable_useCacheRefresh=function(){return Y.H.useCacheRefresh()},me.use=function(D){return Y.H.use(D)},me.useActionState=function(D,X,W){return Y.H.useActionState(D,X,W)},me.useCallback=function(D,X){return Y.H.useCallback(D,X)},me.useContext=function(D){return Y.H.useContext(D)},me.useDebugValue=function(){},me.useDeferredValue=function(D,X){return Y.H.useDeferredValue(D,X)},me.useEffect=function(D,X,W){var F=Y.H;if(typeof W=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return F.useEffect(D,X)},me.useId=function(){return Y.H.useId()},me.useImperativeHandle=function(D,X,W){return Y.H.useImperativeHandle(D,X,W)},me.useInsertionEffect=function(D,X){return Y.H.useInsertionEffect(D,X)},me.useLayoutEffect=function(D,X){return Y.H.useLayoutEffect(D,X)},me.useMemo=function(D,X){return Y.H.useMemo(D,X)},me.useOptimistic=function(D,X){return Y.H.useOptimistic(D,X)},me.useReducer=function(D,X,W){return Y.H.useReducer(D,X,W)},me.useRef=function(D){return Y.H.useRef(D)},me.useState=function(D){return Y.H.useState(D)},me.useSyncExternalStore=function(D,X,W){return Y.H.useSyncExternalStore(D,X,W)},me.useTransition=function(){return Y.H.useTransition()},me.version="19.1.0",me}var Um;function hu(){return Um||(Um=1,Ao.exports=vx()),Ao.exports}var N=hu();const Op=fu(N);var Mo={exports:{}},ir={},ko={exports:{}},Uo={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zm;function bx(){return zm||(zm=1,function(n){function r(B,K){var ie=B.length;B.push(K);e:for(;0<ie;){var je=ie-1>>>1,D=B[je];if(0<u(D,K))B[je]=K,B[ie]=D,ie=je;else break e}}function s(B){return B.length===0?null:B[0]}function c(B){if(B.length===0)return null;var K=B[0],ie=B.pop();if(ie!==K){B[0]=ie;e:for(var je=0,D=B.length,X=D>>>1;je<X;){var W=2*(je+1)-1,F=B[W],P=W+1,ne=B[P];if(0>u(F,ie))P<D&&0>u(ne,F)?(B[je]=ne,B[P]=ie,je=P):(B[je]=F,B[W]=ie,je=W);else if(P<D&&0>u(ne,ie))B[je]=ne,B[P]=ie,je=P;else break e}}return K}function u(B,K){var ie=B.sortIndex-K.sortIndex;return ie!==0?ie:B.id-K.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var h=Date,g=h.now();n.unstable_now=function(){return h.now()-g}}var y=[],p=[],x=1,w=null,b=3,R=!1,T=!1,C=!1,S=!1,E=typeof setTimeout=="function"?setTimeout:null,k=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;function z(B){for(var K=s(p);K!==null;){if(K.callback===null)c(p);else if(K.startTime<=B)c(p),K.sortIndex=K.expirationTime,r(y,K);else break;K=s(p)}}function Y(B){if(C=!1,z(B),!T)if(s(y)!==null)T=!0,Z||(Z=!0,Re());else{var K=s(p);K!==null&&be(Y,K.startTime-B)}}var Z=!1,se=-1,Q=5,I=-1;function oe(){return S?!0:!(n.unstable_now()-I<Q)}function ve(){if(S=!1,Z){var B=n.unstable_now();I=B;var K=!0;try{e:{T=!1,C&&(C=!1,k(se),se=-1),R=!0;var ie=b;try{t:{for(z(B),w=s(y);w!==null&&!(w.expirationTime>B&&oe());){var je=w.callback;if(typeof je=="function"){w.callback=null,b=w.priorityLevel;var D=je(w.expirationTime<=B);if(B=n.unstable_now(),typeof D=="function"){w.callback=D,z(B),K=!0;break t}w===s(y)&&c(y),z(B)}else c(y);w=s(y)}if(w!==null)K=!0;else{var X=s(p);X!==null&&be(Y,X.startTime-B),K=!1}}break e}finally{w=null,b=ie,R=!1}K=void 0}}finally{K?Re():Z=!1}}}var Re;if(typeof $=="function")Re=function(){$(ve)};else if(typeof MessageChannel<"u"){var te=new MessageChannel,J=te.port2;te.port1.onmessage=ve,Re=function(){J.postMessage(null)}}else Re=function(){E(ve,0)};function be(B,K){se=E(function(){B(n.unstable_now())},K)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Q=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return b},n.unstable_next=function(B){switch(b){case 1:case 2:case 3:var K=3;break;default:K=b}var ie=b;b=K;try{return B()}finally{b=ie}},n.unstable_requestPaint=function(){S=!0},n.unstable_runWithPriority=function(B,K){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var ie=b;b=B;try{return K()}finally{b=ie}},n.unstable_scheduleCallback=function(B,K,ie){var je=n.unstable_now();switch(typeof ie=="object"&&ie!==null?(ie=ie.delay,ie=typeof ie=="number"&&0<ie?je+ie:je):ie=je,B){case 1:var D=-1;break;case 2:D=250;break;case 5:D=1073741823;break;case 4:D=1e4;break;default:D=5e3}return D=ie+D,B={id:x++,callback:K,priorityLevel:B,startTime:ie,expirationTime:D,sortIndex:-1},ie>je?(B.sortIndex=ie,r(p,B),s(y)===null&&B===s(p)&&(C?(k(se),se=-1):C=!0,be(Y,ie-je))):(B.sortIndex=D,r(y,B),T||R||(T=!0,Z||(Z=!0,Re()))),B},n.unstable_shouldYield=oe,n.unstable_wrapCallback=function(B){var K=b;return function(){var ie=b;b=K;try{return B.apply(this,arguments)}finally{b=ie}}}}(Uo)),Uo}var Lm;function Nx(){return Lm||(Lm=1,ko.exports=bx()),ko.exports}var zo={exports:{}},nt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hm;function wx(){if(Hm)return nt;Hm=1;var n=hu();function r(y){var p="https://react.dev/errors/"+y;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)p+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+y+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(r(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},u=Symbol.for("react.portal");function f(y,p,x){var w=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:w==null?null:""+w,children:y,containerInfo:p,implementation:x}}var h=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,p){if(y==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return nt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,nt.createPortal=function(y,p){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(r(299));return f(y,p,null,x)},nt.flushSync=function(y){var p=h.T,x=c.p;try{if(h.T=null,c.p=2,y)return y()}finally{h.T=p,c.p=x,c.d.f()}},nt.preconnect=function(y,p){typeof y=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,c.d.C(y,p))},nt.prefetchDNS=function(y){typeof y=="string"&&c.d.D(y)},nt.preinit=function(y,p){if(typeof y=="string"&&p&&typeof p.as=="string"){var x=p.as,w=g(x,p.crossOrigin),b=typeof p.integrity=="string"?p.integrity:void 0,R=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;x==="style"?c.d.S(y,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:w,integrity:b,fetchPriority:R}):x==="script"&&c.d.X(y,{crossOrigin:w,integrity:b,fetchPriority:R,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},nt.preinitModule=function(y,p){if(typeof y=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var x=g(p.as,p.crossOrigin);c.d.M(y,{crossOrigin:x,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&c.d.M(y)},nt.preload=function(y,p){if(typeof y=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var x=p.as,w=g(x,p.crossOrigin);c.d.L(y,x,{crossOrigin:w,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},nt.preloadModule=function(y,p){if(typeof y=="string")if(p){var x=g(p.as,p.crossOrigin);c.d.m(y,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:x,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else c.d.m(y)},nt.requestFormReset=function(y){c.d.r(y)},nt.unstable_batchedUpdates=function(y,p){return y(p)},nt.useFormState=function(y,p,x){return h.H.useFormState(y,p,x)},nt.useFormStatus=function(){return h.H.useHostTransitionStatus()},nt.version="19.1.0",nt}var Bm;function Sx(){if(Bm)return zo.exports;Bm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),zo.exports=wx(),zo.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm;function jx(){if(qm)return ir;qm=1;var n=Nx(),r=hu(),s=Sx();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(f(e)!==e)throw Error(c(188))}function y(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(c(188));return t!==e?null:e}for(var a=e,l=t;;){var o=a.return;if(o===null)break;var d=o.alternate;if(d===null){if(l=o.return,l!==null){a=l;continue}break}if(o.child===d.child){for(d=o.child;d;){if(d===a)return g(o),e;if(d===l)return g(o),t;d=d.sibling}throw Error(c(188))}if(a.return!==l.return)a=o,l=d;else{for(var m=!1,v=o.child;v;){if(v===a){m=!0,a=o,l=d;break}if(v===l){m=!0,l=o,a=d;break}v=v.sibling}if(!m){for(v=d.child;v;){if(v===a){m=!0,a=d,l=o;break}if(v===l){m=!0,l=d,a=o;break}v=v.sibling}if(!m)throw Error(c(189))}}if(a.alternate!==l)throw Error(c(190))}if(a.tag!==3)throw Error(c(188));return a.stateNode.current===a?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,w=Symbol.for("react.element"),b=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),k=Symbol.for("react.consumer"),$=Symbol.for("react.context"),z=Symbol.for("react.forward_ref"),Y=Symbol.for("react.suspense"),Z=Symbol.for("react.suspense_list"),se=Symbol.for("react.memo"),Q=Symbol.for("react.lazy"),I=Symbol.for("react.activity"),oe=Symbol.for("react.memo_cache_sentinel"),ve=Symbol.iterator;function Re(e){return e===null||typeof e!="object"?null:(e=ve&&e[ve]||e["@@iterator"],typeof e=="function"?e:null)}var te=Symbol.for("react.client.reference");function J(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===te?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case T:return"Fragment";case S:return"Profiler";case C:return"StrictMode";case Y:return"Suspense";case Z:return"SuspenseList";case I:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case R:return"Portal";case $:return(e.displayName||"Context")+".Provider";case k:return(e._context.displayName||"Context")+".Consumer";case z:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case se:return t=e.displayName||null,t!==null?t:J(e.type)||"Memo";case Q:t=e._payload,e=e._init;try{return J(e(t))}catch{}}return null}var be=Array.isArray,B=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ie={pending:!1,data:null,method:null,action:null},je=[],D=-1;function X(e){return{current:e}}function W(e){0>D||(e.current=je[D],je[D]=null,D--)}function F(e,t){D++,je[D]=e.current,e.current=t}var P=X(null),ne=X(null),ee=X(null),et=X(null);function _e(e,t){switch(F(ee,t),F(ne,e),F(P,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?rm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=rm(t),e=sm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}W(P),F(P,e)}function $t(){W(P),W(ne),W(ee)}function cl(e){e.memoizedState!==null&&F(et,e);var t=P.current,a=sm(t,e.type);t!==a&&(F(ne,e),F(P,a))}function Ga(e){ne.current===e&&(W(P),W(ne)),et.current===e&&(W(et),tr._currentValue=ie)}var gi=Object.prototype.hasOwnProperty,yi=n.unstable_scheduleCallback,xi=n.unstable_cancelCallback,Qg=n.unstable_shouldYield,Zg=n.unstable_requestPaint,Vt=n.unstable_now,Kg=n.unstable_getCurrentPriorityLevel,Hu=n.unstable_ImmediatePriority,Bu=n.unstable_UserBlockingPriority,Er=n.unstable_NormalPriority,Ig=n.unstable_LowPriority,qu=n.unstable_IdlePriority,Fg=n.log,Jg=n.unstable_setDisableYieldValue,ol=null,pt=null;function ga(e){if(typeof Fg=="function"&&Jg(e),pt&&typeof pt.setStrictMode=="function")try{pt.setStrictMode(ol,e)}catch{}}var gt=Math.clz32?Math.clz32:t0,Wg=Math.log,e0=Math.LN2;function t0(e){return e>>>=0,e===0?32:31-(Wg(e)/e0|0)|0}var Tr=256,Cr=4194304;function $a(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Dr(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var o=0,d=e.suspendedLanes,m=e.pingedLanes;e=e.warmLanes;var v=l&134217727;return v!==0?(l=v&~d,l!==0?o=$a(l):(m&=v,m!==0?o=$a(m):a||(a=v&~e,a!==0&&(o=$a(a))))):(v=l&~d,v!==0?o=$a(v):m!==0?o=$a(m):a||(a=l&~e,a!==0&&(o=$a(a)))),o===0?0:t!==0&&t!==o&&(t&d)===0&&(d=o&-o,a=t&-t,d>=a||d===32&&(a&4194048)!==0)?t:o}function ul(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function a0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Yu(){var e=Tr;return Tr<<=1,(Tr&4194048)===0&&(Tr=256),e}function Gu(){var e=Cr;return Cr<<=1,(Cr&62914560)===0&&(Cr=4194304),e}function vi(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function dl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function n0(e,t,a,l,o,d){var m=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var v=e.entanglements,j=e.expirationTimes,M=e.hiddenUpdates;for(a=m&~a;0<a;){var q=31-gt(a),V=1<<q;v[q]=0,j[q]=-1;var U=M[q];if(U!==null)for(M[q]=null,q=0;q<U.length;q++){var L=U[q];L!==null&&(L.lane&=-536870913)}a&=~V}l!==0&&$u(e,l,0),d!==0&&o===0&&e.tag!==0&&(e.suspendedLanes|=d&~(m&~t))}function $u(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-gt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Vu(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-gt(a),o=1<<l;o&t|e[l]&t&&(e[l]|=t),a&=~o}}function bi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ni(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Xu(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:Em(e.type))}function l0(e,t){var a=K.p;try{return K.p=e,t()}finally{K.p=a}}var ya=Math.random().toString(36).slice(2),tt="__reactFiber$"+ya,it="__reactProps$"+ya,mn="__reactContainer$"+ya,wi="__reactEvents$"+ya,r0="__reactListeners$"+ya,s0="__reactHandles$"+ya,Pu="__reactResources$"+ya,fl="__reactMarker$"+ya;function Si(e){delete e[tt],delete e[it],delete e[wi],delete e[r0],delete e[s0]}function pn(e){var t=e[tt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[mn]||a[tt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=um(e);e!==null;){if(a=e[tt])return a;e=um(e)}return t}e=a,a=e.parentNode}return null}function gn(e){if(e=e[tt]||e[mn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function hl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function yn(e){var t=e[Pu];return t||(t=e[Pu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Qe(e){e[fl]=!0}var Qu=new Set,Zu={};function Va(e,t){xn(e,t),xn(e+"Capture",t)}function xn(e,t){for(Zu[e]=t,e=0;e<t.length;e++)Qu.add(t[e])}var i0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ku={},Iu={};function c0(e){return gi.call(Iu,e)?!0:gi.call(Ku,e)?!1:i0.test(e)?Iu[e]=!0:(Ku[e]=!0,!1)}function Or(e,t,a){if(c0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Rr(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Ft(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var ji,Fu;function vn(e){if(ji===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);ji=t&&t[1]||"",Fu=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ji+e+Fu}var Ei=!1;function Ti(e,t){if(!e||Ei)return"";Ei=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(L){var U=L}Reflect.construct(e,[],V)}else{try{V.call()}catch(L){U=L}e.call(V.prototype)}}else{try{throw Error()}catch(L){U=L}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(L){if(L&&U&&typeof L.stack=="string")return[L.stack,U.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var d=l.DetermineComponentFrameRoot(),m=d[0],v=d[1];if(m&&v){var j=m.split(`
`),M=v.split(`
`);for(o=l=0;l<j.length&&!j[l].includes("DetermineComponentFrameRoot");)l++;for(;o<M.length&&!M[o].includes("DetermineComponentFrameRoot");)o++;if(l===j.length||o===M.length)for(l=j.length-1,o=M.length-1;1<=l&&0<=o&&j[l]!==M[o];)o--;for(;1<=l&&0<=o;l--,o--)if(j[l]!==M[o]){if(l!==1||o!==1)do if(l--,o--,0>o||j[l]!==M[o]){var q=`
`+j[l].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=l&&0<=o);break}}}finally{Ei=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?vn(a):""}function o0(e){switch(e.tag){case 26:case 27:case 5:return vn(e.type);case 16:return vn("Lazy");case 13:return vn("Suspense");case 19:return vn("SuspenseList");case 0:case 15:return Ti(e.type,!1);case 11:return Ti(e.type.render,!1);case 1:return Ti(e.type,!0);case 31:return vn("Activity");default:return""}}function Ju(e){try{var t="";do t+=o0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Et(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Wu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function u0(e){var t=Wu(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var o=a.get,d=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(m){l=""+m,d.call(this,m)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(m){l=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _r(e){e._valueTracker||(e._valueTracker=u0(e))}function ed(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Wu(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Ar(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var d0=/[\n"\\]/g;function Tt(e){return e.replace(d0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ci(e,t,a,l,o,d,m,v){e.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.type=m:e.removeAttribute("type"),t!=null?m==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Et(t)):e.value!==""+Et(t)&&(e.value=""+Et(t)):m!=="submit"&&m!=="reset"||e.removeAttribute("value"),t!=null?Di(e,m,Et(t)):a!=null?Di(e,m,Et(a)):l!=null&&e.removeAttribute("value"),o==null&&d!=null&&(e.defaultChecked=!!d),o!=null&&(e.checked=o&&typeof o!="function"&&typeof o!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?e.name=""+Et(v):e.removeAttribute("name")}function td(e,t,a,l,o,d,m,v){if(d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.type=d),t!=null||a!=null){if(!(d!=="submit"&&d!=="reset"||t!=null))return;a=a!=null?""+Et(a):"",t=t!=null?""+Et(t):a,v||t===e.value||(e.value=t),e.defaultValue=t}l=l??o,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=v?e.checked:!!l,e.defaultChecked=!!l,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(e.name=m)}function Di(e,t,a){t==="number"&&Ar(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function bn(e,t,a,l){if(e=e.options,t){t={};for(var o=0;o<a.length;o++)t["$"+a[o]]=!0;for(a=0;a<e.length;a++)o=t.hasOwnProperty("$"+e[a].value),e[a].selected!==o&&(e[a].selected=o),o&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Et(a),t=null,o=0;o<e.length;o++){if(e[o].value===a){e[o].selected=!0,l&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ad(e,t,a){if(t!=null&&(t=""+Et(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Et(a):""}function nd(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(c(92));if(be(l)){if(1<l.length)throw Error(c(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Et(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function Nn(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var f0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ld(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||f0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function rd(e,t,a){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var o in t)l=t[o],t.hasOwnProperty(o)&&a[o]!==l&&ld(e,o,l)}else for(var d in t)t.hasOwnProperty(d)&&ld(e,d,t[d])}function Oi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var h0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),m0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Mr(e){return m0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ri=null;function _i(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var wn=null,Sn=null;function sd(e){var t=gn(e);if(t&&(e=t.stateNode)){var a=e[it]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ci(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Tt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var o=l[it]||null;if(!o)throw Error(c(90));Ci(l,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&ed(l)}break e;case"textarea":ad(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&bn(e,!!a.multiple,t,!1)}}}var Ai=!1;function id(e,t,a){if(Ai)return e(t,a);Ai=!0;try{var l=e(t);return l}finally{if(Ai=!1,(wn!==null||Sn!==null)&&(xs(),wn&&(t=wn,e=Sn,Sn=wn=null,sd(t),e)))for(t=0;t<e.length;t++)sd(e[t])}}function ml(e,t){var a=e.stateNode;if(a===null)return null;var l=a[it]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(c(231,t,typeof a));return a}var Jt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Mi=!1;if(Jt)try{var pl={};Object.defineProperty(pl,"passive",{get:function(){Mi=!0}}),window.addEventListener("test",pl,pl),window.removeEventListener("test",pl,pl)}catch{Mi=!1}var xa=null,ki=null,kr=null;function cd(){if(kr)return kr;var e,t=ki,a=t.length,l,o="value"in xa?xa.value:xa.textContent,d=o.length;for(e=0;e<a&&t[e]===o[e];e++);var m=a-e;for(l=1;l<=m&&t[a-l]===o[d-l];l++);return kr=o.slice(e,1<l?1-l:void 0)}function Ur(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function zr(){return!0}function od(){return!1}function ct(e){function t(a,l,o,d,m){this._reactName=a,this._targetInst=o,this.type=l,this.nativeEvent=d,this.target=m,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(a=e[v],this[v]=a?a(d):d[v]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?zr:od,this.isPropagationStopped=od,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=zr)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=zr)},persist:function(){},isPersistent:zr}),t}var Xa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Lr=ct(Xa),gl=x({},Xa,{view:0,detail:0}),p0=ct(gl),Ui,zi,yl,Hr=x({},gl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==yl&&(yl&&e.type==="mousemove"?(Ui=e.screenX-yl.screenX,zi=e.screenY-yl.screenY):zi=Ui=0,yl=e),Ui)},movementY:function(e){return"movementY"in e?e.movementY:zi}}),ud=ct(Hr),g0=x({},Hr,{dataTransfer:0}),y0=ct(g0),x0=x({},gl,{relatedTarget:0}),Li=ct(x0),v0=x({},Xa,{animationName:0,elapsedTime:0,pseudoElement:0}),b0=ct(v0),N0=x({},Xa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),w0=ct(N0),S0=x({},Xa,{data:0}),dd=ct(S0),j0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},E0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},T0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function C0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=T0[e])?!!t[e]:!1}function Hi(){return C0}var D0=x({},gl,{key:function(e){if(e.key){var t=j0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ur(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?E0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hi,charCode:function(e){return e.type==="keypress"?Ur(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ur(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),O0=ct(D0),R0=x({},Hr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),fd=ct(R0),_0=x({},gl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hi}),A0=ct(_0),M0=x({},Xa,{propertyName:0,elapsedTime:0,pseudoElement:0}),k0=ct(M0),U0=x({},Hr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),z0=ct(U0),L0=x({},Xa,{newState:0,oldState:0}),H0=ct(L0),B0=[9,13,27,32],Bi=Jt&&"CompositionEvent"in window,xl=null;Jt&&"documentMode"in document&&(xl=document.documentMode);var q0=Jt&&"TextEvent"in window&&!xl,hd=Jt&&(!Bi||xl&&8<xl&&11>=xl),md=" ",pd=!1;function gd(e,t){switch(e){case"keyup":return B0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function yd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jn=!1;function Y0(e,t){switch(e){case"compositionend":return yd(t);case"keypress":return t.which!==32?null:(pd=!0,md);case"textInput":return e=t.data,e===md&&pd?null:e;default:return null}}function G0(e,t){if(jn)return e==="compositionend"||!Bi&&gd(e,t)?(e=cd(),kr=ki=xa=null,jn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return hd&&t.locale!=="ko"?null:t.data;default:return null}}var $0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function xd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!$0[e.type]:t==="textarea"}function vd(e,t,a,l){wn?Sn?Sn.push(l):Sn=[l]:wn=l,t=js(t,"onChange"),0<t.length&&(a=new Lr("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var vl=null,bl=null;function V0(e){em(e,0)}function Br(e){var t=hl(e);if(ed(t))return e}function bd(e,t){if(e==="change")return t}var Nd=!1;if(Jt){var qi;if(Jt){var Yi="oninput"in document;if(!Yi){var wd=document.createElement("div");wd.setAttribute("oninput","return;"),Yi=typeof wd.oninput=="function"}qi=Yi}else qi=!1;Nd=qi&&(!document.documentMode||9<document.documentMode)}function Sd(){vl&&(vl.detachEvent("onpropertychange",jd),bl=vl=null)}function jd(e){if(e.propertyName==="value"&&Br(bl)){var t=[];vd(t,bl,e,_i(e)),id(V0,t)}}function X0(e,t,a){e==="focusin"?(Sd(),vl=t,bl=a,vl.attachEvent("onpropertychange",jd)):e==="focusout"&&Sd()}function P0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Br(bl)}function Q0(e,t){if(e==="click")return Br(t)}function Z0(e,t){if(e==="input"||e==="change")return Br(t)}function K0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:K0;function Nl(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var o=a[l];if(!gi.call(t,o)||!yt(e[o],t[o]))return!1}return!0}function Ed(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Td(e,t){var a=Ed(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Ed(a)}}function Cd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Cd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Dd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ar(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Ar(e.document)}return t}function Gi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var I0=Jt&&"documentMode"in document&&11>=document.documentMode,En=null,$i=null,wl=null,Vi=!1;function Od(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Vi||En==null||En!==Ar(l)||(l=En,"selectionStart"in l&&Gi(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),wl&&Nl(wl,l)||(wl=l,l=js($i,"onSelect"),0<l.length&&(t=new Lr("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=En)))}function Pa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Tn={animationend:Pa("Animation","AnimationEnd"),animationiteration:Pa("Animation","AnimationIteration"),animationstart:Pa("Animation","AnimationStart"),transitionrun:Pa("Transition","TransitionRun"),transitionstart:Pa("Transition","TransitionStart"),transitioncancel:Pa("Transition","TransitionCancel"),transitionend:Pa("Transition","TransitionEnd")},Xi={},Rd={};Jt&&(Rd=document.createElement("div").style,"AnimationEvent"in window||(delete Tn.animationend.animation,delete Tn.animationiteration.animation,delete Tn.animationstart.animation),"TransitionEvent"in window||delete Tn.transitionend.transition);function Qa(e){if(Xi[e])return Xi[e];if(!Tn[e])return e;var t=Tn[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Rd)return Xi[e]=t[a];return e}var _d=Qa("animationend"),Ad=Qa("animationiteration"),Md=Qa("animationstart"),F0=Qa("transitionrun"),J0=Qa("transitionstart"),W0=Qa("transitioncancel"),kd=Qa("transitionend"),Ud=new Map,Pi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Pi.push("scrollEnd");function zt(e,t){Ud.set(e,t),Va(t,[e])}var zd=new WeakMap;function Ct(e,t){if(typeof e=="object"&&e!==null){var a=zd.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Ju(t)},zd.set(e,t),t)}return{value:e,source:t,stack:Ju(t)}}var Dt=[],Cn=0,Qi=0;function qr(){for(var e=Cn,t=Qi=Cn=0;t<e;){var a=Dt[t];Dt[t++]=null;var l=Dt[t];Dt[t++]=null;var o=Dt[t];Dt[t++]=null;var d=Dt[t];if(Dt[t++]=null,l!==null&&o!==null){var m=l.pending;m===null?o.next=o:(o.next=m.next,m.next=o),l.pending=o}d!==0&&Ld(a,o,d)}}function Yr(e,t,a,l){Dt[Cn++]=e,Dt[Cn++]=t,Dt[Cn++]=a,Dt[Cn++]=l,Qi|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Zi(e,t,a,l){return Yr(e,t,a,l),Gr(e)}function Dn(e,t){return Yr(e,null,null,t),Gr(e)}function Ld(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var o=!1,d=e.return;d!==null;)d.childLanes|=a,l=d.alternate,l!==null&&(l.childLanes|=a),d.tag===22&&(e=d.stateNode,e===null||e._visibility&1||(o=!0)),e=d,d=d.return;return e.tag===3?(d=e.stateNode,o&&t!==null&&(o=31-gt(a),e=d.hiddenUpdates,l=e[o],l===null?e[o]=[t]:l.push(t),t.lane=a|536870912),d):null}function Gr(e){if(50<Ql)throw Ql=0,eo=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var On={};function ey(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function xt(e,t,a,l){return new ey(e,t,a,l)}function Ki(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wt(e,t){var a=e.alternate;return a===null?(a=xt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Hd(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function $r(e,t,a,l,o,d){var m=0;if(l=e,typeof e=="function")Ki(e)&&(m=1);else if(typeof e=="string")m=ax(e,a,P.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case I:return e=xt(31,a,t,o),e.elementType=I,e.lanes=d,e;case T:return Za(a.children,o,d,t);case C:m=8,o|=24;break;case S:return e=xt(12,a,t,o|2),e.elementType=S,e.lanes=d,e;case Y:return e=xt(13,a,t,o),e.elementType=Y,e.lanes=d,e;case Z:return e=xt(19,a,t,o),e.elementType=Z,e.lanes=d,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case E:case $:m=10;break e;case k:m=9;break e;case z:m=11;break e;case se:m=14;break e;case Q:m=16,l=null;break e}m=29,a=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=xt(m,a,t,o),t.elementType=e,t.type=l,t.lanes=d,t}function Za(e,t,a,l){return e=xt(7,e,l,t),e.lanes=a,e}function Ii(e,t,a){return e=xt(6,e,null,t),e.lanes=a,e}function Fi(e,t,a){return t=xt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Rn=[],_n=0,Vr=null,Xr=0,Ot=[],Rt=0,Ka=null,ea=1,ta="";function Ia(e,t){Rn[_n++]=Xr,Rn[_n++]=Vr,Vr=e,Xr=t}function Bd(e,t,a){Ot[Rt++]=ea,Ot[Rt++]=ta,Ot[Rt++]=Ka,Ka=e;var l=ea;e=ta;var o=32-gt(l)-1;l&=~(1<<o),a+=1;var d=32-gt(t)+o;if(30<d){var m=o-o%5;d=(l&(1<<m)-1).toString(32),l>>=m,o-=m,ea=1<<32-gt(t)+o|a<<o|l,ta=d+e}else ea=1<<d|a<<o|l,ta=e}function Ji(e){e.return!==null&&(Ia(e,1),Bd(e,1,0))}function Wi(e){for(;e===Vr;)Vr=Rn[--_n],Rn[_n]=null,Xr=Rn[--_n],Rn[_n]=null;for(;e===Ka;)Ka=Ot[--Rt],Ot[Rt]=null,ta=Ot[--Rt],Ot[Rt]=null,ea=Ot[--Rt],Ot[Rt]=null}var st=null,He=null,Se=!1,Fa=null,Xt=!1,ec=Error(c(519));function Ja(e){var t=Error(c(418,""));throw El(Ct(t,e)),ec}function qd(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[tt]=e,t[it]=l,a){case"dialog":xe("cancel",t),xe("close",t);break;case"iframe":case"object":case"embed":xe("load",t);break;case"video":case"audio":for(a=0;a<Kl.length;a++)xe(Kl[a],t);break;case"source":xe("error",t);break;case"img":case"image":case"link":xe("error",t),xe("load",t);break;case"details":xe("toggle",t);break;case"input":xe("invalid",t),td(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),_r(t);break;case"select":xe("invalid",t);break;case"textarea":xe("invalid",t),nd(t,l.value,l.defaultValue,l.children),_r(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||lm(t.textContent,a)?(l.popover!=null&&(xe("beforetoggle",t),xe("toggle",t)),l.onScroll!=null&&xe("scroll",t),l.onScrollEnd!=null&&xe("scrollend",t),l.onClick!=null&&(t.onclick=Es),t=!0):t=!1,t||Ja(e)}function Yd(e){for(st=e.return;st;)switch(st.tag){case 5:case 13:Xt=!1;return;case 27:case 3:Xt=!0;return;default:st=st.return}}function Sl(e){if(e!==st)return!1;if(!Se)return Yd(e),Se=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||yo(e.type,e.memoizedProps)),a=!a),a&&He&&Ja(e),Yd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){He=Ht(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}He=null}}else t===27?(t=He,ka(e.type)?(e=No,No=null,He=e):He=t):He=st?Ht(e.stateNode.nextSibling):null;return!0}function jl(){He=st=null,Se=!1}function Gd(){var e=Fa;return e!==null&&(dt===null?dt=e:dt.push.apply(dt,e),Fa=null),e}function El(e){Fa===null?Fa=[e]:Fa.push(e)}var tc=X(null),Wa=null,aa=null;function va(e,t,a){F(tc,t._currentValue),t._currentValue=a}function na(e){e._currentValue=tc.current,W(tc)}function ac(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function nc(e,t,a,l){var o=e.child;for(o!==null&&(o.return=e);o!==null;){var d=o.dependencies;if(d!==null){var m=o.child;d=d.firstContext;e:for(;d!==null;){var v=d;d=o;for(var j=0;j<t.length;j++)if(v.context===t[j]){d.lanes|=a,v=d.alternate,v!==null&&(v.lanes|=a),ac(d.return,a,e),l||(m=null);break e}d=v.next}}else if(o.tag===18){if(m=o.return,m===null)throw Error(c(341));m.lanes|=a,d=m.alternate,d!==null&&(d.lanes|=a),ac(m,a,e),m=null}else m=o.child;if(m!==null)m.return=o;else for(m=o;m!==null;){if(m===e){m=null;break}if(o=m.sibling,o!==null){o.return=m.return,m=o;break}m=m.return}o=m}}function Tl(e,t,a,l){e=null;for(var o=t,d=!1;o!==null;){if(!d){if((o.flags&524288)!==0)d=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var m=o.alternate;if(m===null)throw Error(c(387));if(m=m.memoizedProps,m!==null){var v=o.type;yt(o.pendingProps.value,m.value)||(e!==null?e.push(v):e=[v])}}else if(o===et.current){if(m=o.alternate,m===null)throw Error(c(387));m.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(e!==null?e.push(tr):e=[tr])}o=o.return}e!==null&&nc(t,e,a,l),t.flags|=262144}function Pr(e){for(e=e.firstContext;e!==null;){if(!yt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function en(e){Wa=e,aa=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function at(e){return $d(Wa,e)}function Qr(e,t){return Wa===null&&en(e),$d(e,t)}function $d(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},aa===null){if(e===null)throw Error(c(308));aa=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else aa=aa.next=t;return a}var ty=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},ay=n.unstable_scheduleCallback,ny=n.unstable_NormalPriority,Xe={$$typeof:$,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function lc(){return{controller:new ty,data:new Map,refCount:0}}function Cl(e){e.refCount--,e.refCount===0&&ay(ny,function(){e.controller.abort()})}var Dl=null,rc=0,An=0,Mn=null;function ly(e,t){if(Dl===null){var a=Dl=[];rc=0,An=io(),Mn={status:"pending",value:void 0,then:function(l){a.push(l)}}}return rc++,t.then(Vd,Vd),t}function Vd(){if(--rc===0&&Dl!==null){Mn!==null&&(Mn.status="fulfilled");var e=Dl;Dl=null,An=0,Mn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function ry(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(o){a.push(o)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var o=0;o<a.length;o++)(0,a[o])(t)},function(o){for(l.status="rejected",l.reason=o,o=0;o<a.length;o++)(0,a[o])(void 0)}),l}var Xd=B.S;B.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&ly(e,t),Xd!==null&&Xd(e,t)};var tn=X(null);function sc(){var e=tn.current;return e!==null?e:Me.pooledCache}function Zr(e,t){t===null?F(tn,tn.current):F(tn,t.pool)}function Pd(){var e=sc();return e===null?null:{parent:Xe._currentValue,pool:e}}var Ol=Error(c(460)),Qd=Error(c(474)),Kr=Error(c(542)),ic={then:function(){}};function Zd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ir(){}function Kd(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Ir,Ir),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Fd(e),e;default:if(typeof t.status=="string")t.then(Ir,Ir);else{if(e=Me,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var o=t;o.status="fulfilled",o.value=l}},function(l){if(t.status==="pending"){var o=t;o.status="rejected",o.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Fd(e),e}throw Rl=t,Ol}}var Rl=null;function Id(){if(Rl===null)throw Error(c(459));var e=Rl;return Rl=null,e}function Fd(e){if(e===Ol||e===Kr)throw Error(c(483))}var ba=!1;function cc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function oc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Na(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function wa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Ee&2)!==0){var o=l.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),l.pending=t,t=Gr(e),Ld(e,null,a),t}return Yr(e,l,t,a),Gr(e)}function _l(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Vu(e,a)}}function uc(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var o=null,d=null;if(a=a.firstBaseUpdate,a!==null){do{var m={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};d===null?o=d=m:d=d.next=m,a=a.next}while(a!==null);d===null?o=d=t:d=d.next=t}else o=d=t;a={baseState:l.baseState,firstBaseUpdate:o,lastBaseUpdate:d,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var dc=!1;function Al(){if(dc){var e=Mn;if(e!==null)throw e}}function Ml(e,t,a,l){dc=!1;var o=e.updateQueue;ba=!1;var d=o.firstBaseUpdate,m=o.lastBaseUpdate,v=o.shared.pending;if(v!==null){o.shared.pending=null;var j=v,M=j.next;j.next=null,m===null?d=M:m.next=M,m=j;var q=e.alternate;q!==null&&(q=q.updateQueue,v=q.lastBaseUpdate,v!==m&&(v===null?q.firstBaseUpdate=M:v.next=M,q.lastBaseUpdate=j))}if(d!==null){var V=o.baseState;m=0,q=M=j=null,v=d;do{var U=v.lane&-536870913,L=U!==v.lane;if(L?(Ne&U)===U:(l&U)===U){U!==0&&U===An&&(dc=!0),q!==null&&(q=q.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});e:{var de=e,ce=v;U=t;var Oe=a;switch(ce.tag){case 1:if(de=ce.payload,typeof de=="function"){V=de.call(Oe,V,U);break e}V=de;break e;case 3:de.flags=de.flags&-65537|128;case 0:if(de=ce.payload,U=typeof de=="function"?de.call(Oe,V,U):de,U==null)break e;V=x({},V,U);break e;case 2:ba=!0}}U=v.callback,U!==null&&(e.flags|=64,L&&(e.flags|=8192),L=o.callbacks,L===null?o.callbacks=[U]:L.push(U))}else L={lane:U,tag:v.tag,payload:v.payload,callback:v.callback,next:null},q===null?(M=q=L,j=V):q=q.next=L,m|=U;if(v=v.next,v===null){if(v=o.shared.pending,v===null)break;L=v,v=L.next,L.next=null,o.lastBaseUpdate=L,o.shared.pending=null}}while(!0);q===null&&(j=V),o.baseState=j,o.firstBaseUpdate=M,o.lastBaseUpdate=q,d===null&&(o.shared.lanes=0),Ra|=m,e.lanes=m,e.memoizedState=V}}function Jd(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function Wd(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Jd(a[e],t)}var kn=X(null),Fr=X(0);function ef(e,t){e=ua,F(Fr,e),F(kn,t),ua=e|t.baseLanes}function fc(){F(Fr,ua),F(kn,kn.current)}function hc(){ua=Fr.current,W(kn),W(Fr)}var Sa=0,pe=null,Ce=null,Ge=null,Jr=!1,Un=!1,an=!1,Wr=0,kl=0,zn=null,sy=0;function qe(){throw Error(c(321))}function mc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!yt(e[a],t[a]))return!1;return!0}function pc(e,t,a,l,o,d){return Sa=d,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,B.H=e===null||e.memoizedState===null?Hf:Bf,an=!1,d=a(l,o),an=!1,Un&&(d=af(t,a,l,o)),tf(e),d}function tf(e){B.H=rs;var t=Ce!==null&&Ce.next!==null;if(Sa=0,Ge=Ce=pe=null,Jr=!1,kl=0,zn=null,t)throw Error(c(300));e===null||Ze||(e=e.dependencies,e!==null&&Pr(e)&&(Ze=!0))}function af(e,t,a,l){pe=e;var o=0;do{if(Un&&(zn=null),kl=0,Un=!1,25<=o)throw Error(c(301));if(o+=1,Ge=Ce=null,e.updateQueue!=null){var d=e.updateQueue;d.lastEffect=null,d.events=null,d.stores=null,d.memoCache!=null&&(d.memoCache.index=0)}B.H=hy,d=t(a,l)}while(Un);return d}function iy(){var e=B.H,t=e.useState()[0];return t=typeof t.then=="function"?Ul(t):t,e=e.useState()[0],(Ce!==null?Ce.memoizedState:null)!==e&&(pe.flags|=1024),t}function gc(){var e=Wr!==0;return Wr=0,e}function yc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function xc(e){if(Jr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Jr=!1}Sa=0,Ge=Ce=pe=null,Un=!1,kl=Wr=0,zn=null}function ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?pe.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function $e(){if(Ce===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Ge===null?pe.memoizedState:Ge.next;if(t!==null)Ge=t,Ce=e;else{if(e===null)throw pe.alternate===null?Error(c(467)):Error(c(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Ge===null?pe.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function vc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ul(e){var t=kl;return kl+=1,zn===null&&(zn=[]),e=Kd(zn,e,t),t=pe,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,B.H=t===null||t.memoizedState===null?Hf:Bf),e}function es(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ul(e);if(e.$$typeof===$)return at(e)}throw Error(c(438,String(e)))}function bc(e){var t=null,a=pe.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=pe.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(o){return o.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=vc(),pe.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=oe;return t.index++,a}function la(e,t){return typeof t=="function"?t(e):t}function ts(e){var t=$e();return Nc(t,Ce,e)}function Nc(e,t,a){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=a;var o=e.baseQueue,d=l.pending;if(d!==null){if(o!==null){var m=o.next;o.next=d.next,d.next=m}t.baseQueue=o=d,l.pending=null}if(d=e.baseState,o===null)e.memoizedState=d;else{t=o.next;var v=m=null,j=null,M=t,q=!1;do{var V=M.lane&-536870913;if(V!==M.lane?(Ne&V)===V:(Sa&V)===V){var U=M.revertLane;if(U===0)j!==null&&(j=j.next={lane:0,revertLane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),V===An&&(q=!0);else if((Sa&U)===U){M=M.next,U===An&&(q=!0);continue}else V={lane:0,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},j===null?(v=j=V,m=d):j=j.next=V,pe.lanes|=U,Ra|=U;V=M.action,an&&a(d,V),d=M.hasEagerState?M.eagerState:a(d,V)}else U={lane:V,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},j===null?(v=j=U,m=d):j=j.next=U,pe.lanes|=V,Ra|=V;M=M.next}while(M!==null&&M!==t);if(j===null?m=d:j.next=v,!yt(d,e.memoizedState)&&(Ze=!0,q&&(a=Mn,a!==null)))throw a;e.memoizedState=d,e.baseState=m,e.baseQueue=j,l.lastRenderedState=d}return o===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function wc(e){var t=$e(),a=t.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=e;var l=a.dispatch,o=a.pending,d=t.memoizedState;if(o!==null){a.pending=null;var m=o=o.next;do d=e(d,m.action),m=m.next;while(m!==o);yt(d,t.memoizedState)||(Ze=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),a.lastRenderedState=d}return[d,l]}function nf(e,t,a){var l=pe,o=$e(),d=Se;if(d){if(a===void 0)throw Error(c(407));a=a()}else a=t();var m=!yt((Ce||o).memoizedState,a);m&&(o.memoizedState=a,Ze=!0),o=o.queue;var v=sf.bind(null,l,o,e);if(zl(2048,8,v,[e]),o.getSnapshot!==t||m||Ge!==null&&Ge.memoizedState.tag&1){if(l.flags|=2048,Ln(9,as(),rf.bind(null,l,o,a,t),null),Me===null)throw Error(c(349));d||(Sa&124)!==0||lf(l,t,a)}return a}function lf(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=pe.updateQueue,t===null?(t=vc(),pe.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function rf(e,t,a,l){t.value=a,t.getSnapshot=l,cf(t)&&of(e)}function sf(e,t,a){return a(function(){cf(t)&&of(e)})}function cf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!yt(e,a)}catch{return!0}}function of(e){var t=Dn(e,2);t!==null&&St(t,e,2)}function Sc(e){var t=ot();if(typeof e=="function"){var a=e;if(e=a(),an){ga(!0);try{a()}finally{ga(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:la,lastRenderedState:e},t}function uf(e,t,a,l){return e.baseState=a,Nc(e,Ce,typeof l=="function"?l:la)}function cy(e,t,a,l,o){if(ls(e))throw Error(c(485));if(e=t.action,e!==null){var d={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){d.listeners.push(m)}};B.T!==null?a(!0):d.isTransition=!1,l(d),a=t.pending,a===null?(d.next=t.pending=d,df(t,d)):(d.next=a.next,t.pending=a.next=d)}}function df(e,t){var a=t.action,l=t.payload,o=e.state;if(t.isTransition){var d=B.T,m={};B.T=m;try{var v=a(o,l),j=B.S;j!==null&&j(m,v),ff(e,t,v)}catch(M){jc(e,t,M)}finally{B.T=d}}else try{d=a(o,l),ff(e,t,d)}catch(M){jc(e,t,M)}}function ff(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){hf(e,t,l)},function(l){return jc(e,t,l)}):hf(e,t,a)}function hf(e,t,a){t.status="fulfilled",t.value=a,mf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,df(e,a)))}function jc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,mf(t),t=t.next;while(t!==l)}e.action=null}function mf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function pf(e,t){return t}function gf(e,t){if(Se){var a=Me.formState;if(a!==null){e:{var l=pe;if(Se){if(He){t:{for(var o=He,d=Xt;o.nodeType!==8;){if(!d){o=null;break t}if(o=Ht(o.nextSibling),o===null){o=null;break t}}d=o.data,o=d==="F!"||d==="F"?o:null}if(o){He=Ht(o.nextSibling),l=o.data==="F!";break e}}Ja(l)}l=!1}l&&(t=a[0])}}return a=ot(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pf,lastRenderedState:t},a.queue=l,a=Uf.bind(null,pe,l),l.dispatch=a,l=Sc(!1),d=Oc.bind(null,pe,!1,l.queue),l=ot(),o={state:t,dispatch:null,action:e,pending:null},l.queue=o,a=cy.bind(null,pe,o,d,a),o.dispatch=a,l.memoizedState=e,[t,a,!1]}function yf(e){var t=$e();return xf(t,Ce,e)}function xf(e,t,a){if(t=Nc(e,t,pf)[0],e=ts(la)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Ul(t)}catch(m){throw m===Ol?Kr:m}else l=t;t=$e();var o=t.queue,d=o.dispatch;return a!==t.memoizedState&&(pe.flags|=2048,Ln(9,as(),oy.bind(null,o,a),null)),[l,d,e]}function oy(e,t){e.action=t}function vf(e){var t=$e(),a=Ce;if(a!==null)return xf(t,a,e);$e(),t=t.memoizedState,a=$e();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Ln(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=pe.updateQueue,t===null&&(t=vc(),pe.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function as(){return{destroy:void 0,resource:void 0}}function bf(){return $e().memoizedState}function ns(e,t,a,l){var o=ot();l=l===void 0?null:l,pe.flags|=e,o.memoizedState=Ln(1|t,as(),a,l)}function zl(e,t,a,l){var o=$e();l=l===void 0?null:l;var d=o.memoizedState.inst;Ce!==null&&l!==null&&mc(l,Ce.memoizedState.deps)?o.memoizedState=Ln(t,d,a,l):(pe.flags|=e,o.memoizedState=Ln(1|t,d,a,l))}function Nf(e,t){ns(8390656,8,e,t)}function wf(e,t){zl(2048,8,e,t)}function Sf(e,t){return zl(4,2,e,t)}function jf(e,t){return zl(4,4,e,t)}function Ef(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Tf(e,t,a){a=a!=null?a.concat([e]):null,zl(4,4,Ef.bind(null,t,e),a)}function Ec(){}function Cf(e,t){var a=$e();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&mc(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Df(e,t){var a=$e();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&mc(t,l[1]))return l[0];if(l=e(),an){ga(!0);try{e()}finally{ga(!1)}}return a.memoizedState=[l,t],l}function Tc(e,t,a){return a===void 0||(Sa&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=_h(),pe.lanes|=e,Ra|=e,a)}function Of(e,t,a,l){return yt(a,t)?a:kn.current!==null?(e=Tc(e,a,l),yt(e,t)||(Ze=!0),e):(Sa&42)===0?(Ze=!0,e.memoizedState=a):(e=_h(),pe.lanes|=e,Ra|=e,t)}function Rf(e,t,a,l,o){var d=K.p;K.p=d!==0&&8>d?d:8;var m=B.T,v={};B.T=v,Oc(e,!1,t,a);try{var j=o(),M=B.S;if(M!==null&&M(v,j),j!==null&&typeof j=="object"&&typeof j.then=="function"){var q=ry(j,l);Ll(e,t,q,wt(e))}else Ll(e,t,l,wt(e))}catch(V){Ll(e,t,{then:function(){},status:"rejected",reason:V},wt())}finally{K.p=d,B.T=m}}function uy(){}function Cc(e,t,a,l){if(e.tag!==5)throw Error(c(476));var o=_f(e).queue;Rf(e,o,t,ie,a===null?uy:function(){return Af(e),a(l)})}function _f(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ie,baseState:ie,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:la,lastRenderedState:ie},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:la,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Af(e){var t=_f(e).next.queue;Ll(e,t,{},wt())}function Dc(){return at(tr)}function Mf(){return $e().memoizedState}function kf(){return $e().memoizedState}function dy(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=wt();e=Na(a);var l=wa(t,e,a);l!==null&&(St(l,t,a),_l(l,t,a)),t={cache:lc()},e.payload=t;return}t=t.return}}function fy(e,t,a){var l=wt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ls(e)?zf(t,a):(a=Zi(e,t,a,l),a!==null&&(St(a,e,l),Lf(a,t,l)))}function Uf(e,t,a){var l=wt();Ll(e,t,a,l)}function Ll(e,t,a,l){var o={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ls(e))zf(t,o);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var m=t.lastRenderedState,v=d(m,a);if(o.hasEagerState=!0,o.eagerState=v,yt(v,m))return Yr(e,t,o,0),Me===null&&qr(),!1}catch{}finally{}if(a=Zi(e,t,o,l),a!==null)return St(a,e,l),Lf(a,t,l),!0}return!1}function Oc(e,t,a,l){if(l={lane:2,revertLane:io(),action:l,hasEagerState:!1,eagerState:null,next:null},ls(e)){if(t)throw Error(c(479))}else t=Zi(e,a,l,2),t!==null&&St(t,e,2)}function ls(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function zf(e,t){Un=Jr=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Lf(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Vu(e,a)}}var rs={readContext:at,use:es,useCallback:qe,useContext:qe,useEffect:qe,useImperativeHandle:qe,useLayoutEffect:qe,useInsertionEffect:qe,useMemo:qe,useReducer:qe,useRef:qe,useState:qe,useDebugValue:qe,useDeferredValue:qe,useTransition:qe,useSyncExternalStore:qe,useId:qe,useHostTransitionStatus:qe,useFormState:qe,useActionState:qe,useOptimistic:qe,useMemoCache:qe,useCacheRefresh:qe},Hf={readContext:at,use:es,useCallback:function(e,t){return ot().memoizedState=[e,t===void 0?null:t],e},useContext:at,useEffect:Nf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,ns(4194308,4,Ef.bind(null,t,e),a)},useLayoutEffect:function(e,t){return ns(4194308,4,e,t)},useInsertionEffect:function(e,t){ns(4,2,e,t)},useMemo:function(e,t){var a=ot();t=t===void 0?null:t;var l=e();if(an){ga(!0);try{e()}finally{ga(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=ot();if(a!==void 0){var o=a(t);if(an){ga(!0);try{a(t)}finally{ga(!1)}}}else o=t;return l.memoizedState=l.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},l.queue=e,e=e.dispatch=fy.bind(null,pe,e),[l.memoizedState,e]},useRef:function(e){var t=ot();return e={current:e},t.memoizedState=e},useState:function(e){e=Sc(e);var t=e.queue,a=Uf.bind(null,pe,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Ec,useDeferredValue:function(e,t){var a=ot();return Tc(a,e,t)},useTransition:function(){var e=Sc(!1);return e=Rf.bind(null,pe,e.queue,!0,!1),ot().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=pe,o=ot();if(Se){if(a===void 0)throw Error(c(407));a=a()}else{if(a=t(),Me===null)throw Error(c(349));(Ne&124)!==0||lf(l,t,a)}o.memoizedState=a;var d={value:a,getSnapshot:t};return o.queue=d,Nf(sf.bind(null,l,d,e),[e]),l.flags|=2048,Ln(9,as(),rf.bind(null,l,d,a,t),null),a},useId:function(){var e=ot(),t=Me.identifierPrefix;if(Se){var a=ta,l=ea;a=(l&~(1<<32-gt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Wr++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=sy++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Dc,useFormState:gf,useActionState:gf,useOptimistic:function(e){var t=ot();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Oc.bind(null,pe,!0,a),a.dispatch=t,[e,t]},useMemoCache:bc,useCacheRefresh:function(){return ot().memoizedState=dy.bind(null,pe)}},Bf={readContext:at,use:es,useCallback:Cf,useContext:at,useEffect:wf,useImperativeHandle:Tf,useInsertionEffect:Sf,useLayoutEffect:jf,useMemo:Df,useReducer:ts,useRef:bf,useState:function(){return ts(la)},useDebugValue:Ec,useDeferredValue:function(e,t){var a=$e();return Of(a,Ce.memoizedState,e,t)},useTransition:function(){var e=ts(la)[0],t=$e().memoizedState;return[typeof e=="boolean"?e:Ul(e),t]},useSyncExternalStore:nf,useId:Mf,useHostTransitionStatus:Dc,useFormState:yf,useActionState:yf,useOptimistic:function(e,t){var a=$e();return uf(a,Ce,e,t)},useMemoCache:bc,useCacheRefresh:kf},hy={readContext:at,use:es,useCallback:Cf,useContext:at,useEffect:wf,useImperativeHandle:Tf,useInsertionEffect:Sf,useLayoutEffect:jf,useMemo:Df,useReducer:wc,useRef:bf,useState:function(){return wc(la)},useDebugValue:Ec,useDeferredValue:function(e,t){var a=$e();return Ce===null?Tc(a,e,t):Of(a,Ce.memoizedState,e,t)},useTransition:function(){var e=wc(la)[0],t=$e().memoizedState;return[typeof e=="boolean"?e:Ul(e),t]},useSyncExternalStore:nf,useId:Mf,useHostTransitionStatus:Dc,useFormState:vf,useActionState:vf,useOptimistic:function(e,t){var a=$e();return Ce!==null?uf(a,Ce,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:bc,useCacheRefresh:kf},Hn=null,Hl=0;function ss(e){var t=Hl;return Hl+=1,Hn===null&&(Hn=[]),Kd(Hn,e,t)}function Bl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function is(e,t){throw t.$$typeof===w?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function qf(e){var t=e._init;return t(e._payload)}function Yf(e){function t(_,O){if(e){var A=_.deletions;A===null?(_.deletions=[O],_.flags|=16):A.push(O)}}function a(_,O){if(!e)return null;for(;O!==null;)t(_,O),O=O.sibling;return null}function l(_){for(var O=new Map;_!==null;)_.key!==null?O.set(_.key,_):O.set(_.index,_),_=_.sibling;return O}function o(_,O){return _=Wt(_,O),_.index=0,_.sibling=null,_}function d(_,O,A){return _.index=A,e?(A=_.alternate,A!==null?(A=A.index,A<O?(_.flags|=67108866,O):A):(_.flags|=67108866,O)):(_.flags|=1048576,O)}function m(_){return e&&_.alternate===null&&(_.flags|=67108866),_}function v(_,O,A,G){return O===null||O.tag!==6?(O=Ii(A,_.mode,G),O.return=_,O):(O=o(O,A),O.return=_,O)}function j(_,O,A,G){var ae=A.type;return ae===T?q(_,O,A.props.children,G,A.key):O!==null&&(O.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===Q&&qf(ae)===O.type)?(O=o(O,A.props),Bl(O,A),O.return=_,O):(O=$r(A.type,A.key,A.props,null,_.mode,G),Bl(O,A),O.return=_,O)}function M(_,O,A,G){return O===null||O.tag!==4||O.stateNode.containerInfo!==A.containerInfo||O.stateNode.implementation!==A.implementation?(O=Fi(A,_.mode,G),O.return=_,O):(O=o(O,A.children||[]),O.return=_,O)}function q(_,O,A,G,ae){return O===null||O.tag!==7?(O=Za(A,_.mode,G,ae),O.return=_,O):(O=o(O,A),O.return=_,O)}function V(_,O,A){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return O=Ii(""+O,_.mode,A),O.return=_,O;if(typeof O=="object"&&O!==null){switch(O.$$typeof){case b:return A=$r(O.type,O.key,O.props,null,_.mode,A),Bl(A,O),A.return=_,A;case R:return O=Fi(O,_.mode,A),O.return=_,O;case Q:var G=O._init;return O=G(O._payload),V(_,O,A)}if(be(O)||Re(O))return O=Za(O,_.mode,A,null),O.return=_,O;if(typeof O.then=="function")return V(_,ss(O),A);if(O.$$typeof===$)return V(_,Qr(_,O),A);is(_,O)}return null}function U(_,O,A,G){var ae=O!==null?O.key:null;if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return ae!==null?null:v(_,O,""+A,G);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case b:return A.key===ae?j(_,O,A,G):null;case R:return A.key===ae?M(_,O,A,G):null;case Q:return ae=A._init,A=ae(A._payload),U(_,O,A,G)}if(be(A)||Re(A))return ae!==null?null:q(_,O,A,G,null);if(typeof A.then=="function")return U(_,O,ss(A),G);if(A.$$typeof===$)return U(_,O,Qr(_,A),G);is(_,A)}return null}function L(_,O,A,G,ae){if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return _=_.get(A)||null,v(O,_,""+G,ae);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case b:return _=_.get(G.key===null?A:G.key)||null,j(O,_,G,ae);case R:return _=_.get(G.key===null?A:G.key)||null,M(O,_,G,ae);case Q:var ge=G._init;return G=ge(G._payload),L(_,O,A,G,ae)}if(be(G)||Re(G))return _=_.get(A)||null,q(O,_,G,ae,null);if(typeof G.then=="function")return L(_,O,A,ss(G),ae);if(G.$$typeof===$)return L(_,O,A,Qr(O,G),ae);is(O,G)}return null}function de(_,O,A,G){for(var ae=null,ge=null,re=O,ue=O=0,Ie=null;re!==null&&ue<A.length;ue++){re.index>ue?(Ie=re,re=null):Ie=re.sibling;var we=U(_,re,A[ue],G);if(we===null){re===null&&(re=Ie);break}e&&re&&we.alternate===null&&t(_,re),O=d(we,O,ue),ge===null?ae=we:ge.sibling=we,ge=we,re=Ie}if(ue===A.length)return a(_,re),Se&&Ia(_,ue),ae;if(re===null){for(;ue<A.length;ue++)re=V(_,A[ue],G),re!==null&&(O=d(re,O,ue),ge===null?ae=re:ge.sibling=re,ge=re);return Se&&Ia(_,ue),ae}for(re=l(re);ue<A.length;ue++)Ie=L(re,_,ue,A[ue],G),Ie!==null&&(e&&Ie.alternate!==null&&re.delete(Ie.key===null?ue:Ie.key),O=d(Ie,O,ue),ge===null?ae=Ie:ge.sibling=Ie,ge=Ie);return e&&re.forEach(function(Ba){return t(_,Ba)}),Se&&Ia(_,ue),ae}function ce(_,O,A,G){if(A==null)throw Error(c(151));for(var ae=null,ge=null,re=O,ue=O=0,Ie=null,we=A.next();re!==null&&!we.done;ue++,we=A.next()){re.index>ue?(Ie=re,re=null):Ie=re.sibling;var Ba=U(_,re,we.value,G);if(Ba===null){re===null&&(re=Ie);break}e&&re&&Ba.alternate===null&&t(_,re),O=d(Ba,O,ue),ge===null?ae=Ba:ge.sibling=Ba,ge=Ba,re=Ie}if(we.done)return a(_,re),Se&&Ia(_,ue),ae;if(re===null){for(;!we.done;ue++,we=A.next())we=V(_,we.value,G),we!==null&&(O=d(we,O,ue),ge===null?ae=we:ge.sibling=we,ge=we);return Se&&Ia(_,ue),ae}for(re=l(re);!we.done;ue++,we=A.next())we=L(re,_,ue,we.value,G),we!==null&&(e&&we.alternate!==null&&re.delete(we.key===null?ue:we.key),O=d(we,O,ue),ge===null?ae=we:ge.sibling=we,ge=we);return e&&re.forEach(function(mx){return t(_,mx)}),Se&&Ia(_,ue),ae}function Oe(_,O,A,G){if(typeof A=="object"&&A!==null&&A.type===T&&A.key===null&&(A=A.props.children),typeof A=="object"&&A!==null){switch(A.$$typeof){case b:e:{for(var ae=A.key;O!==null;){if(O.key===ae){if(ae=A.type,ae===T){if(O.tag===7){a(_,O.sibling),G=o(O,A.props.children),G.return=_,_=G;break e}}else if(O.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===Q&&qf(ae)===O.type){a(_,O.sibling),G=o(O,A.props),Bl(G,A),G.return=_,_=G;break e}a(_,O);break}else t(_,O);O=O.sibling}A.type===T?(G=Za(A.props.children,_.mode,G,A.key),G.return=_,_=G):(G=$r(A.type,A.key,A.props,null,_.mode,G),Bl(G,A),G.return=_,_=G)}return m(_);case R:e:{for(ae=A.key;O!==null;){if(O.key===ae)if(O.tag===4&&O.stateNode.containerInfo===A.containerInfo&&O.stateNode.implementation===A.implementation){a(_,O.sibling),G=o(O,A.children||[]),G.return=_,_=G;break e}else{a(_,O);break}else t(_,O);O=O.sibling}G=Fi(A,_.mode,G),G.return=_,_=G}return m(_);case Q:return ae=A._init,A=ae(A._payload),Oe(_,O,A,G)}if(be(A))return de(_,O,A,G);if(Re(A)){if(ae=Re(A),typeof ae!="function")throw Error(c(150));return A=ae.call(A),ce(_,O,A,G)}if(typeof A.then=="function")return Oe(_,O,ss(A),G);if(A.$$typeof===$)return Oe(_,O,Qr(_,A),G);is(_,A)}return typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint"?(A=""+A,O!==null&&O.tag===6?(a(_,O.sibling),G=o(O,A),G.return=_,_=G):(a(_,O),G=Ii(A,_.mode,G),G.return=_,_=G),m(_)):a(_,O)}return function(_,O,A,G){try{Hl=0;var ae=Oe(_,O,A,G);return Hn=null,ae}catch(re){if(re===Ol||re===Kr)throw re;var ge=xt(29,re,null,_.mode);return ge.lanes=G,ge.return=_,ge}finally{}}}var Bn=Yf(!0),Gf=Yf(!1),_t=X(null),Pt=null;function ja(e){var t=e.alternate;F(Pe,Pe.current&1),F(_t,e),Pt===null&&(t===null||kn.current!==null||t.memoizedState!==null)&&(Pt=e)}function $f(e){if(e.tag===22){if(F(Pe,Pe.current),F(_t,e),Pt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Pt=e)}}else Ea()}function Ea(){F(Pe,Pe.current),F(_t,_t.current)}function ra(e){W(_t),Pt===e&&(Pt=null),W(Pe)}var Pe=X(0);function cs(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||bo(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Rc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:x({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var _c={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=wt(),o=Na(l);o.payload=t,a!=null&&(o.callback=a),t=wa(e,o,l),t!==null&&(St(t,e,l),_l(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=wt(),o=Na(l);o.tag=1,o.payload=t,a!=null&&(o.callback=a),t=wa(e,o,l),t!==null&&(St(t,e,l),_l(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=wt(),l=Na(a);l.tag=2,t!=null&&(l.callback=t),t=wa(e,l,a),t!==null&&(St(t,e,a),_l(t,e,a))}};function Vf(e,t,a,l,o,d,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,d,m):t.prototype&&t.prototype.isPureReactComponent?!Nl(a,l)||!Nl(o,d):!0}function Xf(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&_c.enqueueReplaceState(t,t.state,null)}function nn(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=x({},a));for(var o in e)a[o]===void 0&&(a[o]=e[o])}return a}var os=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Pf(e){os(e)}function Qf(e){console.error(e)}function Zf(e){os(e)}function us(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Kf(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function Ac(e,t,a){return a=Na(a),a.tag=3,a.payload={element:null},a.callback=function(){us(e,t)},a}function If(e){return e=Na(e),e.tag=3,e}function Ff(e,t,a,l){var o=a.type.getDerivedStateFromError;if(typeof o=="function"){var d=l.value;e.payload=function(){return o(d)},e.callback=function(){Kf(t,a,l)}}var m=a.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(e.callback=function(){Kf(t,a,l),typeof o!="function"&&(_a===null?_a=new Set([this]):_a.add(this));var v=l.stack;this.componentDidCatch(l.value,{componentStack:v!==null?v:""})})}function my(e,t,a,l,o){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&Tl(t,a,o,!0),a=_t.current,a!==null){switch(a.tag){case 13:return Pt===null?ao():a.alternate===null&&Be===0&&(Be=3),a.flags&=-257,a.flags|=65536,a.lanes=o,l===ic?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),lo(e,l,o)),!1;case 22:return a.flags|=65536,l===ic?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),lo(e,l,o)),!1}throw Error(c(435,a.tag))}return lo(e,l,o),ao(),!1}if(Se)return t=_t.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=o,l!==ec&&(e=Error(c(422),{cause:l}),El(Ct(e,a)))):(l!==ec&&(t=Error(c(423),{cause:l}),El(Ct(t,a))),e=e.current.alternate,e.flags|=65536,o&=-o,e.lanes|=o,l=Ct(l,a),o=Ac(e.stateNode,l,o),uc(e,o),Be!==4&&(Be=2)),!1;var d=Error(c(520),{cause:l});if(d=Ct(d,a),Pl===null?Pl=[d]:Pl.push(d),Be!==4&&(Be=2),t===null)return!0;l=Ct(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=o&-o,a.lanes|=e,e=Ac(a.stateNode,l,e),uc(a,e),!1;case 1:if(t=a.type,d=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(_a===null||!_a.has(d))))return a.flags|=65536,o&=-o,a.lanes|=o,o=If(o),Ff(o,e,a,l),uc(a,o),!1}a=a.return}while(a!==null);return!1}var Jf=Error(c(461)),Ze=!1;function Fe(e,t,a,l){t.child=e===null?Gf(t,null,a,l):Bn(t,e.child,a,l)}function Wf(e,t,a,l,o){a=a.render;var d=t.ref;if("ref"in l){var m={};for(var v in l)v!=="ref"&&(m[v]=l[v])}else m=l;return en(t),l=pc(e,t,a,m,d,o),v=gc(),e!==null&&!Ze?(yc(e,t,o),sa(e,t,o)):(Se&&v&&Ji(t),t.flags|=1,Fe(e,t,l,o),t.child)}function eh(e,t,a,l,o){if(e===null){var d=a.type;return typeof d=="function"&&!Ki(d)&&d.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=d,th(e,t,d,l,o)):(e=$r(a.type,null,l,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,!qc(e,o)){var m=d.memoizedProps;if(a=a.compare,a=a!==null?a:Nl,a(m,l)&&e.ref===t.ref)return sa(e,t,o)}return t.flags|=1,e=Wt(d,l),e.ref=t.ref,e.return=t,t.child=e}function th(e,t,a,l,o){if(e!==null){var d=e.memoizedProps;if(Nl(d,l)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=l=d,qc(e,o))(e.flags&131072)!==0&&(Ze=!0);else return t.lanes=e.lanes,sa(e,t,o)}return Mc(e,t,a,l,o)}function ah(e,t,a){var l=t.pendingProps,o=l.children,d=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=d!==null?d.baseLanes|a:a,e!==null){for(o=t.child=e.child,d=0;o!==null;)d=d|o.lanes|o.childLanes,o=o.sibling;t.childLanes=d&~l}else t.childLanes=0,t.child=null;return nh(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Zr(t,d!==null?d.cachePool:null),d!==null?ef(t,d):fc(),$f(t);else return t.lanes=t.childLanes=536870912,nh(e,t,d!==null?d.baseLanes|a:a,a)}else d!==null?(Zr(t,d.cachePool),ef(t,d),Ea(),t.memoizedState=null):(e!==null&&Zr(t,null),fc(),Ea());return Fe(e,t,o,a),t.child}function nh(e,t,a,l){var o=sc();return o=o===null?null:{parent:Xe._currentValue,pool:o},t.memoizedState={baseLanes:a,cachePool:o},e!==null&&Zr(t,null),fc(),$f(t),e!==null&&Tl(e,t,l,!0),null}function ds(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(c(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Mc(e,t,a,l,o){return en(t),a=pc(e,t,a,l,void 0,o),l=gc(),e!==null&&!Ze?(yc(e,t,o),sa(e,t,o)):(Se&&l&&Ji(t),t.flags|=1,Fe(e,t,a,o),t.child)}function lh(e,t,a,l,o,d){return en(t),t.updateQueue=null,a=af(t,l,a,o),tf(e),l=gc(),e!==null&&!Ze?(yc(e,t,d),sa(e,t,d)):(Se&&l&&Ji(t),t.flags|=1,Fe(e,t,a,d),t.child)}function rh(e,t,a,l,o){if(en(t),t.stateNode===null){var d=On,m=a.contextType;typeof m=="object"&&m!==null&&(d=at(m)),d=new a(l,d),t.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,d.updater=_c,t.stateNode=d,d._reactInternals=t,d=t.stateNode,d.props=l,d.state=t.memoizedState,d.refs={},cc(t),m=a.contextType,d.context=typeof m=="object"&&m!==null?at(m):On,d.state=t.memoizedState,m=a.getDerivedStateFromProps,typeof m=="function"&&(Rc(t,a,m,l),d.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(m=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),m!==d.state&&_c.enqueueReplaceState(d,d.state,null),Ml(t,l,d,o),Al(),d.state=t.memoizedState),typeof d.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){d=t.stateNode;var v=t.memoizedProps,j=nn(a,v);d.props=j;var M=d.context,q=a.contextType;m=On,typeof q=="object"&&q!==null&&(m=at(q));var V=a.getDerivedStateFromProps;q=typeof V=="function"||typeof d.getSnapshotBeforeUpdate=="function",v=t.pendingProps!==v,q||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(v||M!==m)&&Xf(t,d,l,m),ba=!1;var U=t.memoizedState;d.state=U,Ml(t,l,d,o),Al(),M=t.memoizedState,v||U!==M||ba?(typeof V=="function"&&(Rc(t,a,V,l),M=t.memoizedState),(j=ba||Vf(t,a,j,l,U,M,m))?(q||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(t.flags|=4194308)):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=M),d.props=l,d.state=M,d.context=m,l=j):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{d=t.stateNode,oc(e,t),m=t.memoizedProps,q=nn(a,m),d.props=q,V=t.pendingProps,U=d.context,M=a.contextType,j=On,typeof M=="object"&&M!==null&&(j=at(M)),v=a.getDerivedStateFromProps,(M=typeof v=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(m!==V||U!==j)&&Xf(t,d,l,j),ba=!1,U=t.memoizedState,d.state=U,Ml(t,l,d,o),Al();var L=t.memoizedState;m!==V||U!==L||ba||e!==null&&e.dependencies!==null&&Pr(e.dependencies)?(typeof v=="function"&&(Rc(t,a,v,l),L=t.memoizedState),(q=ba||Vf(t,a,q,l,U,L,j)||e!==null&&e.dependencies!==null&&Pr(e.dependencies))?(M||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(l,L,j),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(l,L,j)),typeof d.componentDidUpdate=="function"&&(t.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof d.componentDidUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=L),d.props=l,d.state=L,d.context=j,l=q):(typeof d.componentDidUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),l=!1)}return d=l,ds(e,t),l=(t.flags&128)!==0,d||l?(d=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:d.render(),t.flags|=1,e!==null&&l?(t.child=Bn(t,e.child,null,o),t.child=Bn(t,null,a,o)):Fe(e,t,a,o),t.memoizedState=d.state,e=t.child):e=sa(e,t,o),e}function sh(e,t,a,l){return jl(),t.flags|=256,Fe(e,t,a,l),t.child}var kc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Uc(e){return{baseLanes:e,cachePool:Pd()}}function zc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=At),e}function ih(e,t,a){var l=t.pendingProps,o=!1,d=(t.flags&128)!==0,m;if((m=d)||(m=e!==null&&e.memoizedState===null?!1:(Pe.current&2)!==0),m&&(o=!0,t.flags&=-129),m=(t.flags&32)!==0,t.flags&=-33,e===null){if(Se){if(o?ja(t):Ea(),Se){var v=He,j;if(j=v){e:{for(j=v,v=Xt;j.nodeType!==8;){if(!v){v=null;break e}if(j=Ht(j.nextSibling),j===null){v=null;break e}}v=j}v!==null?(t.memoizedState={dehydrated:v,treeContext:Ka!==null?{id:ea,overflow:ta}:null,retryLane:536870912,hydrationErrors:null},j=xt(18,null,null,0),j.stateNode=v,j.return=t,t.child=j,st=t,He=null,j=!0):j=!1}j||Ja(t)}if(v=t.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return bo(v)?t.lanes=32:t.lanes=536870912,null;ra(t)}return v=l.children,l=l.fallback,o?(Ea(),o=t.mode,v=fs({mode:"hidden",children:v},o),l=Za(l,o,a,null),v.return=t,l.return=t,v.sibling=l,t.child=v,o=t.child,o.memoizedState=Uc(a),o.childLanes=zc(e,m,a),t.memoizedState=kc,l):(ja(t),Lc(t,v))}if(j=e.memoizedState,j!==null&&(v=j.dehydrated,v!==null)){if(d)t.flags&256?(ja(t),t.flags&=-257,t=Hc(e,t,a)):t.memoizedState!==null?(Ea(),t.child=e.child,t.flags|=128,t=null):(Ea(),o=l.fallback,v=t.mode,l=fs({mode:"visible",children:l.children},v),o=Za(o,v,a,null),o.flags|=2,l.return=t,o.return=t,l.sibling=o,t.child=l,Bn(t,e.child,null,a),l=t.child,l.memoizedState=Uc(a),l.childLanes=zc(e,m,a),t.memoizedState=kc,t=o);else if(ja(t),bo(v)){if(m=v.nextSibling&&v.nextSibling.dataset,m)var M=m.dgst;m=M,l=Error(c(419)),l.stack="",l.digest=m,El({value:l,source:null,stack:null}),t=Hc(e,t,a)}else if(Ze||Tl(e,t,a,!1),m=(a&e.childLanes)!==0,Ze||m){if(m=Me,m!==null&&(l=a&-a,l=(l&42)!==0?1:bi(l),l=(l&(m.suspendedLanes|a))!==0?0:l,l!==0&&l!==j.retryLane))throw j.retryLane=l,Dn(e,l),St(m,e,l),Jf;v.data==="$?"||ao(),t=Hc(e,t,a)}else v.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=j.treeContext,He=Ht(v.nextSibling),st=t,Se=!0,Fa=null,Xt=!1,e!==null&&(Ot[Rt++]=ea,Ot[Rt++]=ta,Ot[Rt++]=Ka,ea=e.id,ta=e.overflow,Ka=t),t=Lc(t,l.children),t.flags|=4096);return t}return o?(Ea(),o=l.fallback,v=t.mode,j=e.child,M=j.sibling,l=Wt(j,{mode:"hidden",children:l.children}),l.subtreeFlags=j.subtreeFlags&65011712,M!==null?o=Wt(M,o):(o=Za(o,v,a,null),o.flags|=2),o.return=t,l.return=t,l.sibling=o,t.child=l,l=o,o=t.child,v=e.child.memoizedState,v===null?v=Uc(a):(j=v.cachePool,j!==null?(M=Xe._currentValue,j=j.parent!==M?{parent:M,pool:M}:j):j=Pd(),v={baseLanes:v.baseLanes|a,cachePool:j}),o.memoizedState=v,o.childLanes=zc(e,m,a),t.memoizedState=kc,l):(ja(t),a=e.child,e=a.sibling,a=Wt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(m=t.deletions,m===null?(t.deletions=[e],t.flags|=16):m.push(e)),t.child=a,t.memoizedState=null,a)}function Lc(e,t){return t=fs({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function fs(e,t){return e=xt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Hc(e,t,a){return Bn(t,e.child,null,a),e=Lc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ch(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),ac(e.return,t,a)}function Bc(e,t,a,l,o){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:o}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=l,d.tail=a,d.tailMode=o)}function oh(e,t,a){var l=t.pendingProps,o=l.revealOrder,d=l.tail;if(Fe(e,t,l.children,a),l=Pe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ch(e,a,t);else if(e.tag===19)ch(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(F(Pe,l),o){case"forwards":for(a=t.child,o=null;a!==null;)e=a.alternate,e!==null&&cs(e)===null&&(o=a),a=a.sibling;a=o,a===null?(o=t.child,t.child=null):(o=a.sibling,a.sibling=null),Bc(t,!1,o,a,d);break;case"backwards":for(a=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&cs(e)===null){t.child=o;break}e=o.sibling,o.sibling=a,a=o,o=e}Bc(t,!0,a,null,d);break;case"together":Bc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function sa(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ra|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Tl(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,a=Wt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Wt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function qc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Pr(e)))}function py(e,t,a){switch(t.tag){case 3:_e(t,t.stateNode.containerInfo),va(t,Xe,e.memoizedState.cache),jl();break;case 27:case 5:cl(t);break;case 4:_e(t,t.stateNode.containerInfo);break;case 10:va(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(ja(t),t.flags|=128,null):(a&t.child.childLanes)!==0?ih(e,t,a):(ja(t),e=sa(e,t,a),e!==null?e.sibling:null);ja(t);break;case 19:var o=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(Tl(e,t,a,!1),l=(a&t.childLanes)!==0),o){if(l)return oh(e,t,a);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),F(Pe,Pe.current),l)break;return null;case 22:case 23:return t.lanes=0,ah(e,t,a);case 24:va(t,Xe,e.memoizedState.cache)}return sa(e,t,a)}function uh(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ze=!0;else{if(!qc(e,a)&&(t.flags&128)===0)return Ze=!1,py(e,t,a);Ze=(e.flags&131072)!==0}else Ze=!1,Se&&(t.flags&1048576)!==0&&Bd(t,Xr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,o=l._init;if(l=o(l._payload),t.type=l,typeof l=="function")Ki(l)?(e=nn(l,e),t.tag=1,t=rh(null,t,l,e,a)):(t.tag=0,t=Mc(null,t,l,e,a));else{if(l!=null){if(o=l.$$typeof,o===z){t.tag=11,t=Wf(null,t,l,e,a);break e}else if(o===se){t.tag=14,t=eh(null,t,l,e,a);break e}}throw t=J(l)||l,Error(c(306,t,""))}}return t;case 0:return Mc(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,o=nn(l,t.pendingProps),rh(e,t,l,o,a);case 3:e:{if(_e(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var d=t.memoizedState;o=d.element,oc(e,t),Ml(t,l,null,a);var m=t.memoizedState;if(l=m.cache,va(t,Xe,l),l!==d.cache&&nc(t,[Xe],a,!0),Al(),l=m.element,d.isDehydrated)if(d={element:l,isDehydrated:!1,cache:m.cache},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){t=sh(e,t,l,a);break e}else if(l!==o){o=Ct(Error(c(424)),t),El(o),t=sh(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(He=Ht(e.firstChild),st=t,Se=!0,Fa=null,Xt=!0,a=Gf(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(jl(),l===o){t=sa(e,t,a);break e}Fe(e,t,l,a)}t=t.child}return t;case 26:return ds(e,t),e===null?(a=mm(t.type,null,t.pendingProps,null))?t.memoizedState=a:Se||(a=t.type,e=t.pendingProps,l=Ts(ee.current).createElement(a),l[tt]=t,l[it]=e,We(l,a,e),Qe(l),t.stateNode=l):t.memoizedState=mm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return cl(t),e===null&&Se&&(l=t.stateNode=dm(t.type,t.pendingProps,ee.current),st=t,Xt=!0,o=He,ka(t.type)?(No=o,He=Ht(l.firstChild)):He=o),Fe(e,t,t.pendingProps.children,a),ds(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Se&&((o=l=He)&&(l=$y(l,t.type,t.pendingProps,Xt),l!==null?(t.stateNode=l,st=t,He=Ht(l.firstChild),Xt=!1,o=!0):o=!1),o||Ja(t)),cl(t),o=t.type,d=t.pendingProps,m=e!==null?e.memoizedProps:null,l=d.children,yo(o,d)?l=null:m!==null&&yo(o,m)&&(t.flags|=32),t.memoizedState!==null&&(o=pc(e,t,iy,null,null,a),tr._currentValue=o),ds(e,t),Fe(e,t,l,a),t.child;case 6:return e===null&&Se&&((e=a=He)&&(a=Vy(a,t.pendingProps,Xt),a!==null?(t.stateNode=a,st=t,He=null,e=!0):e=!1),e||Ja(t)),null;case 13:return ih(e,t,a);case 4:return _e(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Bn(t,null,l,a):Fe(e,t,l,a),t.child;case 11:return Wf(e,t,t.type,t.pendingProps,a);case 7:return Fe(e,t,t.pendingProps,a),t.child;case 8:return Fe(e,t,t.pendingProps.children,a),t.child;case 12:return Fe(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,va(t,t.type,l.value),Fe(e,t,l.children,a),t.child;case 9:return o=t.type._context,l=t.pendingProps.children,en(t),o=at(o),l=l(o),t.flags|=1,Fe(e,t,l,a),t.child;case 14:return eh(e,t,t.type,t.pendingProps,a);case 15:return th(e,t,t.type,t.pendingProps,a);case 19:return oh(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=fs(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Wt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return ah(e,t,a);case 24:return en(t),l=at(Xe),e===null?(o=sc(),o===null&&(o=Me,d=lc(),o.pooledCache=d,d.refCount++,d!==null&&(o.pooledCacheLanes|=a),o=d),t.memoizedState={parent:l,cache:o},cc(t),va(t,Xe,o)):((e.lanes&a)!==0&&(oc(e,t),Ml(t,null,null,a),Al()),o=e.memoizedState,d=t.memoizedState,o.parent!==l?(o={parent:l,cache:l},t.memoizedState=o,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=o),va(t,Xe,l)):(l=d.cache,va(t,Xe,l),l!==o.cache&&nc(t,[Xe],a,!0))),Fe(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function ia(e){e.flags|=4}function dh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!vm(t)){if(t=_t.current,t!==null&&((Ne&4194048)===Ne?Pt!==null:(Ne&62914560)!==Ne&&(Ne&536870912)===0||t!==Pt))throw Rl=ic,Qd;e.flags|=8192}}function hs(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Gu():536870912,e.lanes|=t,$n|=t)}function ql(e,t){if(!Se)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var o=e.child;o!==null;)a|=o.lanes|o.childLanes,l|=o.subtreeFlags&65011712,l|=o.flags&65011712,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)a|=o.lanes|o.childLanes,l|=o.subtreeFlags,l|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function gy(e,t,a){var l=t.pendingProps;switch(Wi(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return ze(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),na(Xe),$t(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Sl(t)?ia(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Gd())),ze(t),null;case 26:return a=t.memoizedState,e===null?(ia(t),a!==null?(ze(t),dh(t,a)):(ze(t),t.flags&=-16777217)):a?a!==e.memoizedState?(ia(t),ze(t),dh(t,a)):(ze(t),t.flags&=-16777217):(e.memoizedProps!==l&&ia(t),ze(t),t.flags&=-16777217),null;case 27:Ga(t),a=ee.current;var o=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&ia(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return ze(t),null}e=P.current,Sl(t)?qd(t):(e=dm(o,l,a),t.stateNode=e,ia(t))}return ze(t),null;case 5:if(Ga(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&ia(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return ze(t),null}if(e=P.current,Sl(t))qd(t);else{switch(o=Ts(ee.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?o.createElement("select",{is:l.is}):o.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?o.createElement(a,{is:l.is}):o.createElement(a)}}e[tt]=t,e[it]=l;e:for(o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;o.sibling===null;){if(o.return===null||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(We(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ia(t)}}return ze(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&ia(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=ee.current,Sl(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,o=st,o!==null)switch(o.tag){case 27:case 5:l=o.memoizedProps}e[tt]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||lm(e.nodeValue,a)),e||Ja(t)}else e=Ts(e).createTextNode(l),e[tt]=t,t.stateNode=e}return ze(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(o=Sl(t),l!==null&&l.dehydrated!==null){if(e===null){if(!o)throw Error(c(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(c(317));o[tt]=t}else jl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ze(t),o=!1}else o=Gd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=o),o=!0;if(!o)return t.flags&256?(ra(t),t):(ra(t),null)}if(ra(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,o=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(o=l.alternate.memoizedState.cachePool.pool);var d=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(d=l.memoizedState.cachePool.pool),d!==o&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),hs(t,t.updateQueue),ze(t),null;case 4:return $t(),e===null&&fo(t.stateNode.containerInfo),ze(t),null;case 10:return na(t.type),ze(t),null;case 19:if(W(Pe),o=t.memoizedState,o===null)return ze(t),null;if(l=(t.flags&128)!==0,d=o.rendering,d===null)if(l)ql(o,!1);else{if(Be!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(d=cs(e),d!==null){for(t.flags|=128,ql(o,!1),e=d.updateQueue,t.updateQueue=e,hs(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Hd(a,e),a=a.sibling;return F(Pe,Pe.current&1|2),t.child}e=e.sibling}o.tail!==null&&Vt()>gs&&(t.flags|=128,l=!0,ql(o,!1),t.lanes=4194304)}else{if(!l)if(e=cs(d),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,hs(t,e),ql(o,!0),o.tail===null&&o.tailMode==="hidden"&&!d.alternate&&!Se)return ze(t),null}else 2*Vt()-o.renderingStartTime>gs&&a!==536870912&&(t.flags|=128,l=!0,ql(o,!1),t.lanes=4194304);o.isBackwards?(d.sibling=t.child,t.child=d):(e=o.last,e!==null?e.sibling=d:t.child=d,o.last=d)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Vt(),t.sibling=null,e=Pe.current,F(Pe,l?e&1|2:e&1),t):(ze(t),null);case 22:case 23:return ra(t),hc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),a=t.updateQueue,a!==null&&hs(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&W(tn),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),na(Xe),ze(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function yy(e,t){switch(Wi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return na(Xe),$t(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ga(t),null;case 13:if(ra(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));jl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(Pe),null;case 4:return $t(),null;case 10:return na(t.type),null;case 22:case 23:return ra(t),hc(),e!==null&&W(tn),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return na(Xe),null;case 25:return null;default:return null}}function fh(e,t){switch(Wi(t),t.tag){case 3:na(Xe),$t();break;case 26:case 27:case 5:Ga(t);break;case 4:$t();break;case 13:ra(t);break;case 19:W(Pe);break;case 10:na(t.type);break;case 22:case 23:ra(t),hc(),e!==null&&W(tn);break;case 24:na(Xe)}}function Yl(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var o=l.next;a=o;do{if((a.tag&e)===e){l=void 0;var d=a.create,m=a.inst;l=d(),m.destroy=l}a=a.next}while(a!==o)}}catch(v){Ae(t,t.return,v)}}function Ta(e,t,a){try{var l=t.updateQueue,o=l!==null?l.lastEffect:null;if(o!==null){var d=o.next;l=d;do{if((l.tag&e)===e){var m=l.inst,v=m.destroy;if(v!==void 0){m.destroy=void 0,o=t;var j=a,M=v;try{M()}catch(q){Ae(o,j,q)}}}l=l.next}while(l!==d)}}catch(q){Ae(t,t.return,q)}}function hh(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Wd(t,a)}catch(l){Ae(e,e.return,l)}}}function mh(e,t,a){a.props=nn(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Ae(e,t,l)}}function Gl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(o){Ae(e,t,o)}}function Qt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(o){Ae(e,t,o)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(o){Ae(e,t,o)}else a.current=null}function ph(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(o){Ae(e,e.return,o)}}function Yc(e,t,a){try{var l=e.stateNode;Hy(l,e.type,a,t),l[it]=t}catch(o){Ae(e,e.return,o)}}function gh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ka(e.type)||e.tag===4}function Gc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||gh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ka(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function $c(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Es));else if(l!==4&&(l===27&&ka(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for($c(e,t,a),e=e.sibling;e!==null;)$c(e,t,a),e=e.sibling}function ms(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&ka(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(ms(e,t,a),e=e.sibling;e!==null;)ms(e,t,a),e=e.sibling}function yh(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);We(t,l,a),t[tt]=e,t[it]=a}catch(d){Ae(e,e.return,d)}}var ca=!1,Ye=!1,Vc=!1,xh=typeof WeakSet=="function"?WeakSet:Set,Ke=null;function xy(e,t){if(e=e.containerInfo,po=As,e=Dd(e),Gi(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var o=l.anchorOffset,d=l.focusNode;l=l.focusOffset;try{a.nodeType,d.nodeType}catch{a=null;break e}var m=0,v=-1,j=-1,M=0,q=0,V=e,U=null;t:for(;;){for(var L;V!==a||o!==0&&V.nodeType!==3||(v=m+o),V!==d||l!==0&&V.nodeType!==3||(j=m+l),V.nodeType===3&&(m+=V.nodeValue.length),(L=V.firstChild)!==null;)U=V,V=L;for(;;){if(V===e)break t;if(U===a&&++M===o&&(v=m),U===d&&++q===l&&(j=m),(L=V.nextSibling)!==null)break;V=U,U=V.parentNode}V=L}a=v===-1||j===-1?null:{start:v,end:j}}else a=null}a=a||{start:0,end:0}}else a=null;for(go={focusedElem:e,selectionRange:a},As=!1,Ke=t;Ke!==null;)if(t=Ke,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ke=e;else for(;Ke!==null;){switch(t=Ke,d=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&d!==null){e=void 0,a=t,o=d.memoizedProps,d=d.memoizedState,l=a.stateNode;try{var de=nn(a.type,o,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(de,d),l.__reactInternalSnapshotBeforeUpdate=e}catch(ce){Ae(a,a.return,ce)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)vo(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":vo(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Ke=e;break}Ke=t.return}}function vh(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Ca(e,a),l&4&&Yl(5,a);break;case 1:if(Ca(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(m){Ae(a,a.return,m)}else{var o=nn(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(m){Ae(a,a.return,m)}}l&64&&hh(a),l&512&&Gl(a,a.return);break;case 3:if(Ca(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Wd(e,t)}catch(m){Ae(a,a.return,m)}}break;case 27:t===null&&l&4&&yh(a);case 26:case 5:Ca(e,a),t===null&&l&4&&ph(a),l&512&&Gl(a,a.return);break;case 12:Ca(e,a);break;case 13:Ca(e,a),l&4&&wh(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Cy.bind(null,a),Xy(e,a))));break;case 22:if(l=a.memoizedState!==null||ca,!l){t=t!==null&&t.memoizedState!==null||Ye,o=ca;var d=Ye;ca=l,(Ye=t)&&!d?Da(e,a,(a.subtreeFlags&8772)!==0):Ca(e,a),ca=o,Ye=d}break;case 30:break;default:Ca(e,a)}}function bh(e){var t=e.alternate;t!==null&&(e.alternate=null,bh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Si(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ke=null,ut=!1;function oa(e,t,a){for(a=a.child;a!==null;)Nh(e,t,a),a=a.sibling}function Nh(e,t,a){if(pt&&typeof pt.onCommitFiberUnmount=="function")try{pt.onCommitFiberUnmount(ol,a)}catch{}switch(a.tag){case 26:Ye||Qt(a,t),oa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ye||Qt(a,t);var l=ke,o=ut;ka(a.type)&&(ke=a.stateNode,ut=!1),oa(e,t,a),Fl(a.stateNode),ke=l,ut=o;break;case 5:Ye||Qt(a,t);case 6:if(l=ke,o=ut,ke=null,oa(e,t,a),ke=l,ut=o,ke!==null)if(ut)try{(ke.nodeType===9?ke.body:ke.nodeName==="HTML"?ke.ownerDocument.body:ke).removeChild(a.stateNode)}catch(d){Ae(a,t,d)}else try{ke.removeChild(a.stateNode)}catch(d){Ae(a,t,d)}break;case 18:ke!==null&&(ut?(e=ke,om(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),rr(e)):om(ke,a.stateNode));break;case 4:l=ke,o=ut,ke=a.stateNode.containerInfo,ut=!0,oa(e,t,a),ke=l,ut=o;break;case 0:case 11:case 14:case 15:Ye||Ta(2,a,t),Ye||Ta(4,a,t),oa(e,t,a);break;case 1:Ye||(Qt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&mh(a,t,l)),oa(e,t,a);break;case 21:oa(e,t,a);break;case 22:Ye=(l=Ye)||a.memoizedState!==null,oa(e,t,a),Ye=l;break;default:oa(e,t,a)}}function wh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{rr(e)}catch(a){Ae(t,t.return,a)}}function vy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new xh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new xh),t;default:throw Error(c(435,e.tag))}}function Xc(e,t){var a=vy(e);t.forEach(function(l){var o=Dy.bind(null,e,l);a.has(l)||(a.add(l),l.then(o,o))})}function vt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var o=a[l],d=e,m=t,v=m;e:for(;v!==null;){switch(v.tag){case 27:if(ka(v.type)){ke=v.stateNode,ut=!1;break e}break;case 5:ke=v.stateNode,ut=!1;break e;case 3:case 4:ke=v.stateNode.containerInfo,ut=!0;break e}v=v.return}if(ke===null)throw Error(c(160));Nh(d,m,o),ke=null,ut=!1,d=o.alternate,d!==null&&(d.return=null),o.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Sh(t,e),t=t.sibling}var Lt=null;function Sh(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:vt(t,e),bt(e),l&4&&(Ta(3,e,e.return),Yl(3,e),Ta(5,e,e.return));break;case 1:vt(t,e),bt(e),l&512&&(Ye||a===null||Qt(a,a.return)),l&64&&ca&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var o=Lt;if(vt(t,e),bt(e),l&512&&(Ye||a===null||Qt(a,a.return)),l&4){var d=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,o=o.ownerDocument||o;t:switch(l){case"title":d=o.getElementsByTagName("title")[0],(!d||d[fl]||d[tt]||d.namespaceURI==="http://www.w3.org/2000/svg"||d.hasAttribute("itemprop"))&&(d=o.createElement(l),o.head.insertBefore(d,o.querySelector("head > title"))),We(d,l,a),d[tt]=e,Qe(d),l=d;break e;case"link":var m=ym("link","href",o).get(l+(a.href||""));if(m){for(var v=0;v<m.length;v++)if(d=m[v],d.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&d.getAttribute("rel")===(a.rel==null?null:a.rel)&&d.getAttribute("title")===(a.title==null?null:a.title)&&d.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){m.splice(v,1);break t}}d=o.createElement(l),We(d,l,a),o.head.appendChild(d);break;case"meta":if(m=ym("meta","content",o).get(l+(a.content||""))){for(v=0;v<m.length;v++)if(d=m[v],d.getAttribute("content")===(a.content==null?null:""+a.content)&&d.getAttribute("name")===(a.name==null?null:a.name)&&d.getAttribute("property")===(a.property==null?null:a.property)&&d.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&d.getAttribute("charset")===(a.charSet==null?null:a.charSet)){m.splice(v,1);break t}}d=o.createElement(l),We(d,l,a),o.head.appendChild(d);break;default:throw Error(c(468,l))}d[tt]=e,Qe(d),l=d}e.stateNode=l}else xm(o,e.type,e.stateNode);else e.stateNode=gm(o,l,e.memoizedProps);else d!==l?(d===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):d.count--,l===null?xm(o,e.type,e.stateNode):gm(o,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Yc(e,e.memoizedProps,a.memoizedProps)}break;case 27:vt(t,e),bt(e),l&512&&(Ye||a===null||Qt(a,a.return)),a!==null&&l&4&&Yc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(vt(t,e),bt(e),l&512&&(Ye||a===null||Qt(a,a.return)),e.flags&32){o=e.stateNode;try{Nn(o,"")}catch(L){Ae(e,e.return,L)}}l&4&&e.stateNode!=null&&(o=e.memoizedProps,Yc(e,o,a!==null?a.memoizedProps:o)),l&1024&&(Vc=!0);break;case 6:if(vt(t,e),bt(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(L){Ae(e,e.return,L)}}break;case 3:if(Os=null,o=Lt,Lt=Cs(t.containerInfo),vt(t,e),Lt=o,bt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{rr(t.containerInfo)}catch(L){Ae(e,e.return,L)}Vc&&(Vc=!1,jh(e));break;case 4:l=Lt,Lt=Cs(e.stateNode.containerInfo),vt(t,e),bt(e),Lt=l;break;case 12:vt(t,e),bt(e);break;case 13:vt(t,e),bt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Fc=Vt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Xc(e,l)));break;case 22:o=e.memoizedState!==null;var j=a!==null&&a.memoizedState!==null,M=ca,q=Ye;if(ca=M||o,Ye=q||j,vt(t,e),Ye=q,ca=M,bt(e),l&8192)e:for(t=e.stateNode,t._visibility=o?t._visibility&-2:t._visibility|1,o&&(a===null||j||ca||Ye||ln(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){j=a=t;try{if(d=j.stateNode,o)m=d.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{v=j.stateNode;var V=j.memoizedProps.style,U=V!=null&&V.hasOwnProperty("display")?V.display:null;v.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(L){Ae(j,j.return,L)}}}else if(t.tag===6){if(a===null){j=t;try{j.stateNode.nodeValue=o?"":j.memoizedProps}catch(L){Ae(j,j.return,L)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Xc(e,a))));break;case 19:vt(t,e),bt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Xc(e,l)));break;case 30:break;case 21:break;default:vt(t,e),bt(e)}}function bt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(gh(l)){a=l;break}l=l.return}if(a==null)throw Error(c(160));switch(a.tag){case 27:var o=a.stateNode,d=Gc(e);ms(e,d,o);break;case 5:var m=a.stateNode;a.flags&32&&(Nn(m,""),a.flags&=-33);var v=Gc(e);ms(e,v,m);break;case 3:case 4:var j=a.stateNode.containerInfo,M=Gc(e);$c(e,M,j);break;default:throw Error(c(161))}}catch(q){Ae(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function jh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;jh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Ca(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)vh(e,t.alternate,t),t=t.sibling}function ln(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ta(4,t,t.return),ln(t);break;case 1:Qt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&mh(t,t.return,a),ln(t);break;case 27:Fl(t.stateNode);case 26:case 5:Qt(t,t.return),ln(t);break;case 22:t.memoizedState===null&&ln(t);break;case 30:ln(t);break;default:ln(t)}e=e.sibling}}function Da(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,o=e,d=t,m=d.flags;switch(d.tag){case 0:case 11:case 15:Da(o,d,a),Yl(4,d);break;case 1:if(Da(o,d,a),l=d,o=l.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(M){Ae(l,l.return,M)}if(l=d,o=l.updateQueue,o!==null){var v=l.stateNode;try{var j=o.shared.hiddenCallbacks;if(j!==null)for(o.shared.hiddenCallbacks=null,o=0;o<j.length;o++)Jd(j[o],v)}catch(M){Ae(l,l.return,M)}}a&&m&64&&hh(d),Gl(d,d.return);break;case 27:yh(d);case 26:case 5:Da(o,d,a),a&&l===null&&m&4&&ph(d),Gl(d,d.return);break;case 12:Da(o,d,a);break;case 13:Da(o,d,a),a&&m&4&&wh(o,d);break;case 22:d.memoizedState===null&&Da(o,d,a),Gl(d,d.return);break;case 30:break;default:Da(o,d,a)}t=t.sibling}}function Pc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Cl(a))}function Qc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cl(e))}function Zt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Eh(e,t,a,l),t=t.sibling}function Eh(e,t,a,l){var o=t.flags;switch(t.tag){case 0:case 11:case 15:Zt(e,t,a,l),o&2048&&Yl(9,t);break;case 1:Zt(e,t,a,l);break;case 3:Zt(e,t,a,l),o&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cl(e)));break;case 12:if(o&2048){Zt(e,t,a,l),e=t.stateNode;try{var d=t.memoizedProps,m=d.id,v=d.onPostCommit;typeof v=="function"&&v(m,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(j){Ae(t,t.return,j)}}else Zt(e,t,a,l);break;case 13:Zt(e,t,a,l);break;case 23:break;case 22:d=t.stateNode,m=t.alternate,t.memoizedState!==null?d._visibility&2?Zt(e,t,a,l):$l(e,t):d._visibility&2?Zt(e,t,a,l):(d._visibility|=2,qn(e,t,a,l,(t.subtreeFlags&10256)!==0)),o&2048&&Pc(m,t);break;case 24:Zt(e,t,a,l),o&2048&&Qc(t.alternate,t);break;default:Zt(e,t,a,l)}}function qn(e,t,a,l,o){for(o=o&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var d=e,m=t,v=a,j=l,M=m.flags;switch(m.tag){case 0:case 11:case 15:qn(d,m,v,j,o),Yl(8,m);break;case 23:break;case 22:var q=m.stateNode;m.memoizedState!==null?q._visibility&2?qn(d,m,v,j,o):$l(d,m):(q._visibility|=2,qn(d,m,v,j,o)),o&&M&2048&&Pc(m.alternate,m);break;case 24:qn(d,m,v,j,o),o&&M&2048&&Qc(m.alternate,m);break;default:qn(d,m,v,j,o)}t=t.sibling}}function $l(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,o=l.flags;switch(l.tag){case 22:$l(a,l),o&2048&&Pc(l.alternate,l);break;case 24:$l(a,l),o&2048&&Qc(l.alternate,l);break;default:$l(a,l)}t=t.sibling}}var Vl=8192;function Yn(e){if(e.subtreeFlags&Vl)for(e=e.child;e!==null;)Th(e),e=e.sibling}function Th(e){switch(e.tag){case 26:Yn(e),e.flags&Vl&&e.memoizedState!==null&&lx(Lt,e.memoizedState,e.memoizedProps);break;case 5:Yn(e);break;case 3:case 4:var t=Lt;Lt=Cs(e.stateNode.containerInfo),Yn(e),Lt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Vl,Vl=16777216,Yn(e),Vl=t):Yn(e));break;default:Yn(e)}}function Ch(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Xl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ke=l,Oh(l,e)}Ch(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Dh(e),e=e.sibling}function Dh(e){switch(e.tag){case 0:case 11:case 15:Xl(e),e.flags&2048&&Ta(9,e,e.return);break;case 3:Xl(e);break;case 12:Xl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ps(e)):Xl(e);break;default:Xl(e)}}function ps(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ke=l,Oh(l,e)}Ch(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ta(8,t,t.return),ps(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,ps(t));break;default:ps(t)}e=e.sibling}}function Oh(e,t){for(;Ke!==null;){var a=Ke;switch(a.tag){case 0:case 11:case 15:Ta(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Cl(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ke=l;else e:for(a=e;Ke!==null;){l=Ke;var o=l.sibling,d=l.return;if(bh(l),l===a){Ke=null;break e}if(o!==null){o.return=d,Ke=o;break e}Ke=d}}}var by={getCacheForType:function(e){var t=at(Xe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Ny=typeof WeakMap=="function"?WeakMap:Map,Ee=0,Me=null,ye=null,Ne=0,Te=0,Nt=null,Oa=!1,Gn=!1,Zc=!1,ua=0,Be=0,Ra=0,rn=0,Kc=0,At=0,$n=0,Pl=null,dt=null,Ic=!1,Fc=0,gs=1/0,ys=null,_a=null,Je=0,Aa=null,Vn=null,Xn=0,Jc=0,Wc=null,Rh=null,Ql=0,eo=null;function wt(){if((Ee&2)!==0&&Ne!==0)return Ne&-Ne;if(B.T!==null){var e=An;return e!==0?e:io()}return Xu()}function _h(){At===0&&(At=(Ne&536870912)===0||Se?Yu():536870912);var e=_t.current;return e!==null&&(e.flags|=32),At}function St(e,t,a){(e===Me&&(Te===2||Te===9)||e.cancelPendingCommit!==null)&&(Pn(e,0),Ma(e,Ne,At,!1)),dl(e,a),((Ee&2)===0||e!==Me)&&(e===Me&&((Ee&2)===0&&(rn|=a),Be===4&&Ma(e,Ne,At,!1)),Kt(e))}function Ah(e,t,a){if((Ee&6)!==0)throw Error(c(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||ul(e,t),o=l?jy(e,t):no(e,t,!0),d=l;do{if(o===0){Gn&&!l&&Ma(e,t,0,!1);break}else{if(a=e.current.alternate,d&&!wy(a)){o=no(e,t,!1),d=!1;continue}if(o===2){if(d=t,e.errorRecoveryDisabledLanes&d)var m=0;else m=e.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){t=m;e:{var v=e;o=Pl;var j=v.current.memoizedState.isDehydrated;if(j&&(Pn(v,m).flags|=256),m=no(v,m,!1),m!==2){if(Zc&&!j){v.errorRecoveryDisabledLanes|=d,rn|=d,o=4;break e}d=dt,dt=o,d!==null&&(dt===null?dt=d:dt.push.apply(dt,d))}o=m}if(d=!1,o!==2)continue}}if(o===1){Pn(e,0),Ma(e,t,0,!0);break}e:{switch(l=e,d=o,d){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Ma(l,t,At,!Oa);break e;case 2:dt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(o=Fc+300-Vt(),10<o)){if(Ma(l,t,At,!Oa),Dr(l,0,!0)!==0)break e;l.timeoutHandle=im(Mh.bind(null,l,a,dt,ys,Ic,t,At,rn,$n,Oa,d,2,-0,0),o);break e}Mh(l,a,dt,ys,Ic,t,At,rn,$n,Oa,d,0,-0,0)}}break}while(!0);Kt(e)}function Mh(e,t,a,l,o,d,m,v,j,M,q,V,U,L){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(er={stylesheets:null,count:0,unsuspend:nx},Th(t),V=rx(),V!==null)){e.cancelPendingCommit=V(qh.bind(null,e,t,d,a,l,o,m,v,j,q,1,U,L)),Ma(e,d,m,!M);return}qh(e,t,d,a,l,o,m,v,j)}function wy(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var o=a[l],d=o.getSnapshot;o=o.value;try{if(!yt(d(),o))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ma(e,t,a,l){t&=~Kc,t&=~rn,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var o=t;0<o;){var d=31-gt(o),m=1<<d;l[d]=-1,o&=~m}a!==0&&$u(e,a,t)}function xs(){return(Ee&6)===0?(Zl(0),!1):!0}function to(){if(ye!==null){if(Te===0)var e=ye.return;else e=ye,aa=Wa=null,xc(e),Hn=null,Hl=0,e=ye;for(;e!==null;)fh(e.alternate,e),e=e.return;ye=null}}function Pn(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,qy(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),to(),Me=e,ye=a=Wt(e.current,null),Ne=t,Te=0,Nt=null,Oa=!1,Gn=ul(e,t),Zc=!1,$n=At=Kc=rn=Ra=Be=0,dt=Pl=null,Ic=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var o=31-gt(l),d=1<<o;t|=e[o],l&=~d}return ua=t,qr(),a}function kh(e,t){pe=null,B.H=rs,t===Ol||t===Kr?(t=Id(),Te=3):t===Qd?(t=Id(),Te=4):Te=t===Jf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Nt=t,ye===null&&(Be=1,us(e,Ct(t,e.current)))}function Uh(){var e=B.H;return B.H=rs,e===null?rs:e}function zh(){var e=B.A;return B.A=by,e}function ao(){Be=4,Oa||(Ne&4194048)!==Ne&&_t.current!==null||(Gn=!0),(Ra&134217727)===0&&(rn&134217727)===0||Me===null||Ma(Me,Ne,At,!1)}function no(e,t,a){var l=Ee;Ee|=2;var o=Uh(),d=zh();(Me!==e||Ne!==t)&&(ys=null,Pn(e,t)),t=!1;var m=Be;e:do try{if(Te!==0&&ye!==null){var v=ye,j=Nt;switch(Te){case 8:to(),m=6;break e;case 3:case 2:case 9:case 6:_t.current===null&&(t=!0);var M=Te;if(Te=0,Nt=null,Qn(e,v,j,M),a&&Gn){m=0;break e}break;default:M=Te,Te=0,Nt=null,Qn(e,v,j,M)}}Sy(),m=Be;break}catch(q){kh(e,q)}while(!0);return t&&e.shellSuspendCounter++,aa=Wa=null,Ee=l,B.H=o,B.A=d,ye===null&&(Me=null,Ne=0,qr()),m}function Sy(){for(;ye!==null;)Lh(ye)}function jy(e,t){var a=Ee;Ee|=2;var l=Uh(),o=zh();Me!==e||Ne!==t?(ys=null,gs=Vt()+500,Pn(e,t)):Gn=ul(e,t);e:do try{if(Te!==0&&ye!==null){t=ye;var d=Nt;t:switch(Te){case 1:Te=0,Nt=null,Qn(e,t,d,1);break;case 2:case 9:if(Zd(d)){Te=0,Nt=null,Hh(t);break}t=function(){Te!==2&&Te!==9||Me!==e||(Te=7),Kt(e)},d.then(t,t);break e;case 3:Te=7;break e;case 4:Te=5;break e;case 7:Zd(d)?(Te=0,Nt=null,Hh(t)):(Te=0,Nt=null,Qn(e,t,d,7));break;case 5:var m=null;switch(ye.tag){case 26:m=ye.memoizedState;case 5:case 27:var v=ye;if(!m||vm(m)){Te=0,Nt=null;var j=v.sibling;if(j!==null)ye=j;else{var M=v.return;M!==null?(ye=M,vs(M)):ye=null}break t}}Te=0,Nt=null,Qn(e,t,d,5);break;case 6:Te=0,Nt=null,Qn(e,t,d,6);break;case 8:to(),Be=6;break e;default:throw Error(c(462))}}Ey();break}catch(q){kh(e,q)}while(!0);return aa=Wa=null,B.H=l,B.A=o,Ee=a,ye!==null?0:(Me=null,Ne=0,qr(),Be)}function Ey(){for(;ye!==null&&!Qg();)Lh(ye)}function Lh(e){var t=uh(e.alternate,e,ua);e.memoizedProps=e.pendingProps,t===null?vs(e):ye=t}function Hh(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=lh(a,t,t.pendingProps,t.type,void 0,Ne);break;case 11:t=lh(a,t,t.pendingProps,t.type.render,t.ref,Ne);break;case 5:xc(t);default:fh(a,t),t=ye=Hd(t,ua),t=uh(a,t,ua)}e.memoizedProps=e.pendingProps,t===null?vs(e):ye=t}function Qn(e,t,a,l){aa=Wa=null,xc(t),Hn=null,Hl=0;var o=t.return;try{if(my(e,o,t,a,Ne)){Be=1,us(e,Ct(a,e.current)),ye=null;return}}catch(d){if(o!==null)throw ye=o,d;Be=1,us(e,Ct(a,e.current)),ye=null;return}t.flags&32768?(Se||l===1?e=!0:Gn||(Ne&536870912)!==0?e=!1:(Oa=e=!0,(l===2||l===9||l===3||l===6)&&(l=_t.current,l!==null&&l.tag===13&&(l.flags|=16384))),Bh(t,e)):vs(t)}function vs(e){var t=e;do{if((t.flags&32768)!==0){Bh(t,Oa);return}e=t.return;var a=gy(t.alternate,t,ua);if(a!==null){ye=a;return}if(t=t.sibling,t!==null){ye=t;return}ye=t=e}while(t!==null);Be===0&&(Be=5)}function Bh(e,t){do{var a=yy(e.alternate,e);if(a!==null){a.flags&=32767,ye=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ye=e;return}ye=e=a}while(e!==null);Be=6,ye=null}function qh(e,t,a,l,o,d,m,v,j){e.cancelPendingCommit=null;do bs();while(Je!==0);if((Ee&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(d=t.lanes|t.childLanes,d|=Qi,n0(e,a,d,m,v,j),e===Me&&(ye=Me=null,Ne=0),Vn=t,Aa=e,Xn=a,Jc=d,Wc=o,Rh=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Oy(Er,function(){return Xh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=B.T,B.T=null,o=K.p,K.p=2,m=Ee,Ee|=4;try{xy(e,t,a)}finally{Ee=m,K.p=o,B.T=l}}Je=1,Yh(),Gh(),$h()}}function Yh(){if(Je===1){Je=0;var e=Aa,t=Vn,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=B.T,B.T=null;var l=K.p;K.p=2;var o=Ee;Ee|=4;try{Sh(t,e);var d=go,m=Dd(e.containerInfo),v=d.focusedElem,j=d.selectionRange;if(m!==v&&v&&v.ownerDocument&&Cd(v.ownerDocument.documentElement,v)){if(j!==null&&Gi(v)){var M=j.start,q=j.end;if(q===void 0&&(q=M),"selectionStart"in v)v.selectionStart=M,v.selectionEnd=Math.min(q,v.value.length);else{var V=v.ownerDocument||document,U=V&&V.defaultView||window;if(U.getSelection){var L=U.getSelection(),de=v.textContent.length,ce=Math.min(j.start,de),Oe=j.end===void 0?ce:Math.min(j.end,de);!L.extend&&ce>Oe&&(m=Oe,Oe=ce,ce=m);var _=Td(v,ce),O=Td(v,Oe);if(_&&O&&(L.rangeCount!==1||L.anchorNode!==_.node||L.anchorOffset!==_.offset||L.focusNode!==O.node||L.focusOffset!==O.offset)){var A=V.createRange();A.setStart(_.node,_.offset),L.removeAllRanges(),ce>Oe?(L.addRange(A),L.extend(O.node,O.offset)):(A.setEnd(O.node,O.offset),L.addRange(A))}}}}for(V=[],L=v;L=L.parentNode;)L.nodeType===1&&V.push({element:L,left:L.scrollLeft,top:L.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<V.length;v++){var G=V[v];G.element.scrollLeft=G.left,G.element.scrollTop=G.top}}As=!!po,go=po=null}finally{Ee=o,K.p=l,B.T=a}}e.current=t,Je=2}}function Gh(){if(Je===2){Je=0;var e=Aa,t=Vn,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=B.T,B.T=null;var l=K.p;K.p=2;var o=Ee;Ee|=4;try{vh(e,t.alternate,t)}finally{Ee=o,K.p=l,B.T=a}}Je=3}}function $h(){if(Je===4||Je===3){Je=0,Zg();var e=Aa,t=Vn,a=Xn,l=Rh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Je=5:(Je=0,Vn=Aa=null,Vh(e,e.pendingLanes));var o=e.pendingLanes;if(o===0&&(_a=null),Ni(a),t=t.stateNode,pt&&typeof pt.onCommitFiberRoot=="function")try{pt.onCommitFiberRoot(ol,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=B.T,o=K.p,K.p=2,B.T=null;try{for(var d=e.onRecoverableError,m=0;m<l.length;m++){var v=l[m];d(v.value,{componentStack:v.stack})}}finally{B.T=t,K.p=o}}(Xn&3)!==0&&bs(),Kt(e),o=e.pendingLanes,(a&4194090)!==0&&(o&42)!==0?e===eo?Ql++:(Ql=0,eo=e):Ql=0,Zl(0)}}function Vh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Cl(t)))}function bs(e){return Yh(),Gh(),$h(),Xh()}function Xh(){if(Je!==5)return!1;var e=Aa,t=Jc;Jc=0;var a=Ni(Xn),l=B.T,o=K.p;try{K.p=32>a?32:a,B.T=null,a=Wc,Wc=null;var d=Aa,m=Xn;if(Je=0,Vn=Aa=null,Xn=0,(Ee&6)!==0)throw Error(c(331));var v=Ee;if(Ee|=4,Dh(d.current),Eh(d,d.current,m,a),Ee=v,Zl(0,!1),pt&&typeof pt.onPostCommitFiberRoot=="function")try{pt.onPostCommitFiberRoot(ol,d)}catch{}return!0}finally{K.p=o,B.T=l,Vh(e,t)}}function Ph(e,t,a){t=Ct(a,t),t=Ac(e.stateNode,t,2),e=wa(e,t,2),e!==null&&(dl(e,2),Kt(e))}function Ae(e,t,a){if(e.tag===3)Ph(e,e,a);else for(;t!==null;){if(t.tag===3){Ph(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(_a===null||!_a.has(l))){e=Ct(a,e),a=If(2),l=wa(t,a,2),l!==null&&(Ff(a,l,t,e),dl(l,2),Kt(l));break}}t=t.return}}function lo(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Ny;var o=new Set;l.set(t,o)}else o=l.get(t),o===void 0&&(o=new Set,l.set(t,o));o.has(a)||(Zc=!0,o.add(a),e=Ty.bind(null,e,t,a),t.then(e,e))}function Ty(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Me===e&&(Ne&a)===a&&(Be===4||Be===3&&(Ne&62914560)===Ne&&300>Vt()-Fc?(Ee&2)===0&&Pn(e,0):Kc|=a,$n===Ne&&($n=0)),Kt(e)}function Qh(e,t){t===0&&(t=Gu()),e=Dn(e,t),e!==null&&(dl(e,t),Kt(e))}function Cy(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Qh(e,a)}function Dy(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,o=e.memoizedState;o!==null&&(a=o.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),Qh(e,a)}function Oy(e,t){return yi(e,t)}var Ns=null,Zn=null,ro=!1,ws=!1,so=!1,sn=0;function Kt(e){e!==Zn&&e.next===null&&(Zn===null?Ns=Zn=e:Zn=Zn.next=e),ws=!0,ro||(ro=!0,_y())}function Zl(e,t){if(!so&&ws){so=!0;do for(var a=!1,l=Ns;l!==null;){if(e!==0){var o=l.pendingLanes;if(o===0)var d=0;else{var m=l.suspendedLanes,v=l.pingedLanes;d=(1<<31-gt(42|e)+1)-1,d&=o&~(m&~v),d=d&201326741?d&201326741|1:d?d|2:0}d!==0&&(a=!0,Fh(l,d))}else d=Ne,d=Dr(l,l===Me?d:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(d&3)===0||ul(l,d)||(a=!0,Fh(l,d));l=l.next}while(a);so=!1}}function Ry(){Zh()}function Zh(){ws=ro=!1;var e=0;sn!==0&&(By()&&(e=sn),sn=0);for(var t=Vt(),a=null,l=Ns;l!==null;){var o=l.next,d=Kh(l,t);d===0?(l.next=null,a===null?Ns=o:a.next=o,o===null&&(Zn=a)):(a=l,(e!==0||(d&3)!==0)&&(ws=!0)),l=o}Zl(e)}function Kh(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,o=e.expirationTimes,d=e.pendingLanes&-62914561;0<d;){var m=31-gt(d),v=1<<m,j=o[m];j===-1?((v&a)===0||(v&l)!==0)&&(o[m]=a0(v,t)):j<=t&&(e.expiredLanes|=v),d&=~v}if(t=Me,a=Ne,a=Dr(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Te===2||Te===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&xi(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||ul(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&xi(l),Ni(a)){case 2:case 8:a=Bu;break;case 32:a=Er;break;case 268435456:a=qu;break;default:a=Er}return l=Ih.bind(null,e),a=yi(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&xi(l),e.callbackPriority=2,e.callbackNode=null,2}function Ih(e,t){if(Je!==0&&Je!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(bs()&&e.callbackNode!==a)return null;var l=Ne;return l=Dr(e,e===Me?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Ah(e,l,t),Kh(e,Vt()),e.callbackNode!=null&&e.callbackNode===a?Ih.bind(null,e):null)}function Fh(e,t){if(bs())return null;Ah(e,t,!0)}function _y(){Yy(function(){(Ee&6)!==0?yi(Hu,Ry):Zh()})}function io(){return sn===0&&(sn=Yu()),sn}function Jh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Mr(""+e)}function Wh(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ay(e,t,a,l,o){if(t==="submit"&&a&&a.stateNode===o){var d=Jh((o[it]||null).action),m=l.submitter;m&&(t=(t=m[it]||null)?Jh(t.formAction):m.getAttribute("formAction"),t!==null&&(d=t,m=null));var v=new Lr("action","action",null,l,o);e.push({event:v,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(sn!==0){var j=m?Wh(o,m):new FormData(o);Cc(a,{pending:!0,data:j,method:o.method,action:d},null,j)}}else typeof d=="function"&&(v.preventDefault(),j=m?Wh(o,m):new FormData(o),Cc(a,{pending:!0,data:j,method:o.method,action:d},d,j))},currentTarget:o}]})}}for(var co=0;co<Pi.length;co++){var oo=Pi[co],My=oo.toLowerCase(),ky=oo[0].toUpperCase()+oo.slice(1);zt(My,"on"+ky)}zt(_d,"onAnimationEnd"),zt(Ad,"onAnimationIteration"),zt(Md,"onAnimationStart"),zt("dblclick","onDoubleClick"),zt("focusin","onFocus"),zt("focusout","onBlur"),zt(F0,"onTransitionRun"),zt(J0,"onTransitionStart"),zt(W0,"onTransitionCancel"),zt(kd,"onTransitionEnd"),xn("onMouseEnter",["mouseout","mouseover"]),xn("onMouseLeave",["mouseout","mouseover"]),xn("onPointerEnter",["pointerout","pointerover"]),xn("onPointerLeave",["pointerout","pointerover"]),Va("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Va("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Va("onBeforeInput",["compositionend","keypress","textInput","paste"]),Va("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Va("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Va("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Uy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Kl));function em(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],o=l.event;l=l.listeners;e:{var d=void 0;if(t)for(var m=l.length-1;0<=m;m--){var v=l[m],j=v.instance,M=v.currentTarget;if(v=v.listener,j!==d&&o.isPropagationStopped())break e;d=v,o.currentTarget=M;try{d(o)}catch(q){os(q)}o.currentTarget=null,d=j}else for(m=0;m<l.length;m++){if(v=l[m],j=v.instance,M=v.currentTarget,v=v.listener,j!==d&&o.isPropagationStopped())break e;d=v,o.currentTarget=M;try{d(o)}catch(q){os(q)}o.currentTarget=null,d=j}}}}function xe(e,t){var a=t[wi];a===void 0&&(a=t[wi]=new Set);var l=e+"__bubble";a.has(l)||(tm(t,e,2,!1),a.add(l))}function uo(e,t,a){var l=0;t&&(l|=4),tm(a,e,l,t)}var Ss="_reactListening"+Math.random().toString(36).slice(2);function fo(e){if(!e[Ss]){e[Ss]=!0,Qu.forEach(function(a){a!=="selectionchange"&&(Uy.has(a)||uo(a,!1,e),uo(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ss]||(t[Ss]=!0,uo("selectionchange",!1,t))}}function tm(e,t,a,l){switch(Em(t)){case 2:var o=cx;break;case 8:o=ox;break;default:o=To}a=o.bind(null,t,a,e),o=void 0,!Mi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),l?o!==void 0?e.addEventListener(t,a,{capture:!0,passive:o}):e.addEventListener(t,a,!0):o!==void 0?e.addEventListener(t,a,{passive:o}):e.addEventListener(t,a,!1)}function ho(e,t,a,l,o){var d=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var m=l.tag;if(m===3||m===4){var v=l.stateNode.containerInfo;if(v===o)break;if(m===4)for(m=l.return;m!==null;){var j=m.tag;if((j===3||j===4)&&m.stateNode.containerInfo===o)return;m=m.return}for(;v!==null;){if(m=pn(v),m===null)return;if(j=m.tag,j===5||j===6||j===26||j===27){l=d=m;continue e}v=v.parentNode}}l=l.return}id(function(){var M=d,q=_i(a),V=[];e:{var U=Ud.get(e);if(U!==void 0){var L=Lr,de=e;switch(e){case"keypress":if(Ur(a)===0)break e;case"keydown":case"keyup":L=O0;break;case"focusin":de="focus",L=Li;break;case"focusout":de="blur",L=Li;break;case"beforeblur":case"afterblur":L=Li;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":L=ud;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":L=y0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":L=A0;break;case _d:case Ad:case Md:L=b0;break;case kd:L=k0;break;case"scroll":case"scrollend":L=p0;break;case"wheel":L=z0;break;case"copy":case"cut":case"paste":L=w0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":L=fd;break;case"toggle":case"beforetoggle":L=H0}var ce=(t&4)!==0,Oe=!ce&&(e==="scroll"||e==="scrollend"),_=ce?U!==null?U+"Capture":null:U;ce=[];for(var O=M,A;O!==null;){var G=O;if(A=G.stateNode,G=G.tag,G!==5&&G!==26&&G!==27||A===null||_===null||(G=ml(O,_),G!=null&&ce.push(Il(O,G,A))),Oe)break;O=O.return}0<ce.length&&(U=new L(U,de,null,a,q),V.push({event:U,listeners:ce}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",L=e==="mouseout"||e==="pointerout",U&&a!==Ri&&(de=a.relatedTarget||a.fromElement)&&(pn(de)||de[mn]))break e;if((L||U)&&(U=q.window===q?q:(U=q.ownerDocument)?U.defaultView||U.parentWindow:window,L?(de=a.relatedTarget||a.toElement,L=M,de=de?pn(de):null,de!==null&&(Oe=f(de),ce=de.tag,de!==Oe||ce!==5&&ce!==27&&ce!==6)&&(de=null)):(L=null,de=M),L!==de)){if(ce=ud,G="onMouseLeave",_="onMouseEnter",O="mouse",(e==="pointerout"||e==="pointerover")&&(ce=fd,G="onPointerLeave",_="onPointerEnter",O="pointer"),Oe=L==null?U:hl(L),A=de==null?U:hl(de),U=new ce(G,O+"leave",L,a,q),U.target=Oe,U.relatedTarget=A,G=null,pn(q)===M&&(ce=new ce(_,O+"enter",de,a,q),ce.target=A,ce.relatedTarget=Oe,G=ce),Oe=G,L&&de)t:{for(ce=L,_=de,O=0,A=ce;A;A=Kn(A))O++;for(A=0,G=_;G;G=Kn(G))A++;for(;0<O-A;)ce=Kn(ce),O--;for(;0<A-O;)_=Kn(_),A--;for(;O--;){if(ce===_||_!==null&&ce===_.alternate)break t;ce=Kn(ce),_=Kn(_)}ce=null}else ce=null;L!==null&&am(V,U,L,ce,!1),de!==null&&Oe!==null&&am(V,Oe,de,ce,!0)}}e:{if(U=M?hl(M):window,L=U.nodeName&&U.nodeName.toLowerCase(),L==="select"||L==="input"&&U.type==="file")var ae=bd;else if(xd(U))if(Nd)ae=Z0;else{ae=P0;var ge=X0}else L=U.nodeName,!L||L.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?M&&Oi(M.elementType)&&(ae=bd):ae=Q0;if(ae&&(ae=ae(e,M))){vd(V,ae,a,q);break e}ge&&ge(e,U,M),e==="focusout"&&M&&U.type==="number"&&M.memoizedProps.value!=null&&Di(U,"number",U.value)}switch(ge=M?hl(M):window,e){case"focusin":(xd(ge)||ge.contentEditable==="true")&&(En=ge,$i=M,wl=null);break;case"focusout":wl=$i=En=null;break;case"mousedown":Vi=!0;break;case"contextmenu":case"mouseup":case"dragend":Vi=!1,Od(V,a,q);break;case"selectionchange":if(I0)break;case"keydown":case"keyup":Od(V,a,q)}var re;if(Bi)e:{switch(e){case"compositionstart":var ue="onCompositionStart";break e;case"compositionend":ue="onCompositionEnd";break e;case"compositionupdate":ue="onCompositionUpdate";break e}ue=void 0}else jn?gd(e,a)&&(ue="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ue="onCompositionStart");ue&&(hd&&a.locale!=="ko"&&(jn||ue!=="onCompositionStart"?ue==="onCompositionEnd"&&jn&&(re=cd()):(xa=q,ki="value"in xa?xa.value:xa.textContent,jn=!0)),ge=js(M,ue),0<ge.length&&(ue=new dd(ue,e,null,a,q),V.push({event:ue,listeners:ge}),re?ue.data=re:(re=yd(a),re!==null&&(ue.data=re)))),(re=q0?Y0(e,a):G0(e,a))&&(ue=js(M,"onBeforeInput"),0<ue.length&&(ge=new dd("onBeforeInput","beforeinput",null,a,q),V.push({event:ge,listeners:ue}),ge.data=re)),Ay(V,e,M,a,q)}em(V,t)})}function Il(e,t,a){return{instance:e,listener:t,currentTarget:a}}function js(e,t){for(var a=t+"Capture",l=[];e!==null;){var o=e,d=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||d===null||(o=ml(e,a),o!=null&&l.unshift(Il(e,o,d)),o=ml(e,t),o!=null&&l.push(Il(e,o,d))),e.tag===3)return l;e=e.return}return[]}function Kn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function am(e,t,a,l,o){for(var d=t._reactName,m=[];a!==null&&a!==l;){var v=a,j=v.alternate,M=v.stateNode;if(v=v.tag,j!==null&&j===l)break;v!==5&&v!==26&&v!==27||M===null||(j=M,o?(M=ml(a,d),M!=null&&m.unshift(Il(a,M,j))):o||(M=ml(a,d),M!=null&&m.push(Il(a,M,j)))),a=a.return}m.length!==0&&e.push({event:t,listeners:m})}var zy=/\r\n?/g,Ly=/\u0000|\uFFFD/g;function nm(e){return(typeof e=="string"?e:""+e).replace(zy,`
`).replace(Ly,"")}function lm(e,t){return t=nm(t),nm(e)===t}function Es(){}function De(e,t,a,l,o,d){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Nn(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Nn(e,""+l);break;case"className":Rr(e,"class",l);break;case"tabIndex":Rr(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Rr(e,a,l);break;case"style":rd(e,l,d);break;case"data":if(t!=="object"){Rr(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=Mr(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof d=="function"&&(a==="formAction"?(t!=="input"&&De(e,t,"name",o.name,o,null),De(e,t,"formEncType",o.formEncType,o,null),De(e,t,"formMethod",o.formMethod,o,null),De(e,t,"formTarget",o.formTarget,o,null)):(De(e,t,"encType",o.encType,o,null),De(e,t,"method",o.method,o,null),De(e,t,"target",o.target,o,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=Mr(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=Es);break;case"onScroll":l!=null&&xe("scroll",e);break;case"onScrollEnd":l!=null&&xe("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(o.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=Mr(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":xe("beforetoggle",e),xe("toggle",e),Or(e,"popover",l);break;case"xlinkActuate":Ft(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Ft(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Ft(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Ft(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Ft(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Ft(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Ft(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Ft(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Ft(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Or(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=h0.get(a)||a,Or(e,a,l))}}function mo(e,t,a,l,o,d){switch(a){case"style":rd(e,l,d);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(o.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"children":typeof l=="string"?Nn(e,l):(typeof l=="number"||typeof l=="bigint")&&Nn(e,""+l);break;case"onScroll":l!=null&&xe("scroll",e);break;case"onScrollEnd":l!=null&&xe("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Es);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Zu.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(o=a.endsWith("Capture"),t=a.slice(2,o?a.length-7:void 0),d=e[it]||null,d=d!=null?d[a]:null,typeof d=="function"&&e.removeEventListener(t,d,o),typeof l=="function")){typeof d!="function"&&d!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,o);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):Or(e,a,l)}}}function We(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":xe("error",e),xe("load",e);var l=!1,o=!1,d;for(d in a)if(a.hasOwnProperty(d)){var m=a[d];if(m!=null)switch(d){case"src":l=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:De(e,t,d,m,a,null)}}o&&De(e,t,"srcSet",a.srcSet,a,null),l&&De(e,t,"src",a.src,a,null);return;case"input":xe("invalid",e);var v=d=m=o=null,j=null,M=null;for(l in a)if(a.hasOwnProperty(l)){var q=a[l];if(q!=null)switch(l){case"name":o=q;break;case"type":m=q;break;case"checked":j=q;break;case"defaultChecked":M=q;break;case"value":d=q;break;case"defaultValue":v=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(c(137,t));break;default:De(e,t,l,q,a,null)}}td(e,d,v,j,M,m,o,!1),_r(e);return;case"select":xe("invalid",e),l=m=d=null;for(o in a)if(a.hasOwnProperty(o)&&(v=a[o],v!=null))switch(o){case"value":d=v;break;case"defaultValue":m=v;break;case"multiple":l=v;default:De(e,t,o,v,a,null)}t=d,a=m,e.multiple=!!l,t!=null?bn(e,!!l,t,!1):a!=null&&bn(e,!!l,a,!0);return;case"textarea":xe("invalid",e),d=o=l=null;for(m in a)if(a.hasOwnProperty(m)&&(v=a[m],v!=null))switch(m){case"value":l=v;break;case"defaultValue":o=v;break;case"children":d=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(c(91));break;default:De(e,t,m,v,a,null)}nd(e,l,o,d),_r(e);return;case"option":for(j in a)if(a.hasOwnProperty(j)&&(l=a[j],l!=null))switch(j){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:De(e,t,j,l,a,null)}return;case"dialog":xe("beforetoggle",e),xe("toggle",e),xe("cancel",e),xe("close",e);break;case"iframe":case"object":xe("load",e);break;case"video":case"audio":for(l=0;l<Kl.length;l++)xe(Kl[l],e);break;case"image":xe("error",e),xe("load",e);break;case"details":xe("toggle",e);break;case"embed":case"source":case"link":xe("error",e),xe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(M in a)if(a.hasOwnProperty(M)&&(l=a[M],l!=null))switch(M){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:De(e,t,M,l,a,null)}return;default:if(Oi(t)){for(q in a)a.hasOwnProperty(q)&&(l=a[q],l!==void 0&&mo(e,t,q,l,a,void 0));return}}for(v in a)a.hasOwnProperty(v)&&(l=a[v],l!=null&&De(e,t,v,l,a,null))}function Hy(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,d=null,m=null,v=null,j=null,M=null,q=null;for(L in a){var V=a[L];if(a.hasOwnProperty(L)&&V!=null)switch(L){case"checked":break;case"value":break;case"defaultValue":j=V;default:l.hasOwnProperty(L)||De(e,t,L,null,l,V)}}for(var U in l){var L=l[U];if(V=a[U],l.hasOwnProperty(U)&&(L!=null||V!=null))switch(U){case"type":d=L;break;case"name":o=L;break;case"checked":M=L;break;case"defaultChecked":q=L;break;case"value":m=L;break;case"defaultValue":v=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(c(137,t));break;default:L!==V&&De(e,t,U,L,l,V)}}Ci(e,m,v,j,M,q,d,o);return;case"select":L=m=v=U=null;for(d in a)if(j=a[d],a.hasOwnProperty(d)&&j!=null)switch(d){case"value":break;case"multiple":L=j;default:l.hasOwnProperty(d)||De(e,t,d,null,l,j)}for(o in l)if(d=l[o],j=a[o],l.hasOwnProperty(o)&&(d!=null||j!=null))switch(o){case"value":U=d;break;case"defaultValue":v=d;break;case"multiple":m=d;default:d!==j&&De(e,t,o,d,l,j)}t=v,a=m,l=L,U!=null?bn(e,!!a,U,!1):!!l!=!!a&&(t!=null?bn(e,!!a,t,!0):bn(e,!!a,a?[]:"",!1));return;case"textarea":L=U=null;for(v in a)if(o=a[v],a.hasOwnProperty(v)&&o!=null&&!l.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:De(e,t,v,null,l,o)}for(m in l)if(o=l[m],d=a[m],l.hasOwnProperty(m)&&(o!=null||d!=null))switch(m){case"value":U=o;break;case"defaultValue":L=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(c(91));break;default:o!==d&&De(e,t,m,o,l,d)}ad(e,U,L);return;case"option":for(var de in a)if(U=a[de],a.hasOwnProperty(de)&&U!=null&&!l.hasOwnProperty(de))switch(de){case"selected":e.selected=!1;break;default:De(e,t,de,null,l,U)}for(j in l)if(U=l[j],L=a[j],l.hasOwnProperty(j)&&U!==L&&(U!=null||L!=null))switch(j){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:De(e,t,j,U,l,L)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ce in a)U=a[ce],a.hasOwnProperty(ce)&&U!=null&&!l.hasOwnProperty(ce)&&De(e,t,ce,null,l,U);for(M in l)if(U=l[M],L=a[M],l.hasOwnProperty(M)&&U!==L&&(U!=null||L!=null))switch(M){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(c(137,t));break;default:De(e,t,M,U,l,L)}return;default:if(Oi(t)){for(var Oe in a)U=a[Oe],a.hasOwnProperty(Oe)&&U!==void 0&&!l.hasOwnProperty(Oe)&&mo(e,t,Oe,void 0,l,U);for(q in l)U=l[q],L=a[q],!l.hasOwnProperty(q)||U===L||U===void 0&&L===void 0||mo(e,t,q,U,l,L);return}}for(var _ in a)U=a[_],a.hasOwnProperty(_)&&U!=null&&!l.hasOwnProperty(_)&&De(e,t,_,null,l,U);for(V in l)U=l[V],L=a[V],!l.hasOwnProperty(V)||U===L||U==null&&L==null||De(e,t,V,U,l,L)}var po=null,go=null;function Ts(e){return e.nodeType===9?e:e.ownerDocument}function rm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function yo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var xo=null;function By(){var e=window.event;return e&&e.type==="popstate"?e===xo?!1:(xo=e,!0):(xo=null,!1)}var im=typeof setTimeout=="function"?setTimeout:void 0,qy=typeof clearTimeout=="function"?clearTimeout:void 0,cm=typeof Promise=="function"?Promise:void 0,Yy=typeof queueMicrotask=="function"?queueMicrotask:typeof cm<"u"?function(e){return cm.resolve(null).then(e).catch(Gy)}:im;function Gy(e){setTimeout(function(){throw e})}function ka(e){return e==="head"}function om(e,t){var a=t,l=0,o=0;do{var d=a.nextSibling;if(e.removeChild(a),d&&d.nodeType===8)if(a=d.data,a==="/$"){if(0<l&&8>l){a=l;var m=e.ownerDocument;if(a&1&&Fl(m.documentElement),a&2&&Fl(m.body),a&4)for(a=m.head,Fl(a),m=a.firstChild;m;){var v=m.nextSibling,j=m.nodeName;m[fl]||j==="SCRIPT"||j==="STYLE"||j==="LINK"&&m.rel.toLowerCase()==="stylesheet"||a.removeChild(m),m=v}}if(o===0){e.removeChild(d),rr(t);return}o--}else a==="$"||a==="$?"||a==="$!"?o++:l=a.charCodeAt(0)-48;else l=0;a=d}while(a);rr(t)}function vo(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":vo(a),Si(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function $y(e,t,a,l){for(;e.nodeType===1;){var o=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[fl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(d=e.getAttribute("rel"),d==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(d!==o.rel||e.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||e.getAttribute("title")!==(o.title==null?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(d=e.getAttribute("src"),(d!==(o.src==null?null:o.src)||e.getAttribute("type")!==(o.type==null?null:o.type)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&d&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var d=o.name==null?null:""+o.name;if(o.type==="hidden"&&e.getAttribute("name")===d)return e}else return e;if(e=Ht(e.nextSibling),e===null)break}return null}function Vy(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Ht(e.nextSibling),e===null))return null;return e}function bo(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Xy(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var No=null;function um(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function dm(e,t,a){switch(t=Ts(a),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Fl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Si(e)}var Mt=new Map,fm=new Set;function Cs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var da=K.d;K.d={f:Py,r:Qy,D:Zy,C:Ky,L:Iy,m:Fy,X:Wy,S:Jy,M:ex};function Py(){var e=da.f(),t=xs();return e||t}function Qy(e){var t=gn(e);t!==null&&t.tag===5&&t.type==="form"?Af(t):da.r(e)}var In=typeof document>"u"?null:document;function hm(e,t,a){var l=In;if(l&&typeof t=="string"&&t){var o=Tt(t);o='link[rel="'+e+'"][href="'+o+'"]',typeof a=="string"&&(o+='[crossorigin="'+a+'"]'),fm.has(o)||(fm.add(o),e={rel:e,crossOrigin:a,href:t},l.querySelector(o)===null&&(t=l.createElement("link"),We(t,"link",e),Qe(t),l.head.appendChild(t)))}}function Zy(e){da.D(e),hm("dns-prefetch",e,null)}function Ky(e,t){da.C(e,t),hm("preconnect",e,t)}function Iy(e,t,a){da.L(e,t,a);var l=In;if(l&&e&&t){var o='link[rel="preload"][as="'+Tt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(o+='[imagesrcset="'+Tt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(o+='[imagesizes="'+Tt(a.imageSizes)+'"]')):o+='[href="'+Tt(e)+'"]';var d=o;switch(t){case"style":d=Fn(e);break;case"script":d=Jn(e)}Mt.has(d)||(e=x({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Mt.set(d,e),l.querySelector(o)!==null||t==="style"&&l.querySelector(Jl(d))||t==="script"&&l.querySelector(Wl(d))||(t=l.createElement("link"),We(t,"link",e),Qe(t),l.head.appendChild(t)))}}function Fy(e,t){da.m(e,t);var a=In;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",o='link[rel="modulepreload"][as="'+Tt(l)+'"][href="'+Tt(e)+'"]',d=o;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":d=Jn(e)}if(!Mt.has(d)&&(e=x({rel:"modulepreload",href:e},t),Mt.set(d,e),a.querySelector(o)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Wl(d)))return}l=a.createElement("link"),We(l,"link",e),Qe(l),a.head.appendChild(l)}}}function Jy(e,t,a){da.S(e,t,a);var l=In;if(l&&e){var o=yn(l).hoistableStyles,d=Fn(e);t=t||"default";var m=o.get(d);if(!m){var v={loading:0,preload:null};if(m=l.querySelector(Jl(d)))v.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Mt.get(d))&&wo(e,a);var j=m=l.createElement("link");Qe(j),We(j,"link",e),j._p=new Promise(function(M,q){j.onload=M,j.onerror=q}),j.addEventListener("load",function(){v.loading|=1}),j.addEventListener("error",function(){v.loading|=2}),v.loading|=4,Ds(m,t,l)}m={type:"stylesheet",instance:m,count:1,state:v},o.set(d,m)}}}function Wy(e,t){da.X(e,t);var a=In;if(a&&e){var l=yn(a).hoistableScripts,o=Jn(e),d=l.get(o);d||(d=a.querySelector(Wl(o)),d||(e=x({src:e,async:!0},t),(t=Mt.get(o))&&So(e,t),d=a.createElement("script"),Qe(d),We(d,"link",e),a.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},l.set(o,d))}}function ex(e,t){da.M(e,t);var a=In;if(a&&e){var l=yn(a).hoistableScripts,o=Jn(e),d=l.get(o);d||(d=a.querySelector(Wl(o)),d||(e=x({src:e,async:!0,type:"module"},t),(t=Mt.get(o))&&So(e,t),d=a.createElement("script"),Qe(d),We(d,"link",e),a.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},l.set(o,d))}}function mm(e,t,a,l){var o=(o=ee.current)?Cs(o):null;if(!o)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Fn(a.href),a=yn(o).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Fn(a.href);var d=yn(o).hoistableStyles,m=d.get(e);if(m||(o=o.ownerDocument||o,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(e,m),(d=o.querySelector(Jl(e)))&&!d._p&&(m.instance=d,m.state.loading=5),Mt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Mt.set(e,a),d||tx(o,e,a,m.state))),t&&l===null)throw Error(c(528,""));return m}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Jn(a),a=yn(o).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Fn(e){return'href="'+Tt(e)+'"'}function Jl(e){return'link[rel="stylesheet"]['+e+"]"}function pm(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function tx(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),We(t,"link",a),Qe(t),e.head.appendChild(t))}function Jn(e){return'[src="'+Tt(e)+'"]'}function Wl(e){return"script[async]"+e}function gm(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Tt(a.href)+'"]');if(l)return t.instance=l,Qe(l),l;var o=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Qe(l),We(l,"style",o),Ds(l,a.precedence,e),t.instance=l;case"stylesheet":o=Fn(a.href);var d=e.querySelector(Jl(o));if(d)return t.state.loading|=4,t.instance=d,Qe(d),d;l=pm(a),(o=Mt.get(o))&&wo(l,o),d=(e.ownerDocument||e).createElement("link"),Qe(d);var m=d;return m._p=new Promise(function(v,j){m.onload=v,m.onerror=j}),We(d,"link",l),t.state.loading|=4,Ds(d,a.precedence,e),t.instance=d;case"script":return d=Jn(a.src),(o=e.querySelector(Wl(d)))?(t.instance=o,Qe(o),o):(l=a,(o=Mt.get(d))&&(l=x({},a),So(l,o)),e=e.ownerDocument||e,o=e.createElement("script"),Qe(o),We(o,"link",l),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Ds(l,a.precedence,e));return t.instance}function Ds(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=l.length?l[l.length-1]:null,d=o,m=0;m<l.length;m++){var v=l[m];if(v.dataset.precedence===t)d=v;else if(d!==o)break}d?d.parentNode.insertBefore(e,d.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function wo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function So(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Os=null;function ym(e,t,a){if(Os===null){var l=new Map,o=Os=new Map;o.set(a,l)}else o=Os,l=o.get(a),l||(l=new Map,o.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),o=0;o<a.length;o++){var d=a[o];if(!(d[fl]||d[tt]||e==="link"&&d.getAttribute("rel")==="stylesheet")&&d.namespaceURI!=="http://www.w3.org/2000/svg"){var m=d.getAttribute(t)||"";m=e+m;var v=l.get(m);v?v.push(d):l.set(m,[d])}}return l}function xm(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function ax(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function vm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var er=null;function nx(){}function lx(e,t,a){if(er===null)throw Error(c(475));var l=er;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var o=Fn(a.href),d=e.querySelector(Jl(o));if(d){e=d._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Rs.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=d,Qe(d);return}d=e.ownerDocument||e,a=pm(a),(o=Mt.get(o))&&wo(a,o),d=d.createElement("link"),Qe(d);var m=d;m._p=new Promise(function(v,j){m.onload=v,m.onerror=j}),We(d,"link",a),t.instance=d}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Rs.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function rx(){if(er===null)throw Error(c(475));var e=er;return e.stylesheets&&e.count===0&&jo(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&jo(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Rs(){if(this.count--,this.count===0){if(this.stylesheets)jo(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var _s=null;function jo(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,_s=new Map,t.forEach(sx,e),_s=null,Rs.call(e))}function sx(e,t){if(!(t.state.loading&4)){var a=_s.get(e);if(a)var l=a.get(null);else{a=new Map,_s.set(e,a);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),d=0;d<o.length;d++){var m=o[d];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(a.set(m.dataset.precedence,m),l=m)}l&&a.set(null,l)}o=t.instance,m=o.getAttribute("data-precedence"),d=a.get(m)||l,d===l&&a.set(null,o),a.set(m,o),this.count++,l=Rs.bind(this),o.addEventListener("load",l),o.addEventListener("error",l),d?d.parentNode.insertBefore(o,d.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(o,e.firstChild)),t.state.loading|=4}}var tr={$$typeof:$,Provider:null,Consumer:null,_currentValue:ie,_currentValue2:ie,_threadCount:0};function ix(e,t,a,l,o,d,m,v){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=vi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vi(0),this.hiddenUpdates=vi(null),this.identifierPrefix=l,this.onUncaughtError=o,this.onCaughtError=d,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function bm(e,t,a,l,o,d,m,v,j,M,q,V){return e=new ix(e,t,a,m,v,j,M,V),t=1,d===!0&&(t|=24),d=xt(3,null,null,t),e.current=d,d.stateNode=e,t=lc(),t.refCount++,e.pooledCache=t,t.refCount++,d.memoizedState={element:l,isDehydrated:a,cache:t},cc(d),e}function Nm(e){return e?(e=On,e):On}function wm(e,t,a,l,o,d){o=Nm(o),l.context===null?l.context=o:l.pendingContext=o,l=Na(t),l.payload={element:a},d=d===void 0?null:d,d!==null&&(l.callback=d),a=wa(e,l,t),a!==null&&(St(a,e,t),_l(a,e,t))}function Sm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Eo(e,t){Sm(e,t),(e=e.alternate)&&Sm(e,t)}function jm(e){if(e.tag===13){var t=Dn(e,67108864);t!==null&&St(t,e,67108864),Eo(e,67108864)}}var As=!0;function cx(e,t,a,l){var o=B.T;B.T=null;var d=K.p;try{K.p=2,To(e,t,a,l)}finally{K.p=d,B.T=o}}function ox(e,t,a,l){var o=B.T;B.T=null;var d=K.p;try{K.p=8,To(e,t,a,l)}finally{K.p=d,B.T=o}}function To(e,t,a,l){if(As){var o=Co(l);if(o===null)ho(e,t,l,Ms,a),Tm(e,l);else if(dx(o,e,t,a,l))l.stopPropagation();else if(Tm(e,l),t&4&&-1<ux.indexOf(e)){for(;o!==null;){var d=gn(o);if(d!==null)switch(d.tag){case 3:if(d=d.stateNode,d.current.memoizedState.isDehydrated){var m=$a(d.pendingLanes);if(m!==0){var v=d;for(v.pendingLanes|=2,v.entangledLanes|=2;m;){var j=1<<31-gt(m);v.entanglements[1]|=j,m&=~j}Kt(d),(Ee&6)===0&&(gs=Vt()+500,Zl(0))}}break;case 13:v=Dn(d,2),v!==null&&St(v,d,2),xs(),Eo(d,2)}if(d=Co(l),d===null&&ho(e,t,l,Ms,a),d===o)break;o=d}o!==null&&l.stopPropagation()}else ho(e,t,l,null,a)}}function Co(e){return e=_i(e),Do(e)}var Ms=null;function Do(e){if(Ms=null,e=pn(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=h(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ms=e,null}function Em(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Kg()){case Hu:return 2;case Bu:return 8;case Er:case Ig:return 32;case qu:return 268435456;default:return 32}default:return 32}}var Oo=!1,Ua=null,za=null,La=null,ar=new Map,nr=new Map,Ha=[],ux="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Tm(e,t){switch(e){case"focusin":case"focusout":Ua=null;break;case"dragenter":case"dragleave":za=null;break;case"mouseover":case"mouseout":La=null;break;case"pointerover":case"pointerout":ar.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":nr.delete(t.pointerId)}}function lr(e,t,a,l,o,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:d,targetContainers:[o]},t!==null&&(t=gn(t),t!==null&&jm(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function dx(e,t,a,l,o){switch(t){case"focusin":return Ua=lr(Ua,e,t,a,l,o),!0;case"dragenter":return za=lr(za,e,t,a,l,o),!0;case"mouseover":return La=lr(La,e,t,a,l,o),!0;case"pointerover":var d=o.pointerId;return ar.set(d,lr(ar.get(d)||null,e,t,a,l,o)),!0;case"gotpointercapture":return d=o.pointerId,nr.set(d,lr(nr.get(d)||null,e,t,a,l,o)),!0}return!1}function Cm(e){var t=pn(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=h(a),t!==null){e.blockedOn=t,l0(e.priority,function(){if(a.tag===13){var l=wt();l=bi(l);var o=Dn(a,l);o!==null&&St(o,a,l),Eo(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ks(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Co(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);Ri=l,a.target.dispatchEvent(l),Ri=null}else return t=gn(a),t!==null&&jm(t),e.blockedOn=a,!1;t.shift()}return!0}function Dm(e,t,a){ks(e)&&a.delete(t)}function fx(){Oo=!1,Ua!==null&&ks(Ua)&&(Ua=null),za!==null&&ks(za)&&(za=null),La!==null&&ks(La)&&(La=null),ar.forEach(Dm),nr.forEach(Dm)}function Us(e,t){e.blockedOn===t&&(e.blockedOn=null,Oo||(Oo=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,fx)))}var zs=null;function Om(e){zs!==e&&(zs=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){zs===e&&(zs=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],o=e[t+2];if(typeof l!="function"){if(Do(l||a)===null)continue;break}var d=gn(a);d!==null&&(e.splice(t,3),t-=3,Cc(d,{pending:!0,data:o,method:a.method,action:l},l,o))}}))}function rr(e){function t(j){return Us(j,e)}Ua!==null&&Us(Ua,e),za!==null&&Us(za,e),La!==null&&Us(La,e),ar.forEach(t),nr.forEach(t);for(var a=0;a<Ha.length;a++){var l=Ha[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Ha.length&&(a=Ha[0],a.blockedOn===null);)Cm(a),a.blockedOn===null&&Ha.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var o=a[l],d=a[l+1],m=o[it]||null;if(typeof d=="function")m||Om(a);else if(m){var v=null;if(d&&d.hasAttribute("formAction")){if(o=d,m=d[it]||null)v=m.formAction;else if(Do(o)!==null)continue}else v=m.action;typeof v=="function"?a[l+1]=v:(a.splice(l,3),l-=3),Om(a)}}}function Ro(e){this._internalRoot=e}Ls.prototype.render=Ro.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var a=t.current,l=wt();wm(a,l,e,t,null,null)},Ls.prototype.unmount=Ro.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wm(e.current,2,null,e,null,null),xs(),t[mn]=null}};function Ls(e){this._internalRoot=e}Ls.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xu();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Ha.length&&t!==0&&t<Ha[a].priority;a++);Ha.splice(a,0,e),a===0&&Cm(e)}};var Rm=r.version;if(Rm!=="19.1.0")throw Error(c(527,Rm,"19.1.0"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=y(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var hx={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Hs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Hs.isDisabled&&Hs.supportsFiber)try{ol=Hs.inject(hx),pt=Hs}catch{}}return ir.createRoot=function(e,t){if(!u(e))throw Error(c(299));var a=!1,l="",o=Pf,d=Qf,m=Zf,v=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(o=t.onUncaughtError),t.onCaughtError!==void 0&&(d=t.onCaughtError),t.onRecoverableError!==void 0&&(m=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(v=t.unstable_transitionCallbacks)),t=bm(e,1,!1,null,null,a,l,o,d,m,v,null),e[mn]=t.current,fo(e),new Ro(t)},ir.hydrateRoot=function(e,t,a){if(!u(e))throw Error(c(299));var l=!1,o="",d=Pf,m=Qf,v=Zf,j=null,M=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(o=a.identifierPrefix),a.onUncaughtError!==void 0&&(d=a.onUncaughtError),a.onCaughtError!==void 0&&(m=a.onCaughtError),a.onRecoverableError!==void 0&&(v=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(j=a.unstable_transitionCallbacks),a.formState!==void 0&&(M=a.formState)),t=bm(e,1,!0,t,a??null,l,o,d,m,v,j,M),t.context=Nm(null),a=t.current,l=wt(),l=bi(l),o=Na(l),o.callback=null,wa(a,o,l),a=l,t.current.lanes=a,dl(t,a),Kt(t),e[mn]=t.current,fo(e),new Ls(t)},ir.version="19.1.0",ir}var Ym;function Ex(){if(Ym)return Mo.exports;Ym=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Mo.exports=jx(),Mo.exports}var Tx=Ex();const Cx=fu(Tx);var cr={},Gm;function Dx(){if(Gm)return cr;Gm=1,Object.defineProperty(cr,"__esModule",{value:!0}),cr.parse=h,cr.serialize=p;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,c=/^[\u0020-\u003A\u003D-\u007E]*$/,u=Object.prototype.toString,f=(()=>{const b=function(){};return b.prototype=Object.create(null),b})();function h(b,R){const T=new f,C=b.length;if(C<2)return T;const S=(R==null?void 0:R.decode)||x;let E=0;do{const k=b.indexOf("=",E);if(k===-1)break;const $=b.indexOf(";",E),z=$===-1?C:$;if(k>z){E=b.lastIndexOf(";",k-1)+1;continue}const Y=g(b,E,k),Z=y(b,k,Y),se=b.slice(Y,Z);if(T[se]===void 0){let Q=g(b,k+1,z),I=y(b,z,Q);const oe=S(b.slice(Q,I));T[se]=oe}E=z+1}while(E<C);return T}function g(b,R,T){do{const C=b.charCodeAt(R);if(C!==32&&C!==9)return R}while(++R<T);return T}function y(b,R,T){for(;R>T;){const C=b.charCodeAt(--R);if(C!==32&&C!==9)return R+1}return T}function p(b,R,T){const C=(T==null?void 0:T.encode)||encodeURIComponent;if(!n.test(b))throw new TypeError(`argument name is invalid: ${b}`);const S=C(R);if(!r.test(S))throw new TypeError(`argument val is invalid: ${R}`);let E=b+"="+S;if(!T)return E;if(T.maxAge!==void 0){if(!Number.isInteger(T.maxAge))throw new TypeError(`option maxAge is invalid: ${T.maxAge}`);E+="; Max-Age="+T.maxAge}if(T.domain){if(!s.test(T.domain))throw new TypeError(`option domain is invalid: ${T.domain}`);E+="; Domain="+T.domain}if(T.path){if(!c.test(T.path))throw new TypeError(`option path is invalid: ${T.path}`);E+="; Path="+T.path}if(T.expires){if(!w(T.expires)||!Number.isFinite(T.expires.valueOf()))throw new TypeError(`option expires is invalid: ${T.expires}`);E+="; Expires="+T.expires.toUTCString()}if(T.httpOnly&&(E+="; HttpOnly"),T.secure&&(E+="; Secure"),T.partitioned&&(E+="; Partitioned"),T.priority)switch(typeof T.priority=="string"?T.priority.toLowerCase():void 0){case"low":E+="; Priority=Low";break;case"medium":E+="; Priority=Medium";break;case"high":E+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${T.priority}`)}if(T.sameSite)switch(typeof T.sameSite=="string"?T.sameSite.toLowerCase():T.sameSite){case!0:case"strict":E+="; SameSite=Strict";break;case"lax":E+="; SameSite=Lax";break;case"none":E+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${T.sameSite}`)}return E}function x(b){if(b.indexOf("%")===-1)return b;try{return decodeURIComponent(b)}catch{return b}}function w(b){return u.call(b)==="[object Date]"}return cr}Dx();var $m="popstate";function Ox(n={}){function r(c,u){let{pathname:f,search:h,hash:g}=c.location;return Fo("",{pathname:f,search:h,hash:g},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function s(c,u){return typeof u=="string"?u:gr(u)}return _x(r,s,null,n)}function Ue(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function Bt(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Rx(){return Math.random().toString(36).substring(2,10)}function Vm(n,r){return{usr:n.state,key:n.key,idx:r}}function Fo(n,r,s=null,c){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof r=="string"?al(r):r,state:s,key:r&&r.key||c||Rx()}}function gr({pathname:n="/",search:r="",hash:s=""}){return r&&r!=="?"&&(n+=r.charAt(0)==="?"?r:"?"+r),s&&s!=="#"&&(n+=s.charAt(0)==="#"?s:"#"+s),n}function al(n){let r={};if(n){let s=n.indexOf("#");s>=0&&(r.hash=n.substring(s),n=n.substring(0,s));let c=n.indexOf("?");c>=0&&(r.search=n.substring(c),n=n.substring(0,c)),n&&(r.pathname=n)}return r}function _x(n,r,s,c={}){let{window:u=document.defaultView,v5Compat:f=!1}=c,h=u.history,g="POP",y=null,p=x();p==null&&(p=0,h.replaceState({...h.state,idx:p},""));function x(){return(h.state||{idx:null}).idx}function w(){g="POP";let S=x(),E=S==null?null:S-p;p=S,y&&y({action:g,location:C.location,delta:E})}function b(S,E){g="PUSH";let k=Fo(C.location,S,E);p=x()+1;let $=Vm(k,p),z=C.createHref(k);try{h.pushState($,"",z)}catch(Y){if(Y instanceof DOMException&&Y.name==="DataCloneError")throw Y;u.location.assign(z)}f&&y&&y({action:g,location:C.location,delta:1})}function R(S,E){g="REPLACE";let k=Fo(C.location,S,E);p=x();let $=Vm(k,p),z=C.createHref(k);h.replaceState($,"",z),f&&y&&y({action:g,location:C.location,delta:0})}function T(S){return Ax(S)}let C={get action(){return g},get location(){return n(u,h)},listen(S){if(y)throw new Error("A history only accepts one active listener");return u.addEventListener($m,w),y=S,()=>{u.removeEventListener($m,w),y=null}},createHref(S){return r(u,S)},createURL:T,encodeLocation(S){let E=T(S);return{pathname:E.pathname,search:E.search,hash:E.hash}},push:b,replace:R,go(S){return h.go(S)}};return C}function Ax(n,r=!1){let s="http://localhost";typeof window<"u"&&(s=window.location.origin!=="null"?window.location.origin:window.location.href),Ue(s,"No window.location.(origin|href) available to create URL");let c=typeof n=="string"?n:gr(n);return c=c.replace(/ $/,"%20"),!r&&c.startsWith("//")&&(c=s+c),new URL(c,s)}function Rp(n,r,s="/"){return Mx(n,r,s,!1)}function Mx(n,r,s,c){let u=typeof r=="string"?al(r):r,f=ma(u.pathname||"/",s);if(f==null)return null;let h=_p(n);kx(h);let g=null;for(let y=0;g==null&&y<h.length;++y){let p=Xx(f);g=$x(h[y],p,c)}return g}function _p(n,r=[],s=[],c=""){let u=(f,h,g)=>{let y={relativePath:g===void 0?f.path||"":g,caseSensitive:f.caseSensitive===!0,childrenIndex:h,route:f};y.relativePath.startsWith("/")&&(Ue(y.relativePath.startsWith(c),`Absolute route path "${y.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),y.relativePath=y.relativePath.slice(c.length));let p=fa([c,y.relativePath]),x=s.concat(y);f.children&&f.children.length>0&&(Ue(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${p}".`),_p(f.children,r,x,p)),!(f.path==null&&!f.index)&&r.push({path:p,score:Yx(p,f.index),routesMeta:x})};return n.forEach((f,h)=>{var g;if(f.path===""||!((g=f.path)!=null&&g.includes("?")))u(f,h);else for(let y of Ap(f.path))u(f,h,y)}),r}function Ap(n){let r=n.split("/");if(r.length===0)return[];let[s,...c]=r,u=s.endsWith("?"),f=s.replace(/\?$/,"");if(c.length===0)return u?[f,""]:[f];let h=Ap(c.join("/")),g=[];return g.push(...h.map(y=>y===""?f:[f,y].join("/"))),u&&g.push(...h),g.map(y=>n.startsWith("/")&&y===""?"/":y)}function kx(n){n.sort((r,s)=>r.score!==s.score?s.score-r.score:Gx(r.routesMeta.map(c=>c.childrenIndex),s.routesMeta.map(c=>c.childrenIndex)))}var Ux=/^:[\w-]+$/,zx=3,Lx=2,Hx=1,Bx=10,qx=-2,Xm=n=>n==="*";function Yx(n,r){let s=n.split("/"),c=s.length;return s.some(Xm)&&(c+=qx),r&&(c+=Lx),s.filter(u=>!Xm(u)).reduce((u,f)=>u+(Ux.test(f)?zx:f===""?Hx:Bx),c)}function Gx(n,r){return n.length===r.length&&n.slice(0,-1).every((c,u)=>c===r[u])?n[n.length-1]-r[r.length-1]:0}function $x(n,r,s=!1){let{routesMeta:c}=n,u={},f="/",h=[];for(let g=0;g<c.length;++g){let y=c[g],p=g===c.length-1,x=f==="/"?r:r.slice(f.length)||"/",w=Zs({path:y.relativePath,caseSensitive:y.caseSensitive,end:p},x),b=y.route;if(!w&&p&&s&&!c[c.length-1].route.index&&(w=Zs({path:y.relativePath,caseSensitive:y.caseSensitive,end:!1},x)),!w)return null;Object.assign(u,w.params),h.push({params:u,pathname:fa([f,w.pathname]),pathnameBase:Kx(fa([f,w.pathnameBase])),route:b}),w.pathnameBase!=="/"&&(f=fa([f,w.pathnameBase]))}return h}function Zs(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[s,c]=Vx(n.path,n.caseSensitive,n.end),u=r.match(s);if(!u)return null;let f=u[0],h=f.replace(/(.)\/+$/,"$1"),g=u.slice(1);return{params:c.reduce((p,{paramName:x,isOptional:w},b)=>{if(x==="*"){let T=g[b]||"";h=f.slice(0,f.length-T.length).replace(/(.)\/+$/,"$1")}const R=g[b];return w&&!R?p[x]=void 0:p[x]=(R||"").replace(/%2F/g,"/"),p},{}),pathname:f,pathnameBase:h,pattern:n}}function Vx(n,r=!1,s=!0){Bt(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let c=[],u="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,g,y)=>(c.push({paramName:g,isOptional:y!=null}),y?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(c.push({paramName:"*"}),u+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?u+="\\/*$":n!==""&&n!=="/"&&(u+="(?:(?=\\/|$))"),[new RegExp(u,r?void 0:"i"),c]}function Xx(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Bt(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),n}}function ma(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let s=r.endsWith("/")?r.length-1:r.length,c=n.charAt(s);return c&&c!=="/"?null:n.slice(s)||"/"}function Px(n,r="/"){let{pathname:s,search:c="",hash:u=""}=typeof n=="string"?al(n):n;return{pathname:s?s.startsWith("/")?s:Qx(s,r):r,search:Ix(c),hash:Fx(u)}}function Qx(n,r){let s=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(u=>{u===".."?s.length>1&&s.pop():u!=="."&&s.push(u)}),s.length>1?s.join("/"):"/"}function Lo(n,r,s,c){return`Cannot include a '${n}' character in a manually specified \`to.${r}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Zx(n){return n.filter((r,s)=>s===0||r.route.path&&r.route.path.length>0)}function mu(n){let r=Zx(n);return r.map((s,c)=>c===r.length-1?s.pathname:s.pathnameBase)}function pu(n,r,s,c=!1){let u;typeof n=="string"?u=al(n):(u={...n},Ue(!u.pathname||!u.pathname.includes("?"),Lo("?","pathname","search",u)),Ue(!u.pathname||!u.pathname.includes("#"),Lo("#","pathname","hash",u)),Ue(!u.search||!u.search.includes("#"),Lo("#","search","hash",u)));let f=n===""||u.pathname==="",h=f?"/":u.pathname,g;if(h==null)g=s;else{let w=r.length-1;if(!c&&h.startsWith("..")){let b=h.split("/");for(;b[0]==="..";)b.shift(),w-=1;u.pathname=b.join("/")}g=w>=0?r[w]:"/"}let y=Px(u,g),p=h&&h!=="/"&&h.endsWith("/"),x=(f||h===".")&&s.endsWith("/");return!y.pathname.endsWith("/")&&(p||x)&&(y.pathname+="/"),y}var fa=n=>n.join("/").replace(/\/\/+/g,"/"),Kx=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),Ix=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,Fx=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function Jx(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var Mp=["POST","PUT","PATCH","DELETE"];new Set(Mp);var Wx=["GET",...Mp];new Set(Wx);var nl=N.createContext(null);nl.displayName="DataRouter";var ti=N.createContext(null);ti.displayName="DataRouterState";var kp=N.createContext({isTransitioning:!1});kp.displayName="ViewTransition";var ev=N.createContext(new Map);ev.displayName="Fetchers";var tv=N.createContext(null);tv.displayName="Await";var qt=N.createContext(null);qt.displayName="Navigation";var xr=N.createContext(null);xr.displayName="Location";var Yt=N.createContext({outlet:null,matches:[],isDataRoute:!1});Yt.displayName="Route";var gu=N.createContext(null);gu.displayName="RouteError";function av(n,{relative:r}={}){Ue(ll(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:c}=N.useContext(qt),{hash:u,pathname:f,search:h}=vr(n,{relative:r}),g=f;return s!=="/"&&(g=f==="/"?s:fa([s,f])),c.createHref({pathname:g,search:h,hash:u})}function ll(){return N.useContext(xr)!=null}function pa(){return Ue(ll(),"useLocation() may be used only in the context of a <Router> component."),N.useContext(xr).location}var Up="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function zp(n){N.useContext(qt).static||N.useLayoutEffect(n)}function ai(){let{isDataRoute:n}=N.useContext(Yt);return n?pv():nv()}function nv(){Ue(ll(),"useNavigate() may be used only in the context of a <Router> component.");let n=N.useContext(nl),{basename:r,navigator:s}=N.useContext(qt),{matches:c}=N.useContext(Yt),{pathname:u}=pa(),f=JSON.stringify(mu(c)),h=N.useRef(!1);return zp(()=>{h.current=!0}),N.useCallback((y,p={})=>{if(Bt(h.current,Up),!h.current)return;if(typeof y=="number"){s.go(y);return}let x=pu(y,JSON.parse(f),u,p.relative==="path");n==null&&r!=="/"&&(x.pathname=x.pathname==="/"?r:fa([r,x.pathname])),(p.replace?s.replace:s.push)(x,p.state,p)},[r,s,f,u,n])}N.createContext(null);function Lp(){let{matches:n}=N.useContext(Yt),r=n[n.length-1];return r?r.params:{}}function vr(n,{relative:r}={}){let{matches:s}=N.useContext(Yt),{pathname:c}=pa(),u=JSON.stringify(mu(s));return N.useMemo(()=>pu(n,JSON.parse(u),c,r==="path"),[n,u,c,r])}function lv(n,r){return Hp(n,r)}function Hp(n,r,s,c){var E;Ue(ll(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:u}=N.useContext(qt),{matches:f}=N.useContext(Yt),h=f[f.length-1],g=h?h.params:{},y=h?h.pathname:"/",p=h?h.pathnameBase:"/",x=h&&h.route;{let k=x&&x.path||"";Bp(y,!x||k.endsWith("*")||k.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${y}" (under <Route path="${k}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${k}"> to <Route path="${k==="/"?"*":`${k}/*`}">.`)}let w=pa(),b;if(r){let k=typeof r=="string"?al(r):r;Ue(p==="/"||((E=k.pathname)==null?void 0:E.startsWith(p)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${p}" but pathname "${k.pathname}" was given in the \`location\` prop.`),b=k}else b=w;let R=b.pathname||"/",T=R;if(p!=="/"){let k=p.replace(/^\//,"").split("/");T="/"+R.replace(/^\//,"").split("/").slice(k.length).join("/")}let C=Rp(n,{pathname:T});Bt(x||C!=null,`No routes matched location "${b.pathname}${b.search}${b.hash}" `),Bt(C==null||C[C.length-1].route.element!==void 0||C[C.length-1].route.Component!==void 0||C[C.length-1].route.lazy!==void 0,`Matched leaf route at location "${b.pathname}${b.search}${b.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let S=ov(C&&C.map(k=>Object.assign({},k,{params:Object.assign({},g,k.params),pathname:fa([p,u.encodeLocation?u.encodeLocation(k.pathname).pathname:k.pathname]),pathnameBase:k.pathnameBase==="/"?p:fa([p,u.encodeLocation?u.encodeLocation(k.pathnameBase).pathname:k.pathnameBase])})),f,s,c);return r&&S?N.createElement(xr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...b},navigationType:"POP"}},S):S}function rv(){let n=mv(),r=Jx(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),s=n instanceof Error?n.stack:null,c="rgba(200,200,200, 0.5)",u={padding:"0.5rem",backgroundColor:c},f={padding:"2px 4px",backgroundColor:c},h=null;return console.error("Error handled by React Router default ErrorBoundary:",n),h=N.createElement(N.Fragment,null,N.createElement("p",null,"💿 Hey developer 👋"),N.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",N.createElement("code",{style:f},"ErrorBoundary")," or"," ",N.createElement("code",{style:f},"errorElement")," prop on your route.")),N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},r),s?N.createElement("pre",{style:u},s):null,h)}var sv=N.createElement(rv,null),iv=class extends N.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,r){return r.location!==n.location||r.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:r.error,location:r.location,revalidation:n.revalidation||r.revalidation}}componentDidCatch(n,r){console.error("React Router caught the following error during render",n,r)}render(){return this.state.error!==void 0?N.createElement(Yt.Provider,{value:this.props.routeContext},N.createElement(gu.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function cv({routeContext:n,match:r,children:s}){let c=N.useContext(nl);return c&&c.static&&c.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=r.route.id),N.createElement(Yt.Provider,{value:n},s)}function ov(n,r=[],s=null,c=null){if(n==null){if(!s)return null;if(s.errors)n=s.matches;else if(r.length===0&&!s.initialized&&s.matches.length>0)n=s.matches;else return null}let u=n,f=s==null?void 0:s.errors;if(f!=null){let y=u.findIndex(p=>p.route.id&&(f==null?void 0:f[p.route.id])!==void 0);Ue(y>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),u=u.slice(0,Math.min(u.length,y+1))}let h=!1,g=-1;if(s)for(let y=0;y<u.length;y++){let p=u[y];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(g=y),p.route.id){let{loaderData:x,errors:w}=s,b=p.route.loader&&!x.hasOwnProperty(p.route.id)&&(!w||w[p.route.id]===void 0);if(p.route.lazy||b){h=!0,g>=0?u=u.slice(0,g+1):u=[u[0]];break}}}return u.reduceRight((y,p,x)=>{let w,b=!1,R=null,T=null;s&&(w=f&&p.route.id?f[p.route.id]:void 0,R=p.route.errorElement||sv,h&&(g<0&&x===0?(Bp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),b=!0,T=null):g===x&&(b=!0,T=p.route.hydrateFallbackElement||null)));let C=r.concat(u.slice(0,x+1)),S=()=>{let E;return w?E=R:b?E=T:p.route.Component?E=N.createElement(p.route.Component,null):p.route.element?E=p.route.element:E=y,N.createElement(cv,{match:p,routeContext:{outlet:y,matches:C,isDataRoute:s!=null},children:E})};return s&&(p.route.ErrorBoundary||p.route.errorElement||x===0)?N.createElement(iv,{location:s.location,revalidation:s.revalidation,component:R,error:w,children:S(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):S()},null)}function yu(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function uv(n){let r=N.useContext(nl);return Ue(r,yu(n)),r}function dv(n){let r=N.useContext(ti);return Ue(r,yu(n)),r}function fv(n){let r=N.useContext(Yt);return Ue(r,yu(n)),r}function xu(n){let r=fv(n),s=r.matches[r.matches.length-1];return Ue(s.route.id,`${n} can only be used on routes that contain a unique "id"`),s.route.id}function hv(){return xu("useRouteId")}function mv(){var c;let n=N.useContext(gu),r=dv("useRouteError"),s=xu("useRouteError");return n!==void 0?n:(c=r.errors)==null?void 0:c[s]}function pv(){let{router:n}=uv("useNavigate"),r=xu("useNavigate"),s=N.useRef(!1);return zp(()=>{s.current=!0}),N.useCallback(async(u,f={})=>{Bt(s.current,Up),s.current&&(typeof u=="number"?n.navigate(u):await n.navigate(u,{fromRouteId:r,...f}))},[n,r])}var Pm={};function Bp(n,r,s){!r&&!Pm[n]&&(Pm[n]=!0,Bt(!1,s))}N.memo(gv);function gv({routes:n,future:r,state:s}){return Hp(n,void 0,s,r)}function vu({to:n,replace:r,state:s,relative:c}){Ue(ll(),"<Navigate> may be used only in the context of a <Router> component.");let{static:u}=N.useContext(qt);Bt(!u,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=N.useContext(Yt),{pathname:h}=pa(),g=ai(),y=pu(n,mu(f),h,c==="path"),p=JSON.stringify(y);return N.useEffect(()=>{g(JSON.parse(p),{replace:r,state:s,relative:c})},[g,p,c,r,s]),null}function ft(n){Ue(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function yv({basename:n="/",children:r=null,location:s,navigationType:c="POP",navigator:u,static:f=!1}){Ue(!ll(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=n.replace(/^\/*/,"/"),g=N.useMemo(()=>({basename:h,navigator:u,static:f,future:{}}),[h,u,f]);typeof s=="string"&&(s=al(s));let{pathname:y="/",search:p="",hash:x="",state:w=null,key:b="default"}=s,R=N.useMemo(()=>{let T=ma(y,h);return T==null?null:{location:{pathname:T,search:p,hash:x,state:w,key:b},navigationType:c}},[h,y,p,x,w,b,c]);return Bt(R!=null,`<Router basename="${h}"> is not able to match the URL "${y}${p}${x}" because it does not start with the basename, so the <Router> won't render anything.`),R==null?null:N.createElement(qt.Provider,{value:g},N.createElement(xr.Provider,{children:r,value:R}))}function Qm({children:n,location:r}){return lv(Jo(n),r)}function Jo(n,r=[]){let s=[];return N.Children.forEach(n,(c,u)=>{if(!N.isValidElement(c))return;let f=[...r,u];if(c.type===N.Fragment){s.push.apply(s,Jo(c.props.children,f));return}Ue(c.type===ft,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ue(!c.props.index||!c.props.children,"An index route cannot have child routes.");let h={id:c.props.id||f.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(h.children=Jo(c.props.children,f)),s.push(h)}),s}var Ys="get",Gs="application/x-www-form-urlencoded";function ni(n){return n!=null&&typeof n.tagName=="string"}function xv(n){return ni(n)&&n.tagName.toLowerCase()==="button"}function vv(n){return ni(n)&&n.tagName.toLowerCase()==="form"}function bv(n){return ni(n)&&n.tagName.toLowerCase()==="input"}function Nv(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function wv(n,r){return n.button===0&&(!r||r==="_self")&&!Nv(n)}var Bs=null;function Sv(){if(Bs===null)try{new FormData(document.createElement("form"),0),Bs=!1}catch{Bs=!0}return Bs}var jv=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ho(n){return n!=null&&!jv.has(n)?(Bt(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Gs}"`),null):n}function Ev(n,r){let s,c,u,f,h;if(vv(n)){let g=n.getAttribute("action");c=g?ma(g,r):null,s=n.getAttribute("method")||Ys,u=Ho(n.getAttribute("enctype"))||Gs,f=new FormData(n)}else if(xv(n)||bv(n)&&(n.type==="submit"||n.type==="image")){let g=n.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let y=n.getAttribute("formaction")||g.getAttribute("action");if(c=y?ma(y,r):null,s=n.getAttribute("formmethod")||g.getAttribute("method")||Ys,u=Ho(n.getAttribute("formenctype"))||Ho(g.getAttribute("enctype"))||Gs,f=new FormData(g,n),!Sv()){let{name:p,type:x,value:w}=n;if(x==="image"){let b=p?`${p}.`:"";f.append(`${b}x`,"0"),f.append(`${b}y`,"0")}else p&&f.append(p,w)}}else{if(ni(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=Ys,c=null,u=Gs,h=n}return f&&u==="text/plain"&&(h=f,f=void 0),{action:c,method:s.toLowerCase(),encType:u,formData:f,body:h}}function bu(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}async function Tv(n,r){if(n.id in r)return r[n.id];try{let s=await import(n.module);return r[n.id]=s,s}catch(s){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Cv(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function Dv(n,r,s){let c=await Promise.all(n.map(async u=>{let f=r.routes[u.route.id];if(f){let h=await Tv(f,s);return h.links?h.links():[]}return[]}));return Av(c.flat(1).filter(Cv).filter(u=>u.rel==="stylesheet"||u.rel==="preload").map(u=>u.rel==="stylesheet"?{...u,rel:"prefetch",as:"style"}:{...u,rel:"prefetch"}))}function Zm(n,r,s,c,u,f){let h=(y,p)=>s[p]?y.route.id!==s[p].route.id:!0,g=(y,p)=>{var x;return s[p].pathname!==y.pathname||((x=s[p].route.path)==null?void 0:x.endsWith("*"))&&s[p].params["*"]!==y.params["*"]};return f==="assets"?r.filter((y,p)=>h(y,p)||g(y,p)):f==="data"?r.filter((y,p)=>{var w;let x=c.routes[y.route.id];if(!x||!x.hasLoader)return!1;if(h(y,p)||g(y,p))return!0;if(y.route.shouldRevalidate){let b=y.route.shouldRevalidate({currentUrl:new URL(u.pathname+u.search+u.hash,window.origin),currentParams:((w=s[0])==null?void 0:w.params)||{},nextUrl:new URL(n,window.origin),nextParams:y.params,defaultShouldRevalidate:!0});if(typeof b=="boolean")return b}return!0}):[]}function Ov(n,r,{includeHydrateFallback:s}={}){return Rv(n.map(c=>{let u=r.routes[c.route.id];if(!u)return[];let f=[u.module];return u.clientActionModule&&(f=f.concat(u.clientActionModule)),u.clientLoaderModule&&(f=f.concat(u.clientLoaderModule)),s&&u.hydrateFallbackModule&&(f=f.concat(u.hydrateFallbackModule)),u.imports&&(f=f.concat(u.imports)),f}).flat(1))}function Rv(n){return[...new Set(n)]}function _v(n){let r={},s=Object.keys(n).sort();for(let c of s)r[c]=n[c];return r}function Av(n,r){let s=new Set;return new Set(r),n.reduce((c,u)=>{let f=JSON.stringify(_v(u));return s.has(f)||(s.add(f),c.push({key:f,link:u})),c},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Mv=new Set([100,101,204,205]);function kv(n,r){let s=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return s.pathname==="/"?s.pathname="_root.data":r&&ma(s.pathname,r)==="/"?s.pathname=`${r.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}function qp(){let n=N.useContext(nl);return bu(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function Uv(){let n=N.useContext(ti);return bu(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Nu=N.createContext(void 0);Nu.displayName="FrameworkContext";function Yp(){let n=N.useContext(Nu);return bu(n,"You must render this element inside a <HydratedRouter> element"),n}function zv(n,r){let s=N.useContext(Nu),[c,u]=N.useState(!1),[f,h]=N.useState(!1),{onFocus:g,onBlur:y,onMouseEnter:p,onMouseLeave:x,onTouchStart:w}=r,b=N.useRef(null);N.useEffect(()=>{if(n==="render"&&h(!0),n==="viewport"){let C=E=>{E.forEach(k=>{h(k.isIntersecting)})},S=new IntersectionObserver(C,{threshold:.5});return b.current&&S.observe(b.current),()=>{S.disconnect()}}},[n]),N.useEffect(()=>{if(c){let C=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(C)}}},[c]);let R=()=>{u(!0)},T=()=>{u(!1),h(!1)};return s?n!=="intent"?[f,b,{}]:[f,b,{onFocus:or(g,R),onBlur:or(y,T),onMouseEnter:or(p,R),onMouseLeave:or(x,T),onTouchStart:or(w,R)}]:[!1,b,{}]}function or(n,r){return s=>{n&&n(s),s.defaultPrevented||r(s)}}function Lv({page:n,...r}){let{router:s}=qp(),c=N.useMemo(()=>Rp(s.routes,n,s.basename),[s.routes,n,s.basename]);return c?N.createElement(Bv,{page:n,matches:c,...r}):null}function Hv(n){let{manifest:r,routeModules:s}=Yp(),[c,u]=N.useState([]);return N.useEffect(()=>{let f=!1;return Dv(n,r,s).then(h=>{f||u(h)}),()=>{f=!0}},[n,r,s]),c}function Bv({page:n,matches:r,...s}){let c=pa(),{manifest:u,routeModules:f}=Yp(),{basename:h}=qp(),{loaderData:g,matches:y}=Uv(),p=N.useMemo(()=>Zm(n,r,y,u,c,"data"),[n,r,y,u,c]),x=N.useMemo(()=>Zm(n,r,y,u,c,"assets"),[n,r,y,u,c]),w=N.useMemo(()=>{if(n===c.pathname+c.search+c.hash)return[];let T=new Set,C=!1;if(r.forEach(E=>{var $;let k=u.routes[E.route.id];!k||!k.hasLoader||(!p.some(z=>z.route.id===E.route.id)&&E.route.id in g&&(($=f[E.route.id])!=null&&$.shouldRevalidate)||k.hasClientLoader?C=!0:T.add(E.route.id))}),T.size===0)return[];let S=kv(n,h);return C&&T.size>0&&S.searchParams.set("_routes",r.filter(E=>T.has(E.route.id)).map(E=>E.route.id).join(",")),[S.pathname+S.search]},[h,g,c,u,p,r,n,f]),b=N.useMemo(()=>Ov(x,u),[x,u]),R=Hv(x);return N.createElement(N.Fragment,null,w.map(T=>N.createElement("link",{key:T,rel:"prefetch",as:"fetch",href:T,...s})),b.map(T=>N.createElement("link",{key:T,rel:"modulepreload",href:T,...s})),R.map(({key:T,link:C})=>N.createElement("link",{key:T,...C})))}function qv(...n){return r=>{n.forEach(s=>{typeof s=="function"?s(r):s!=null&&(s.current=r)})}}var Gp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Gp&&(window.__reactRouterVersion="7.6.2")}catch{}function Yv({basename:n,children:r,window:s}){let c=N.useRef();c.current==null&&(c.current=Ox({window:s,v5Compat:!0}));let u=c.current,[f,h]=N.useState({action:u.action,location:u.location}),g=N.useCallback(y=>{N.startTransition(()=>h(y))},[h]);return N.useLayoutEffect(()=>u.listen(g),[u,g]),N.createElement(yv,{basename:n,children:r,location:f.location,navigationType:f.action,navigator:u})}var $p=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,lt=N.forwardRef(function({onClick:r,discover:s="render",prefetch:c="none",relative:u,reloadDocument:f,replace:h,state:g,target:y,to:p,preventScrollReset:x,viewTransition:w,...b},R){let{basename:T}=N.useContext(qt),C=typeof p=="string"&&$p.test(p),S,E=!1;if(typeof p=="string"&&C&&(S=p,Gp))try{let I=new URL(window.location.href),oe=p.startsWith("//")?new URL(I.protocol+p):new URL(p),ve=ma(oe.pathname,T);oe.origin===I.origin&&ve!=null?p=ve+oe.search+oe.hash:E=!0}catch{Bt(!1,`<Link to="${p}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let k=av(p,{relative:u}),[$,z,Y]=zv(c,b),Z=Xv(p,{replace:h,state:g,target:y,preventScrollReset:x,relative:u,viewTransition:w});function se(I){r&&r(I),I.defaultPrevented||Z(I)}let Q=N.createElement("a",{...b,...Y,href:S||k,onClick:E||f?r:se,ref:qv(R,z),target:y,"data-discover":!C&&s==="render"?"true":void 0});return $&&!C?N.createElement(N.Fragment,null,Q,N.createElement(Lv,{page:k})):Q});lt.displayName="Link";var Gv=N.forwardRef(function({"aria-current":r="page",caseSensitive:s=!1,className:c="",end:u=!1,style:f,to:h,viewTransition:g,children:y,...p},x){let w=vr(h,{relative:p.relative}),b=pa(),R=N.useContext(ti),{navigator:T,basename:C}=N.useContext(qt),S=R!=null&&Iv(w)&&g===!0,E=T.encodeLocation?T.encodeLocation(w).pathname:w.pathname,k=b.pathname,$=R&&R.navigation&&R.navigation.location?R.navigation.location.pathname:null;s||(k=k.toLowerCase(),$=$?$.toLowerCase():null,E=E.toLowerCase()),$&&C&&($=ma($,C)||$);const z=E!=="/"&&E.endsWith("/")?E.length-1:E.length;let Y=k===E||!u&&k.startsWith(E)&&k.charAt(z)==="/",Z=$!=null&&($===E||!u&&$.startsWith(E)&&$.charAt(E.length)==="/"),se={isActive:Y,isPending:Z,isTransitioning:S},Q=Y?r:void 0,I;typeof c=="function"?I=c(se):I=[c,Y?"active":null,Z?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let oe=typeof f=="function"?f(se):f;return N.createElement(lt,{...p,"aria-current":Q,className:I,ref:x,style:oe,to:h,viewTransition:g},typeof y=="function"?y(se):y)});Gv.displayName="NavLink";var $v=N.forwardRef(({discover:n="render",fetcherKey:r,navigate:s,reloadDocument:c,replace:u,state:f,method:h=Ys,action:g,onSubmit:y,relative:p,preventScrollReset:x,viewTransition:w,...b},R)=>{let T=Zv(),C=Kv(g,{relative:p}),S=h.toLowerCase()==="get"?"get":"post",E=typeof g=="string"&&$p.test(g),k=$=>{if(y&&y($),$.defaultPrevented)return;$.preventDefault();let z=$.nativeEvent.submitter,Y=(z==null?void 0:z.getAttribute("formmethod"))||h;T(z||$.currentTarget,{fetcherKey:r,method:Y,navigate:s,replace:u,state:f,relative:p,preventScrollReset:x,viewTransition:w})};return N.createElement("form",{ref:R,method:S,action:C,onSubmit:c?y:k,...b,"data-discover":!E&&n==="render"?"true":void 0})});$v.displayName="Form";function Vv(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Vp(n){let r=N.useContext(nl);return Ue(r,Vv(n)),r}function Xv(n,{target:r,replace:s,state:c,preventScrollReset:u,relative:f,viewTransition:h}={}){let g=ai(),y=pa(),p=vr(n,{relative:f});return N.useCallback(x=>{if(wv(x,r)){x.preventDefault();let w=s!==void 0?s:gr(y)===gr(p);g(n,{replace:w,state:c,preventScrollReset:u,relative:f,viewTransition:h})}},[y,g,p,s,c,r,n,u,f,h])}var Pv=0,Qv=()=>`__${String(++Pv)}__`;function Zv(){let{router:n}=Vp("useSubmit"),{basename:r}=N.useContext(qt),s=hv();return N.useCallback(async(c,u={})=>{let{action:f,method:h,encType:g,formData:y,body:p}=Ev(c,r);if(u.navigate===!1){let x=u.fetcherKey||Qv();await n.fetch(x,s,u.action||f,{preventScrollReset:u.preventScrollReset,formData:y,body:p,formMethod:u.method||h,formEncType:u.encType||g,flushSync:u.flushSync})}else await n.navigate(u.action||f,{preventScrollReset:u.preventScrollReset,formData:y,body:p,formMethod:u.method||h,formEncType:u.encType||g,replace:u.replace,state:u.state,fromRouteId:s,flushSync:u.flushSync,viewTransition:u.viewTransition})},[n,r,s])}function Kv(n,{relative:r}={}){let{basename:s}=N.useContext(qt),c=N.useContext(Yt);Ue(c,"useFormAction must be used inside a RouteContext");let[u]=c.matches.slice(-1),f={...vr(n||".",{relative:r})},h=pa();if(n==null){f.search=h.search;let g=new URLSearchParams(f.search),y=g.getAll("index");if(y.some(x=>x==="")){g.delete("index"),y.filter(w=>w).forEach(w=>g.append("index",w));let x=g.toString();f.search=x?`?${x}`:""}}return(!n||n===".")&&u.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(f.pathname=f.pathname==="/"?s:fa([s,f.pathname])),gr(f)}function Iv(n,r={}){let s=N.useContext(kp);Ue(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=Vp("useViewTransitionState"),u=vr(n,{relative:r.relative});if(!s.isTransitioning)return!1;let f=ma(s.currentLocation.pathname,c)||s.currentLocation.pathname,h=ma(s.nextLocation.pathname,c)||s.nextLocation.pathname;return Zs(u.pathname,h)!=null||Zs(u.pathname,f)!=null}[...Mv];function Xp(n,r){return function(){return n.apply(r,arguments)}}const{toString:Fv}=Object.prototype,{getPrototypeOf:wu}=Object,{iterator:li,toStringTag:Pp}=Symbol,ri=(n=>r=>{const s=Fv.call(r);return n[s]||(n[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Gt=n=>(n=n.toLowerCase(),r=>ri(r)===n),si=n=>r=>typeof r===n,{isArray:rl}=Array,yr=si("undefined");function Jv(n){return n!==null&&!yr(n)&&n.constructor!==null&&!yr(n.constructor)&&ht(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Qp=Gt("ArrayBuffer");function Wv(n){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(n):r=n&&n.buffer&&Qp(n.buffer),r}const eb=si("string"),ht=si("function"),Zp=si("number"),ii=n=>n!==null&&typeof n=="object",tb=n=>n===!0||n===!1,$s=n=>{if(ri(n)!=="object")return!1;const r=wu(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Pp in n)&&!(li in n)},ab=Gt("Date"),nb=Gt("File"),lb=Gt("Blob"),rb=Gt("FileList"),sb=n=>ii(n)&&ht(n.pipe),ib=n=>{let r;return n&&(typeof FormData=="function"&&n instanceof FormData||ht(n.append)&&((r=ri(n))==="formdata"||r==="object"&&ht(n.toString)&&n.toString()==="[object FormData]"))},cb=Gt("URLSearchParams"),[ob,ub,db,fb]=["ReadableStream","Request","Response","Headers"].map(Gt),hb=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function br(n,r,{allOwnKeys:s=!1}={}){if(n===null||typeof n>"u")return;let c,u;if(typeof n!="object"&&(n=[n]),rl(n))for(c=0,u=n.length;c<u;c++)r.call(null,n[c],c,n);else{const f=s?Object.getOwnPropertyNames(n):Object.keys(n),h=f.length;let g;for(c=0;c<h;c++)g=f[c],r.call(null,n[g],g,n)}}function Kp(n,r){r=r.toLowerCase();const s=Object.keys(n);let c=s.length,u;for(;c-- >0;)if(u=s[c],r===u.toLowerCase())return u;return null}const cn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ip=n=>!yr(n)&&n!==cn;function Wo(){const{caseless:n}=Ip(this)&&this||{},r={},s=(c,u)=>{const f=n&&Kp(r,u)||u;$s(r[f])&&$s(c)?r[f]=Wo(r[f],c):$s(c)?r[f]=Wo({},c):rl(c)?r[f]=c.slice():r[f]=c};for(let c=0,u=arguments.length;c<u;c++)arguments[c]&&br(arguments[c],s);return r}const mb=(n,r,s,{allOwnKeys:c}={})=>(br(r,(u,f)=>{s&&ht(u)?n[f]=Xp(u,s):n[f]=u},{allOwnKeys:c}),n),pb=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),gb=(n,r,s,c)=>{n.prototype=Object.create(r.prototype,c),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:r.prototype}),s&&Object.assign(n.prototype,s)},yb=(n,r,s,c)=>{let u,f,h;const g={};if(r=r||{},n==null)return r;do{for(u=Object.getOwnPropertyNames(n),f=u.length;f-- >0;)h=u[f],(!c||c(h,n,r))&&!g[h]&&(r[h]=n[h],g[h]=!0);n=s!==!1&&wu(n)}while(n&&(!s||s(n,r))&&n!==Object.prototype);return r},xb=(n,r,s)=>{n=String(n),(s===void 0||s>n.length)&&(s=n.length),s-=r.length;const c=n.indexOf(r,s);return c!==-1&&c===s},vb=n=>{if(!n)return null;if(rl(n))return n;let r=n.length;if(!Zp(r))return null;const s=new Array(r);for(;r-- >0;)s[r]=n[r];return s},bb=(n=>r=>n&&r instanceof n)(typeof Uint8Array<"u"&&wu(Uint8Array)),Nb=(n,r)=>{const c=(n&&n[li]).call(n);let u;for(;(u=c.next())&&!u.done;){const f=u.value;r.call(n,f[0],f[1])}},wb=(n,r)=>{let s;const c=[];for(;(s=n.exec(r))!==null;)c.push(s);return c},Sb=Gt("HTMLFormElement"),jb=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,c,u){return c.toUpperCase()+u}),Km=(({hasOwnProperty:n})=>(r,s)=>n.call(r,s))(Object.prototype),Eb=Gt("RegExp"),Fp=(n,r)=>{const s=Object.getOwnPropertyDescriptors(n),c={};br(s,(u,f)=>{let h;(h=r(u,f,n))!==!1&&(c[f]=h||u)}),Object.defineProperties(n,c)},Tb=n=>{Fp(n,(r,s)=>{if(ht(n)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const c=n[s];if(ht(c)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},Cb=(n,r)=>{const s={},c=u=>{u.forEach(f=>{s[f]=!0})};return rl(n)?c(n):c(String(n).split(r)),s},Db=()=>{},Ob=(n,r)=>n!=null&&Number.isFinite(n=+n)?n:r;function Rb(n){return!!(n&&ht(n.append)&&n[Pp]==="FormData"&&n[li])}const _b=n=>{const r=new Array(10),s=(c,u)=>{if(ii(c)){if(r.indexOf(c)>=0)return;if(!("toJSON"in c)){r[u]=c;const f=rl(c)?[]:{};return br(c,(h,g)=>{const y=s(h,u+1);!yr(y)&&(f[g]=y)}),r[u]=void 0,f}}return c};return s(n,0)},Ab=Gt("AsyncFunction"),Mb=n=>n&&(ii(n)||ht(n))&&ht(n.then)&&ht(n.catch),Jp=((n,r)=>n?setImmediate:r?((s,c)=>(cn.addEventListener("message",({source:u,data:f})=>{u===cn&&f===s&&c.length&&c.shift()()},!1),u=>{c.push(u),cn.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",ht(cn.postMessage)),kb=typeof queueMicrotask<"u"?queueMicrotask.bind(cn):typeof process<"u"&&process.nextTick||Jp,Ub=n=>n!=null&&ht(n[li]),H={isArray:rl,isArrayBuffer:Qp,isBuffer:Jv,isFormData:ib,isArrayBufferView:Wv,isString:eb,isNumber:Zp,isBoolean:tb,isObject:ii,isPlainObject:$s,isReadableStream:ob,isRequest:ub,isResponse:db,isHeaders:fb,isUndefined:yr,isDate:ab,isFile:nb,isBlob:lb,isRegExp:Eb,isFunction:ht,isStream:sb,isURLSearchParams:cb,isTypedArray:bb,isFileList:rb,forEach:br,merge:Wo,extend:mb,trim:hb,stripBOM:pb,inherits:gb,toFlatObject:yb,kindOf:ri,kindOfTest:Gt,endsWith:xb,toArray:vb,forEachEntry:Nb,matchAll:wb,isHTMLForm:Sb,hasOwnProperty:Km,hasOwnProp:Km,reduceDescriptors:Fp,freezeMethods:Tb,toObjectSet:Cb,toCamelCase:jb,noop:Db,toFiniteNumber:Ob,findKey:Kp,global:cn,isContextDefined:Ip,isSpecCompliantForm:Rb,toJSONObject:_b,isAsyncFn:Ab,isThenable:Mb,setImmediate:Jp,asap:kb,isIterable:Ub};function fe(n,r,s,c,u){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",r&&(this.code=r),s&&(this.config=s),c&&(this.request=c),u&&(this.response=u,this.status=u.status?u.status:null)}H.inherits(fe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:H.toJSONObject(this.config),code:this.code,status:this.status}}});const Wp=fe.prototype,eg={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{eg[n]={value:n}});Object.defineProperties(fe,eg);Object.defineProperty(Wp,"isAxiosError",{value:!0});fe.from=(n,r,s,c,u,f)=>{const h=Object.create(Wp);return H.toFlatObject(n,h,function(y){return y!==Error.prototype},g=>g!=="isAxiosError"),fe.call(h,n.message,r,s,c,u),h.cause=n,h.name=n.name,f&&Object.assign(h,f),h};const zb=null;function eu(n){return H.isPlainObject(n)||H.isArray(n)}function tg(n){return H.endsWith(n,"[]")?n.slice(0,-2):n}function Im(n,r,s){return n?n.concat(r).map(function(u,f){return u=tg(u),!s&&f?"["+u+"]":u}).join(s?".":""):r}function Lb(n){return H.isArray(n)&&!n.some(eu)}const Hb=H.toFlatObject(H,{},null,function(r){return/^is[A-Z]/.test(r)});function ci(n,r,s){if(!H.isObject(n))throw new TypeError("target must be an object");r=r||new FormData,s=H.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(C,S){return!H.isUndefined(S[C])});const c=s.metaTokens,u=s.visitor||x,f=s.dots,h=s.indexes,y=(s.Blob||typeof Blob<"u"&&Blob)&&H.isSpecCompliantForm(r);if(!H.isFunction(u))throw new TypeError("visitor must be a function");function p(T){if(T===null)return"";if(H.isDate(T))return T.toISOString();if(!y&&H.isBlob(T))throw new fe("Blob is not supported. Use a Buffer instead.");return H.isArrayBuffer(T)||H.isTypedArray(T)?y&&typeof Blob=="function"?new Blob([T]):Buffer.from(T):T}function x(T,C,S){let E=T;if(T&&!S&&typeof T=="object"){if(H.endsWith(C,"{}"))C=c?C:C.slice(0,-2),T=JSON.stringify(T);else if(H.isArray(T)&&Lb(T)||(H.isFileList(T)||H.endsWith(C,"[]"))&&(E=H.toArray(T)))return C=tg(C),E.forEach(function($,z){!(H.isUndefined($)||$===null)&&r.append(h===!0?Im([C],z,f):h===null?C:C+"[]",p($))}),!1}return eu(T)?!0:(r.append(Im(S,C,f),p(T)),!1)}const w=[],b=Object.assign(Hb,{defaultVisitor:x,convertValue:p,isVisitable:eu});function R(T,C){if(!H.isUndefined(T)){if(w.indexOf(T)!==-1)throw Error("Circular reference detected in "+C.join("."));w.push(T),H.forEach(T,function(E,k){(!(H.isUndefined(E)||E===null)&&u.call(r,E,H.isString(k)?k.trim():k,C,b))===!0&&R(E,C?C.concat(k):[k])}),w.pop()}}if(!H.isObject(n))throw new TypeError("data must be an object");return R(n),r}function Fm(n){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(c){return r[c]})}function Su(n,r){this._pairs=[],n&&ci(n,this,r)}const ag=Su.prototype;ag.append=function(r,s){this._pairs.push([r,s])};ag.toString=function(r){const s=r?function(c){return r.call(this,c,Fm)}:Fm;return this._pairs.map(function(u){return s(u[0])+"="+s(u[1])},"").join("&")};function Bb(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ng(n,r,s){if(!r)return n;const c=s&&s.encode||Bb;H.isFunction(s)&&(s={serialize:s});const u=s&&s.serialize;let f;if(u?f=u(r,s):f=H.isURLSearchParams(r)?r.toString():new Su(r,s).toString(c),f){const h=n.indexOf("#");h!==-1&&(n=n.slice(0,h)),n+=(n.indexOf("?")===-1?"?":"&")+f}return n}class Jm{constructor(){this.handlers=[]}use(r,s,c){return this.handlers.push({fulfilled:r,rejected:s,synchronous:c?c.synchronous:!1,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){H.forEach(this.handlers,function(c){c!==null&&r(c)})}}const lg={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},qb=typeof URLSearchParams<"u"?URLSearchParams:Su,Yb=typeof FormData<"u"?FormData:null,Gb=typeof Blob<"u"?Blob:null,$b={isBrowser:!0,classes:{URLSearchParams:qb,FormData:Yb,Blob:Gb},protocols:["http","https","file","blob","url","data"]},ju=typeof window<"u"&&typeof document<"u",tu=typeof navigator=="object"&&navigator||void 0,Vb=ju&&(!tu||["ReactNative","NativeScript","NS"].indexOf(tu.product)<0),Xb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Pb=ju&&window.location.href||"http://localhost",Qb=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ju,hasStandardBrowserEnv:Vb,hasStandardBrowserWebWorkerEnv:Xb,navigator:tu,origin:Pb},Symbol.toStringTag,{value:"Module"})),rt={...Qb,...$b};function Zb(n,r){return ci(n,new rt.classes.URLSearchParams,Object.assign({visitor:function(s,c,u,f){return rt.isNode&&H.isBuffer(s)?(this.append(c,s.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},r))}function Kb(n){return H.matchAll(/\w+|\[(\w*)]/g,n).map(r=>r[0]==="[]"?"":r[1]||r[0])}function Ib(n){const r={},s=Object.keys(n);let c;const u=s.length;let f;for(c=0;c<u;c++)f=s[c],r[f]=n[f];return r}function rg(n){function r(s,c,u,f){let h=s[f++];if(h==="__proto__")return!0;const g=Number.isFinite(+h),y=f>=s.length;return h=!h&&H.isArray(u)?u.length:h,y?(H.hasOwnProp(u,h)?u[h]=[u[h],c]:u[h]=c,!g):((!u[h]||!H.isObject(u[h]))&&(u[h]=[]),r(s,c,u[h],f)&&H.isArray(u[h])&&(u[h]=Ib(u[h])),!g)}if(H.isFormData(n)&&H.isFunction(n.entries)){const s={};return H.forEachEntry(n,(c,u)=>{r(Kb(c),u,s,0)}),s}return null}function Fb(n,r,s){if(H.isString(n))try{return(r||JSON.parse)(n),H.trim(n)}catch(c){if(c.name!=="SyntaxError")throw c}return(s||JSON.stringify)(n)}const Nr={transitional:lg,adapter:["xhr","http","fetch"],transformRequest:[function(r,s){const c=s.getContentType()||"",u=c.indexOf("application/json")>-1,f=H.isObject(r);if(f&&H.isHTMLForm(r)&&(r=new FormData(r)),H.isFormData(r))return u?JSON.stringify(rg(r)):r;if(H.isArrayBuffer(r)||H.isBuffer(r)||H.isStream(r)||H.isFile(r)||H.isBlob(r)||H.isReadableStream(r))return r;if(H.isArrayBufferView(r))return r.buffer;if(H.isURLSearchParams(r))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let g;if(f){if(c.indexOf("application/x-www-form-urlencoded")>-1)return Zb(r,this.formSerializer).toString();if((g=H.isFileList(r))||c.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return ci(g?{"files[]":r}:r,y&&new y,this.formSerializer)}}return f||u?(s.setContentType("application/json",!1),Fb(r)):r}],transformResponse:[function(r){const s=this.transitional||Nr.transitional,c=s&&s.forcedJSONParsing,u=this.responseType==="json";if(H.isResponse(r)||H.isReadableStream(r))return r;if(r&&H.isString(r)&&(c&&!this.responseType||u)){const h=!(s&&s.silentJSONParsing)&&u;try{return JSON.parse(r)}catch(g){if(h)throw g.name==="SyntaxError"?fe.from(g,fe.ERR_BAD_RESPONSE,this,null,this.response):g}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rt.classes.FormData,Blob:rt.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};H.forEach(["delete","get","head","post","put","patch"],n=>{Nr.headers[n]={}});const Jb=H.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Wb=n=>{const r={};let s,c,u;return n&&n.split(`
`).forEach(function(h){u=h.indexOf(":"),s=h.substring(0,u).trim().toLowerCase(),c=h.substring(u+1).trim(),!(!s||r[s]&&Jb[s])&&(s==="set-cookie"?r[s]?r[s].push(c):r[s]=[c]:r[s]=r[s]?r[s]+", "+c:c)}),r},Wm=Symbol("internals");function ur(n){return n&&String(n).trim().toLowerCase()}function Vs(n){return n===!1||n==null?n:H.isArray(n)?n.map(Vs):String(n)}function e1(n){const r=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let c;for(;c=s.exec(n);)r[c[1]]=c[2];return r}const t1=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Bo(n,r,s,c,u){if(H.isFunction(c))return c.call(this,r,s);if(u&&(r=s),!!H.isString(r)){if(H.isString(c))return r.indexOf(c)!==-1;if(H.isRegExp(c))return c.test(r)}}function a1(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,s,c)=>s.toUpperCase()+c)}function n1(n,r){const s=H.toCamelCase(" "+r);["get","set","has"].forEach(c=>{Object.defineProperty(n,c+s,{value:function(u,f,h){return this[c].call(this,r,u,f,h)},configurable:!0})})}let mt=class{constructor(r){r&&this.set(r)}set(r,s,c){const u=this;function f(g,y,p){const x=ur(y);if(!x)throw new Error("header name must be a non-empty string");const w=H.findKey(u,x);(!w||u[w]===void 0||p===!0||p===void 0&&u[w]!==!1)&&(u[w||y]=Vs(g))}const h=(g,y)=>H.forEach(g,(p,x)=>f(p,x,y));if(H.isPlainObject(r)||r instanceof this.constructor)h(r,s);else if(H.isString(r)&&(r=r.trim())&&!t1(r))h(Wb(r),s);else if(H.isObject(r)&&H.isIterable(r)){let g={},y,p;for(const x of r){if(!H.isArray(x))throw TypeError("Object iterator must return a key-value pair");g[p=x[0]]=(y=g[p])?H.isArray(y)?[...y,x[1]]:[y,x[1]]:x[1]}h(g,s)}else r!=null&&f(s,r,c);return this}get(r,s){if(r=ur(r),r){const c=H.findKey(this,r);if(c){const u=this[c];if(!s)return u;if(s===!0)return e1(u);if(H.isFunction(s))return s.call(this,u,c);if(H.isRegExp(s))return s.exec(u);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,s){if(r=ur(r),r){const c=H.findKey(this,r);return!!(c&&this[c]!==void 0&&(!s||Bo(this,this[c],c,s)))}return!1}delete(r,s){const c=this;let u=!1;function f(h){if(h=ur(h),h){const g=H.findKey(c,h);g&&(!s||Bo(c,c[g],g,s))&&(delete c[g],u=!0)}}return H.isArray(r)?r.forEach(f):f(r),u}clear(r){const s=Object.keys(this);let c=s.length,u=!1;for(;c--;){const f=s[c];(!r||Bo(this,this[f],f,r,!0))&&(delete this[f],u=!0)}return u}normalize(r){const s=this,c={};return H.forEach(this,(u,f)=>{const h=H.findKey(c,f);if(h){s[h]=Vs(u),delete s[f];return}const g=r?a1(f):String(f).trim();g!==f&&delete s[f],s[g]=Vs(u),c[g]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const s=Object.create(null);return H.forEach(this,(c,u)=>{c!=null&&c!==!1&&(s[u]=r&&H.isArray(c)?c.join(", "):c)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,s])=>r+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...s){const c=new this(r);return s.forEach(u=>c.set(u)),c}static accessor(r){const c=(this[Wm]=this[Wm]={accessors:{}}).accessors,u=this.prototype;function f(h){const g=ur(h);c[g]||(n1(u,h),c[g]=!0)}return H.isArray(r)?r.forEach(f):f(r),this}};mt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);H.reduceDescriptors(mt.prototype,({value:n},r)=>{let s=r[0].toUpperCase()+r.slice(1);return{get:()=>n,set(c){this[s]=c}}});H.freezeMethods(mt);function qo(n,r){const s=this||Nr,c=r||s,u=mt.from(c.headers);let f=c.data;return H.forEach(n,function(g){f=g.call(s,f,u.normalize(),r?r.status:void 0)}),u.normalize(),f}function sg(n){return!!(n&&n.__CANCEL__)}function sl(n,r,s){fe.call(this,n??"canceled",fe.ERR_CANCELED,r,s),this.name="CanceledError"}H.inherits(sl,fe,{__CANCEL__:!0});function ig(n,r,s){const c=s.config.validateStatus;!s.status||!c||c(s.status)?n(s):r(new fe("Request failed with status code "+s.status,[fe.ERR_BAD_REQUEST,fe.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function l1(n){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return r&&r[1]||""}function r1(n,r){n=n||10;const s=new Array(n),c=new Array(n);let u=0,f=0,h;return r=r!==void 0?r:1e3,function(y){const p=Date.now(),x=c[f];h||(h=p),s[u]=y,c[u]=p;let w=f,b=0;for(;w!==u;)b+=s[w++],w=w%n;if(u=(u+1)%n,u===f&&(f=(f+1)%n),p-h<r)return;const R=x&&p-x;return R?Math.round(b*1e3/R):void 0}}function s1(n,r){let s=0,c=1e3/r,u,f;const h=(p,x=Date.now())=>{s=x,u=null,f&&(clearTimeout(f),f=null),n.apply(null,p)};return[(...p)=>{const x=Date.now(),w=x-s;w>=c?h(p,x):(u=p,f||(f=setTimeout(()=>{f=null,h(u)},c-w)))},()=>u&&h(u)]}const Ks=(n,r,s=3)=>{let c=0;const u=r1(50,250);return s1(f=>{const h=f.loaded,g=f.lengthComputable?f.total:void 0,y=h-c,p=u(y),x=h<=g;c=h;const w={loaded:h,total:g,progress:g?h/g:void 0,bytes:y,rate:p||void 0,estimated:p&&g&&x?(g-h)/p:void 0,event:f,lengthComputable:g!=null,[r?"download":"upload"]:!0};n(w)},s)},ep=(n,r)=>{const s=n!=null;return[c=>r[0]({lengthComputable:s,total:n,loaded:c}),r[1]]},tp=n=>(...r)=>H.asap(()=>n(...r)),i1=rt.hasStandardBrowserEnv?((n,r)=>s=>(s=new URL(s,rt.origin),n.protocol===s.protocol&&n.host===s.host&&(r||n.port===s.port)))(new URL(rt.origin),rt.navigator&&/(msie|trident)/i.test(rt.navigator.userAgent)):()=>!0,c1=rt.hasStandardBrowserEnv?{write(n,r,s,c,u,f){const h=[n+"="+encodeURIComponent(r)];H.isNumber(s)&&h.push("expires="+new Date(s).toGMTString()),H.isString(c)&&h.push("path="+c),H.isString(u)&&h.push("domain="+u),f===!0&&h.push("secure"),document.cookie=h.join("; ")},read(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function o1(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function u1(n,r){return r?n.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):n}function cg(n,r,s){let c=!o1(r);return n&&(c||s==!1)?u1(n,r):r}const ap=n=>n instanceof mt?{...n}:n;function dn(n,r){r=r||{};const s={};function c(p,x,w,b){return H.isPlainObject(p)&&H.isPlainObject(x)?H.merge.call({caseless:b},p,x):H.isPlainObject(x)?H.merge({},x):H.isArray(x)?x.slice():x}function u(p,x,w,b){if(H.isUndefined(x)){if(!H.isUndefined(p))return c(void 0,p,w,b)}else return c(p,x,w,b)}function f(p,x){if(!H.isUndefined(x))return c(void 0,x)}function h(p,x){if(H.isUndefined(x)){if(!H.isUndefined(p))return c(void 0,p)}else return c(void 0,x)}function g(p,x,w){if(w in r)return c(p,x);if(w in n)return c(void 0,p)}const y={url:f,method:f,data:f,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:g,headers:(p,x,w)=>u(ap(p),ap(x),w,!0)};return H.forEach(Object.keys(Object.assign({},n,r)),function(x){const w=y[x]||u,b=w(n[x],r[x],x);H.isUndefined(b)&&w!==g||(s[x]=b)}),s}const og=n=>{const r=dn({},n);let{data:s,withXSRFToken:c,xsrfHeaderName:u,xsrfCookieName:f,headers:h,auth:g}=r;r.headers=h=mt.from(h),r.url=ng(cg(r.baseURL,r.url,r.allowAbsoluteUrls),n.params,n.paramsSerializer),g&&h.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let y;if(H.isFormData(s)){if(rt.hasStandardBrowserEnv||rt.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((y=h.getContentType())!==!1){const[p,...x]=y?y.split(";").map(w=>w.trim()).filter(Boolean):[];h.setContentType([p||"multipart/form-data",...x].join("; "))}}if(rt.hasStandardBrowserEnv&&(c&&H.isFunction(c)&&(c=c(r)),c||c!==!1&&i1(r.url))){const p=u&&f&&c1.read(f);p&&h.set(u,p)}return r},d1=typeof XMLHttpRequest<"u",f1=d1&&function(n){return new Promise(function(s,c){const u=og(n);let f=u.data;const h=mt.from(u.headers).normalize();let{responseType:g,onUploadProgress:y,onDownloadProgress:p}=u,x,w,b,R,T;function C(){R&&R(),T&&T(),u.cancelToken&&u.cancelToken.unsubscribe(x),u.signal&&u.signal.removeEventListener("abort",x)}let S=new XMLHttpRequest;S.open(u.method.toUpperCase(),u.url,!0),S.timeout=u.timeout;function E(){if(!S)return;const $=mt.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),Y={data:!g||g==="text"||g==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:$,config:n,request:S};ig(function(se){s(se),C()},function(se){c(se),C()},Y),S=null}"onloadend"in S?S.onloadend=E:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(E)},S.onabort=function(){S&&(c(new fe("Request aborted",fe.ECONNABORTED,n,S)),S=null)},S.onerror=function(){c(new fe("Network Error",fe.ERR_NETWORK,n,S)),S=null},S.ontimeout=function(){let z=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded";const Y=u.transitional||lg;u.timeoutErrorMessage&&(z=u.timeoutErrorMessage),c(new fe(z,Y.clarifyTimeoutError?fe.ETIMEDOUT:fe.ECONNABORTED,n,S)),S=null},f===void 0&&h.setContentType(null),"setRequestHeader"in S&&H.forEach(h.toJSON(),function(z,Y){S.setRequestHeader(Y,z)}),H.isUndefined(u.withCredentials)||(S.withCredentials=!!u.withCredentials),g&&g!=="json"&&(S.responseType=u.responseType),p&&([b,T]=Ks(p,!0),S.addEventListener("progress",b)),y&&S.upload&&([w,R]=Ks(y),S.upload.addEventListener("progress",w),S.upload.addEventListener("loadend",R)),(u.cancelToken||u.signal)&&(x=$=>{S&&(c(!$||$.type?new sl(null,n,S):$),S.abort(),S=null)},u.cancelToken&&u.cancelToken.subscribe(x),u.signal&&(u.signal.aborted?x():u.signal.addEventListener("abort",x)));const k=l1(u.url);if(k&&rt.protocols.indexOf(k)===-1){c(new fe("Unsupported protocol "+k+":",fe.ERR_BAD_REQUEST,n));return}S.send(f||null)})},h1=(n,r)=>{const{length:s}=n=n?n.filter(Boolean):[];if(r||s){let c=new AbortController,u;const f=function(p){if(!u){u=!0,g();const x=p instanceof Error?p:this.reason;c.abort(x instanceof fe?x:new sl(x instanceof Error?x.message:x))}};let h=r&&setTimeout(()=>{h=null,f(new fe(`timeout ${r} of ms exceeded`,fe.ETIMEDOUT))},r);const g=()=>{n&&(h&&clearTimeout(h),h=null,n.forEach(p=>{p.unsubscribe?p.unsubscribe(f):p.removeEventListener("abort",f)}),n=null)};n.forEach(p=>p.addEventListener("abort",f));const{signal:y}=c;return y.unsubscribe=()=>H.asap(g),y}},m1=function*(n,r){let s=n.byteLength;if(s<r){yield n;return}let c=0,u;for(;c<s;)u=c+r,yield n.slice(c,u),c=u},p1=async function*(n,r){for await(const s of g1(n))yield*m1(s,r)},g1=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const r=n.getReader();try{for(;;){const{done:s,value:c}=await r.read();if(s)break;yield c}}finally{await r.cancel()}},np=(n,r,s,c)=>{const u=p1(n,r);let f=0,h,g=y=>{h||(h=!0,c&&c(y))};return new ReadableStream({async pull(y){try{const{done:p,value:x}=await u.next();if(p){g(),y.close();return}let w=x.byteLength;if(s){let b=f+=w;s(b)}y.enqueue(new Uint8Array(x))}catch(p){throw g(p),p}},cancel(y){return g(y),u.return()}},{highWaterMark:2})},oi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ug=oi&&typeof ReadableStream=="function",y1=oi&&(typeof TextEncoder=="function"?(n=>r=>n.encode(r))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),dg=(n,...r)=>{try{return!!n(...r)}catch{return!1}},x1=ug&&dg(()=>{let n=!1;const r=new Request(rt.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!r}),lp=64*1024,au=ug&&dg(()=>H.isReadableStream(new Response("").body)),Is={stream:au&&(n=>n.body)};oi&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Is[r]&&(Is[r]=H.isFunction(n[r])?s=>s[r]():(s,c)=>{throw new fe(`Response type '${r}' is not supported`,fe.ERR_NOT_SUPPORT,c)})})})(new Response);const v1=async n=>{if(n==null)return 0;if(H.isBlob(n))return n.size;if(H.isSpecCompliantForm(n))return(await new Request(rt.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(H.isArrayBufferView(n)||H.isArrayBuffer(n))return n.byteLength;if(H.isURLSearchParams(n)&&(n=n+""),H.isString(n))return(await y1(n)).byteLength},b1=async(n,r)=>{const s=H.toFiniteNumber(n.getContentLength());return s??v1(r)},N1=oi&&(async n=>{let{url:r,method:s,data:c,signal:u,cancelToken:f,timeout:h,onDownloadProgress:g,onUploadProgress:y,responseType:p,headers:x,withCredentials:w="same-origin",fetchOptions:b}=og(n);p=p?(p+"").toLowerCase():"text";let R=h1([u,f&&f.toAbortSignal()],h),T;const C=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let S;try{if(y&&x1&&s!=="get"&&s!=="head"&&(S=await b1(x,c))!==0){let Y=new Request(r,{method:"POST",body:c,duplex:"half"}),Z;if(H.isFormData(c)&&(Z=Y.headers.get("content-type"))&&x.setContentType(Z),Y.body){const[se,Q]=ep(S,Ks(tp(y)));c=np(Y.body,lp,se,Q)}}H.isString(w)||(w=w?"include":"omit");const E="credentials"in Request.prototype;T=new Request(r,{...b,signal:R,method:s.toUpperCase(),headers:x.normalize().toJSON(),body:c,duplex:"half",credentials:E?w:void 0});let k=await fetch(T);const $=au&&(p==="stream"||p==="response");if(au&&(g||$&&C)){const Y={};["status","statusText","headers"].forEach(I=>{Y[I]=k[I]});const Z=H.toFiniteNumber(k.headers.get("content-length")),[se,Q]=g&&ep(Z,Ks(tp(g),!0))||[];k=new Response(np(k.body,lp,se,()=>{Q&&Q(),C&&C()}),Y)}p=p||"text";let z=await Is[H.findKey(Is,p)||"text"](k,n);return!$&&C&&C(),await new Promise((Y,Z)=>{ig(Y,Z,{data:z,headers:mt.from(k.headers),status:k.status,statusText:k.statusText,config:n,request:T})})}catch(E){throw C&&C(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new fe("Network Error",fe.ERR_NETWORK,n,T),{cause:E.cause||E}):fe.from(E,E&&E.code,n,T)}}),nu={http:zb,xhr:f1,fetch:N1};H.forEach(nu,(n,r)=>{if(n){try{Object.defineProperty(n,"name",{value:r})}catch{}Object.defineProperty(n,"adapterName",{value:r})}});const rp=n=>`- ${n}`,w1=n=>H.isFunction(n)||n===null||n===!1,fg={getAdapter:n=>{n=H.isArray(n)?n:[n];const{length:r}=n;let s,c;const u={};for(let f=0;f<r;f++){s=n[f];let h;if(c=s,!w1(s)&&(c=nu[(h=String(s)).toLowerCase()],c===void 0))throw new fe(`Unknown adapter '${h}'`);if(c)break;u[h||"#"+f]=c}if(!c){const f=Object.entries(u).map(([g,y])=>`adapter ${g} `+(y===!1?"is not supported by the environment":"is not available in the build"));let h=r?f.length>1?`since :
`+f.map(rp).join(`
`):" "+rp(f[0]):"as no adapter specified";throw new fe("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return c},adapters:nu};function Yo(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new sl(null,n)}function sp(n){return Yo(n),n.headers=mt.from(n.headers),n.data=qo.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),fg.getAdapter(n.adapter||Nr.adapter)(n).then(function(c){return Yo(n),c.data=qo.call(n,n.transformResponse,c),c.headers=mt.from(c.headers),c},function(c){return sg(c)||(Yo(n),c&&c.response&&(c.response.data=qo.call(n,n.transformResponse,c.response),c.response.headers=mt.from(c.response.headers))),Promise.reject(c)})}const hg="1.9.0",ui={};["object","boolean","number","function","string","symbol"].forEach((n,r)=>{ui[n]=function(c){return typeof c===n||"a"+(r<1?"n ":" ")+n}});const ip={};ui.transitional=function(r,s,c){function u(f,h){return"[Axios v"+hg+"] Transitional option '"+f+"'"+h+(c?". "+c:"")}return(f,h,g)=>{if(r===!1)throw new fe(u(h," has been removed"+(s?" in "+s:"")),fe.ERR_DEPRECATED);return s&&!ip[h]&&(ip[h]=!0,console.warn(u(h," has been deprecated since v"+s+" and will be removed in the near future"))),r?r(f,h,g):!0}};ui.spelling=function(r){return(s,c)=>(console.warn(`${c} is likely a misspelling of ${r}`),!0)};function S1(n,r,s){if(typeof n!="object")throw new fe("options must be an object",fe.ERR_BAD_OPTION_VALUE);const c=Object.keys(n);let u=c.length;for(;u-- >0;){const f=c[u],h=r[f];if(h){const g=n[f],y=g===void 0||h(g,f,n);if(y!==!0)throw new fe("option "+f+" must be "+y,fe.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new fe("Unknown option "+f,fe.ERR_BAD_OPTION)}}const Xs={assertOptions:S1,validators:ui},It=Xs.validators;let on=class{constructor(r){this.defaults=r||{},this.interceptors={request:new Jm,response:new Jm}}async request(r,s){try{return await this._request(r,s)}catch(c){if(c instanceof Error){let u={};Error.captureStackTrace?Error.captureStackTrace(u):u=new Error;const f=u.stack?u.stack.replace(/^.+\n/,""):"";try{c.stack?f&&!String(c.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(c.stack+=`
`+f):c.stack=f}catch{}}throw c}}_request(r,s){typeof r=="string"?(s=s||{},s.url=r):s=r||{},s=dn(this.defaults,s);const{transitional:c,paramsSerializer:u,headers:f}=s;c!==void 0&&Xs.assertOptions(c,{silentJSONParsing:It.transitional(It.boolean),forcedJSONParsing:It.transitional(It.boolean),clarifyTimeoutError:It.transitional(It.boolean)},!1),u!=null&&(H.isFunction(u)?s.paramsSerializer={serialize:u}:Xs.assertOptions(u,{encode:It.function,serialize:It.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Xs.assertOptions(s,{baseUrl:It.spelling("baseURL"),withXsrfToken:It.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let h=f&&H.merge(f.common,f[s.method]);f&&H.forEach(["delete","get","head","post","put","patch","common"],T=>{delete f[T]}),s.headers=mt.concat(h,f);const g=[];let y=!0;this.interceptors.request.forEach(function(C){typeof C.runWhen=="function"&&C.runWhen(s)===!1||(y=y&&C.synchronous,g.unshift(C.fulfilled,C.rejected))});const p=[];this.interceptors.response.forEach(function(C){p.push(C.fulfilled,C.rejected)});let x,w=0,b;if(!y){const T=[sp.bind(this),void 0];for(T.unshift.apply(T,g),T.push.apply(T,p),b=T.length,x=Promise.resolve(s);w<b;)x=x.then(T[w++],T[w++]);return x}b=g.length;let R=s;for(w=0;w<b;){const T=g[w++],C=g[w++];try{R=T(R)}catch(S){C.call(this,S);break}}try{x=sp.call(this,R)}catch(T){return Promise.reject(T)}for(w=0,b=p.length;w<b;)x=x.then(p[w++],p[w++]);return x}getUri(r){r=dn(this.defaults,r);const s=cg(r.baseURL,r.url,r.allowAbsoluteUrls);return ng(s,r.params,r.paramsSerializer)}};H.forEach(["delete","get","head","options"],function(r){on.prototype[r]=function(s,c){return this.request(dn(c||{},{method:r,url:s,data:(c||{}).data}))}});H.forEach(["post","put","patch"],function(r){function s(c){return function(f,h,g){return this.request(dn(g||{},{method:r,headers:c?{"Content-Type":"multipart/form-data"}:{},url:f,data:h}))}}on.prototype[r]=s(),on.prototype[r+"Form"]=s(!0)});let j1=class mg{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(f){s=f});const c=this;this.promise.then(u=>{if(!c._listeners)return;let f=c._listeners.length;for(;f-- >0;)c._listeners[f](u);c._listeners=null}),this.promise.then=u=>{let f;const h=new Promise(g=>{c.subscribe(g),f=g}).then(u);return h.cancel=function(){c.unsubscribe(f)},h},r(function(f,h,g){c.reason||(c.reason=new sl(f,h,g),s(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const s=this._listeners.indexOf(r);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const r=new AbortController,s=c=>{r.abort(c)};return this.subscribe(s),r.signal.unsubscribe=()=>this.unsubscribe(s),r.signal}static source(){let r;return{token:new mg(function(u){r=u}),cancel:r}}};function E1(n){return function(s){return n.apply(null,s)}}function T1(n){return H.isObject(n)&&n.isAxiosError===!0}const lu={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(lu).forEach(([n,r])=>{lu[r]=n});function pg(n){const r=new on(n),s=Xp(on.prototype.request,r);return H.extend(s,on.prototype,r,{allOwnKeys:!0}),H.extend(s,r,null,{allOwnKeys:!0}),s.create=function(u){return pg(dn(n,u))},s}const Le=pg(Nr);Le.Axios=on;Le.CanceledError=sl;Le.CancelToken=j1;Le.isCancel=sg;Le.VERSION=hg;Le.toFormData=ci;Le.AxiosError=fe;Le.Cancel=Le.CanceledError;Le.all=function(r){return Promise.all(r)};Le.spread=E1;Le.isAxiosError=T1;Le.mergeConfig=dn;Le.AxiosHeaders=mt;Le.formToJSON=n=>rg(H.isHTMLForm(n)?new FormData(n):n);Le.getAdapter=fg.getAdapter;Le.HttpStatusCode=lu;Le.default=Le;const{Axios:aj,AxiosError:nj,CanceledError:lj,isCancel:rj,CancelToken:sj,VERSION:ij,all:cj,Cancel:oj,isAxiosError:uj,spread:dj,toFormData:fj,AxiosHeaders:hj,HttpStatusCode:mj,formToJSON:pj,getAdapter:gj,mergeConfig:yj}=Le;class C1{constructor(){_m(this,"api");this.api=Le.create({baseURL:"http://localhost:8000/api",headers:{"Content-Type":"application/json"}}),this.api.interceptors.request.use(r=>{const s=localStorage.getItem("auth_token");return s&&(r.headers.Authorization=`Bearer ${s}`),r},r=>Promise.reject(r)),this.api.interceptors.response.use(r=>r,r=>{var s;return((s=r.response)==null?void 0:s.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/login"),Promise.reject(r)})}async get(r,s){const c=await this.api.get(r,{params:s});if(!c.data.success)throw new Error(c.data.message||"Request failed");return c.data.data}async post(r,s){const c=await this.api.post(r,s);if(!c.data.success)throw new Error(c.data.message||"Request failed");return c.data.data}async put(r,s){const c=await this.api.put(r,s);if(!c.data.success)throw new Error(c.data.message||"Request failed");return c.data.data}async delete(r){const s=await this.api.delete(r);if(!s.data.success)throw new Error(s.data.message||"Request failed");return s.data.data}async upload(r,s){const c=await this.api.post(r,s,{headers:{"Content-Type":"multipart/form-data"}});if(!c.data.success)throw new Error(c.data.message||"Upload failed");return c.data.data}}const Ve=new C1,jt=Ve;class D1{async login(r){return Ve.post("/auth/login",r)}async register(r){return Ve.post("/auth/register",r)}async verifyToken(r){return Ve.get("/auth/verify")}async logout(){return Ve.post("/auth/logout")}}const Go=new D1,gg=N.createContext(void 0),wr=()=>{const n=N.useContext(gg);if(n===void 0)throw new Error("useAuth must be used within an AuthProvider");return n},O1=({children:n})=>{const[r,s]=N.useState(null),[c,u]=N.useState(null),[f,h]=N.useState(!0);N.useEffect(()=>{(async()=>{const R=localStorage.getItem("auth_token");if(R)try{const T=await Go.verifyToken(R);s(T),u(R)}catch{localStorage.removeItem("auth_token"),s(null),u(null)}else s(null),u(null);h(!1)})()},[]);const w={user:r,token:c,login:async b=>{try{const R=await Go.login(b);s(R.user),u(R.token),localStorage.setItem("auth_token",R.token)}catch(R){throw R}},register:async b=>{try{const R=await Go.register(b);s(R.user),u(R.token),localStorage.setItem("auth_token",R.token)}catch(R){throw R}},logout:()=>{s(null),u(null),localStorage.removeItem("auth_token")},updateUser:b=>{s(b)},loading:f};return i.jsx(gg.Provider,{value:w,children:n})},R1=({children:n})=>{const{user:r,loading:s}=wr();return s?i.jsx("div",{className:"min-h-screen flex items-center justify-center",children:i.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})}):r?i.jsx(i.Fragment,{children:n}):i.jsx(vu,{to:"/login",replace:!0})};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _1=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),A1=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,s,c)=>c?c.toUpperCase():s.toLowerCase()),cp=n=>{const r=A1(n);return r.charAt(0).toUpperCase()+r.slice(1)},yg=(...n)=>n.filter((r,s,c)=>!!r&&r.trim()!==""&&c.indexOf(r)===s).join(" ").trim(),M1=n=>{for(const r in n)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var k1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U1=N.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:u="",children:f,iconNode:h,...g},y)=>N.createElement("svg",{ref:y,...k1,width:r,height:r,stroke:n,strokeWidth:c?Number(s)*24/Number(r):s,className:yg("lucide",u),...!f&&!M1(g)&&{"aria-hidden":"true"},...g},[...h.map(([p,x])=>N.createElement(p,x)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=(n,r)=>{const s=N.forwardRef(({className:c,...u},f)=>N.createElement(U1,{ref:f,iconNode:r,className:yg(`lucide-${_1(cp(n))}`,`lucide-${n}`,c),...u}));return s.displayName=cp(n),s};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z1=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],xg=le("arrow-left",z1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L1=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],H1=le("bell",L1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B1=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],q1=le("calendar",B1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y1=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],vg=le("chart-column",Y1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G1=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Eu=le("chevron-down",G1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $1=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],bg=le("chevron-right",$1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],X1=le("circle-alert",V1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P1=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Q1=le("circle-check-big",P1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],K1=le("circle-x",Z1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I1=[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]],F1=le("code",I1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J1=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],W1=le("copy",J1);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eN=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],Ng=le("download",eN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Ps=le("eye-off",tN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ya=le("eye",aN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],un=le("file-text",nN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],rN=le("funnel",lN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sN=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],iN=le("grid-3x3",sN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],Tu=le("image",cN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],op=le("info",oN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]],dN=le("layout-dashboard",uN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fN=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],hN=le("loader-circle",fN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mN=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],ru=le("lock",mN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pN=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],gN=le("log-out",pN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yN=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],ha=le("mail",yN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xN=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],vN=le("menu",xN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bN=[["path",{d:"M5 12h14",key:"1ays0h"}]],wg=le("minus",bN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NN=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],Fs=le("monitor",NN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wN=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],SN=le("moon",wN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jN=[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]],Sg=le("mouse-pointer",jN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EN=[["path",{d:"M12 2v20",key:"t6zp3m"}],["path",{d:"m15 19-3 3-3-3",key:"11eu04"}],["path",{d:"m19 9 3 3-3 3",key:"1mg7y2"}],["path",{d:"M2 12h20",key:"9i4pu4"}],["path",{d:"m5 9-3 3 3 3",key:"j64kie"}],["path",{d:"m9 5 3-3 3 3",key:"l8vdw6"}]],TN=le("move",EN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CN=[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]],jg=le("palette",CN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DN=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]],fr=le("panels-top-left",DN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ON=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],RN=le("pause",ON);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _N=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],AN=le("play",_N);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MN=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],tl=le("plus",MN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kN=[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]],UN=le("redo",kN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zN=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],LN=le("refresh-cw",zN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HN=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Eg=le("rotate-ccw",HN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BN=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],hr=le("save",BN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qN=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],YN=le("search",qN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GN=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Cu=le("send",GN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $N=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],VN=le("settings",$N);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XN=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],PN=le("shopping-cart",XN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QN=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],Js=le("smartphone",QN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZN=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Du=le("square-pen",ZN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KN=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]],su=le("square",KN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IN=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],FN=le("sun",IN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JN=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]],Ws=le("tablet",JN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WN=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Sr=le("trash-2",WN);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e2=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Tg=le("trending-up",e2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t2=[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]],mr=le("type",t2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a2=[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]],n2=le("undo",a2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l2=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],Ou=le("upload",l2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r2=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Ru=le("user",r2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s2=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],fn=le("users",s2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i2=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],jr=le("x",i2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c2=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],o2=le("zoom-in",c2);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u2=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],d2=le("zoom-out",u2),f2=({id:n,type:r,title:s,message:c,duration:u=5e3,onClose:f})=>{N.useEffect(()=>{if(u>0){const p=setTimeout(()=>{f(n)},u);return()=>clearTimeout(p)}},[n,u,f]);const h=()=>{switch(r){case"success":return i.jsx(Q1,{className:"w-5 h-5 text-green-500"});case"error":return i.jsx(K1,{className:"w-5 h-5 text-red-500"});case"warning":return i.jsx(X1,{className:"w-5 h-5 text-yellow-500"});case"info":return i.jsx(op,{className:"w-5 h-5 text-blue-500"});default:return i.jsx(op,{className:"w-5 h-5 text-blue-500"})}},g=()=>{switch(r){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-blue-50 border-blue-200"}},y=()=>{switch(r){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-blue-800"}};return i.jsx("div",{className:`max-w-sm w-full ${g()} border rounded-lg shadow-lg p-4 mb-4 animate-slide-in`,children:i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0",children:h()}),i.jsxs("div",{className:"ml-3 w-0 flex-1",children:[i.jsx("p",{className:`text-sm font-medium ${y()}`,children:s}),c&&i.jsx("p",{className:`mt-1 text-sm ${y()} opacity-90`,children:c})]}),i.jsx("div",{className:"ml-4 flex-shrink-0 flex",children:i.jsxs("button",{className:`inline-flex ${y()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`,onClick:()=>f(n),children:[i.jsx("span",{className:"sr-only",children:"Close"}),i.jsx(jr,{className:"w-4 h-4"})]})})]})})},h2=({notifications:n,onClose:r})=>i.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:n.map(s=>i.jsx(f2,{...s,onClose:r},s.id))}),_u=()=>{const[n,r]=N.useState([]),s=N.useCallback(p=>{const x=Math.random().toString(36).substr(2,9),w={id:x,...p,onClose:c};return r(b=>[...b,w]),x},[]),c=N.useCallback(p=>{r(x=>x.filter(w=>w.id!==p))},[]),u=N.useCallback(()=>{r([])},[]),f=N.useCallback((p,x,w)=>s({type:"success",title:p,message:x,duration:w}),[s]),h=N.useCallback((p,x,w)=>s({type:"error",title:p,message:x,duration:w}),[s]),g=N.useCallback((p,x,w)=>s({type:"warning",title:p,message:x,duration:w}),[s]),y=N.useCallback((p,x,w)=>s({type:"info",title:p,message:x,duration:w}),[s]);return{notifications:n,addNotification:s,removeNotification:c,clearAllNotifications:u,showSuccess:f,showError:h,showWarning:g,showInfo:y}},m2=()=>{const{user:n,login:r,register:s,loading:c}=wr(),[u,f]=N.useState(!0),[h,g]=N.useState(!1),[y,p]=N.useState({name:"",email:"",password:"",confirmPassword:""}),[x,w]=N.useState({}),[b,R]=N.useState(!1);if(n)return i.jsx(vu,{to:"/dashboard",replace:!0});const T=E=>{const{name:k,value:$}=E.target;p(z=>({...z,[k]:$})),x[k]&&w(z=>({...z,[k]:""}))},C=()=>{const E={};return y.email?/\S+@\S+\.\S+/.test(y.email)||(E.email="Email is invalid"):E.email="Email is required",y.password?y.password.length<6&&(E.password="Password must be at least 6 characters"):E.password="Password is required",u||(y.name||(E.name="Name is required"),y.confirmPassword?y.password!==y.confirmPassword&&(E.confirmPassword="Passwords do not match"):E.confirmPassword="Please confirm your password"),w(E),Object.keys(E).length===0},S=async E=>{if(E.preventDefault(),!!C()){R(!0);try{if(u){const k={email:y.email,password:y.password};await r(k)}else{const k={name:y.name,email:y.email,password:y.password,confirmPassword:y.confirmPassword};await s(k)}}catch(k){w({general:k.message||"An error occurred"})}finally{R(!1)}}};return c?i.jsx("div",{className:"min-h-screen flex items-center justify-center",children:i.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})}):i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"max-w-md w-full space-y-8",children:[i.jsxs("div",{children:[i.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:u?"Sign in to your account":"Create your account"}),i.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:[u?"Don't have an account? ":"Already have an account? ",i.jsx("button",{type:"button",onClick:()=>{f(!u),w({}),p({name:"",email:"",password:"",confirmPassword:""})},className:"font-medium text-primary-600 hover:text-primary-500",children:u?"Sign up":"Sign in"})]})]}),i.jsxs("form",{className:"mt-8 space-y-6",onSubmit:S,children:[x.general&&i.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md",children:x.general}),i.jsxs("div",{className:"space-y-4",children:[!u&&i.jsxs("div",{children:[i.jsx("label",{htmlFor:"name",className:"sr-only",children:"Name"}),i.jsxs("div",{className:"relative",children:[i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx(Ru,{className:"h-5 w-5 text-gray-400"})}),i.jsx("input",{id:"name",name:"name",type:"text",value:y.name,onChange:T,className:`input pl-10 ${x.name?"border-red-300":""}`,placeholder:"Full name"})]}),x.name&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.name})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),i.jsxs("div",{className:"relative",children:[i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx(ha,{className:"h-5 w-5 text-gray-400"})}),i.jsx("input",{id:"email",name:"email",type:"email",value:y.email,onChange:T,className:`input pl-10 ${x.email?"border-red-300":""}`,placeholder:"Email address"})]}),x.email&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.email})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),i.jsxs("div",{className:"relative",children:[i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx(ru,{className:"h-5 w-5 text-gray-400"})}),i.jsx("input",{id:"password",name:"password",type:h?"text":"password",value:y.password,onChange:T,className:`input pl-10 pr-10 ${x.password?"border-red-300":""}`,placeholder:"Password"}),i.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!h),children:h?i.jsx(Ps,{className:"h-5 w-5 text-gray-400"}):i.jsx(Ya,{className:"h-5 w-5 text-gray-400"})})]}),x.password&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.password})]}),!u&&i.jsxs("div",{children:[i.jsx("label",{htmlFor:"confirmPassword",className:"sr-only",children:"Confirm Password"}),i.jsxs("div",{className:"relative",children:[i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx(ru,{className:"h-5 w-5 text-gray-400"})}),i.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:"password",value:y.confirmPassword,onChange:T,className:`input pl-10 ${x.confirmPassword?"border-red-300":""}`,placeholder:"Confirm password"})]}),x.confirmPassword&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.confirmPassword})]})]}),i.jsx("div",{children:i.jsx("button",{type:"submit",disabled:b,className:"btn btn-primary w-full h-12 text-base",children:b?i.jsxs("div",{className:"flex items-center justify-center",children:[i.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),u?"Signing in...":"Creating account..."]}):u?"Sign in":"Create account"})})]})]})})},ei={getDashboardStats:async()=>Ve.get("/dashboard/stats"),getStats:async()=>Ve.get("/dashboard/stats"),getCampaigns:async(n=5)=>Ve.get(`/dashboard/campaigns?limit=${n}`),getRecentActivity:async(n=10)=>Ve.get(`/dashboard/activity?limit=${n}`),getCampaignPerformance:async(n=5)=>Ve.get(`/dashboard/campaigns?limit=${n}`),getContactGrowth:async(n=30)=>Ve.get(`/dashboard/contacts/growth?days=${n}`)},p2=()=>{const{user:n}=wr(),[r,s]=N.useState(null),[c,u]=N.useState([]),[f,h]=N.useState(!0),[g,y]=N.useState(!1);N.useEffect(()=>{p()},[]);const p=async()=>{try{h(!0);const[w,b]=await Promise.all([ei.getDashboardStats(),ei.getCampaignPerformance(5)]);s(w),u(b)}catch(w){console.error("Failed to load dashboard data:",w)}finally{h(!1)}},x=async()=>{try{y(!0),await p()}catch(w){console.error("Failed to refresh dashboard:",w)}finally{y(!1)}};return f?i.jsx("div",{className:"p-6 flex items-center justify-center min-h-96",children:i.jsxs("div",{className:"text-center",children:[i.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),i.jsx("p",{className:"text-gray-600",children:"Loading dashboard..."})]})}):r?i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsxs("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",n==null?void 0:n.name,"!"]}),i.jsx("p",{className:"text-gray-600",children:"Here's what's happening with your email campaigns today."})]}),i.jsxs("button",{onClick:x,disabled:g,className:"flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50",children:[i.jsx(LN,{className:`w-4 h-4 mr-2 ${g?"animate-spin":""}`}),"Refresh"]})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[i.jsx("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-200",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-3 bg-blue-100 rounded-xl",children:i.jsx(ha,{className:"w-6 h-6 text-blue-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Campaigns"}),i.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalCampaigns})]})]})}),i.jsx("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-200",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-3 bg-green-100 rounded-xl",children:i.jsx(fn,{className:"w-6 h-6 text-green-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Contacts"}),i.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalContacts.toLocaleString()})]})]})}),i.jsx("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-200",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-3 bg-purple-100 rounded-xl",children:i.jsx(un,{className:"w-6 h-6 text-purple-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Templates"}),i.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalTemplates})]})]})}),i.jsx("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-200",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-3 bg-orange-100 rounded-xl",children:i.jsx(Cu,{className:"w-6 h-6 text-orange-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Emails Sent"}),i.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.emailsSent.toLocaleString()})]})]})})]}),i.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[i.jsxs("div",{className:"card p-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Overview"}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx(Ya,{className:"w-5 h-5 text-blue-500 mr-2"}),i.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Open Rate"})]}),i.jsxs("span",{className:"text-lg font-bold text-gray-900",children:[r.openRate,"%"]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx(Tg,{className:"w-5 h-5 text-green-500 mr-2"}),i.jsx("span",{className:"text-sm font-medium text-gray-600",children:"Click Rate"})]}),i.jsxs("span",{className:"text-lg font-bold text-gray-900",children:[r.clickRate,"%"]})]})]})]}),i.jsxs("div",{className:"card p-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),i.jsxs("div",{className:"space-y-3",children:[i.jsxs(lt,{to:"/campaigns/new",className:"flex items-center p-3 bg-primary-50 hover:bg-primary-100 rounded-lg transition-colors",children:[i.jsx(tl,{className:"w-5 h-5 text-primary-600 mr-3"}),i.jsx("span",{className:"font-medium text-primary-700",children:"Create New Campaign"})]}),i.jsxs(lt,{to:"/templates/new",className:"flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors",children:[i.jsx(tl,{className:"w-5 h-5 text-gray-600 mr-3"}),i.jsx("span",{className:"font-medium text-gray-700",children:"Create New Template"})]}),i.jsxs(lt,{to:"/contacts",className:"flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors",children:[i.jsx(fn,{className:"w-5 h-5 text-gray-600 mr-3"}),i.jsx("span",{className:"font-medium text-gray-700",children:"Manage Contacts"})]})]})]})]}),i.jsxs("div",{className:"card",children:[i.jsx("div",{className:"p-6 border-b",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Campaigns"}),i.jsx(lt,{to:"/campaigns",className:"text-sm font-medium text-primary-600 hover:text-primary-500",children:"View all"})]})}),i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[i.jsx("thead",{className:"bg-gray-50",children:i.jsxs("tr",{children:[i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campaign"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sent"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Opened"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Clicked"})]})}),i.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.length===0?i.jsx("tr",{children:i.jsxs("td",{colSpan:5,className:"px-6 py-8 text-center text-gray-500",children:["No campaigns found. ",i.jsx(lt,{to:"/campaigns/new",className:"text-blue-600 hover:text-blue-500",children:"Create your first campaign"})]})}):c.map(w=>i.jsxs("tr",{className:"hover:bg-gray-50",children:[i.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[i.jsx("div",{className:"text-sm font-medium text-gray-900",children:w.name}),w.sentAt&&i.jsxs("div",{className:"text-sm text-gray-500",children:["Sent on ",new Date(w.sentAt).toLocaleDateString()]})]}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:i.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"sent"})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:w.sent.toLocaleString()}),i.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[w.opened.toLocaleString(),i.jsxs("span",{className:"text-gray-500 ml-1",children:["(",w.openRate,"%)"]})]}),i.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[w.clicked.toLocaleString(),i.jsxs("span",{className:"text-gray-500 ml-1",children:["(",w.clickRate,"%)"]})]})]},w.id))})]})})]})]}):i.jsx("div",{className:"p-6 flex items-center justify-center min-h-96",children:i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"text-gray-600 mb-4",children:"Failed to load dashboard data"}),i.jsx("button",{onClick:p,className:"btn btn-primary",children:"Try Again"})]})})},g2=()=>{const n=[{id:1,name:"Newsletter Template",description:"A clean and modern newsletter template",thumbnail:null,created_at:"2024-01-10",updated_at:"2024-01-12"},{id:2,name:"Product Announcement",description:"Template for announcing new products",thumbnail:null,created_at:"2024-01-08",updated_at:"2024-01-08"},{id:3,name:"Welcome Email",description:"Welcome new subscribers with this template",thumbnail:null,created_at:"2024-01-05",updated_at:"2024-01-06"}];return i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center justify-between mb-8",children:[i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Email Templates"}),i.jsx("p",{className:"text-gray-600",children:"Create and manage your email templates"})]}),i.jsxs(lt,{to:"/templates/new",className:"btn btn-primary flex items-center",children:[i.jsx(tl,{className:"w-4 h-4 mr-2"}),"Create Template"]})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(r=>i.jsxs("div",{className:"card overflow-hidden",children:[i.jsx("div",{className:"h-48 bg-gray-100 flex items-center justify-center",children:r.thumbnail?i.jsx("img",{src:r.thumbnail,alt:r.name,className:"w-full h-full object-cover"}):i.jsx(un,{className:"w-16 h-16 text-gray-400"})}),i.jsxs("div",{className:"p-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r.name}),i.jsx("p",{className:"text-sm text-gray-600 mb-4",children:r.description}),i.jsxs("div",{className:"text-xs text-gray-500 mb-4",children:["Updated ",new Date(r.updated_at).toLocaleDateString()]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsxs(lt,{to:`/templates/edit/${r.id}`,className:"btn btn-outline flex-1 flex items-center justify-center",children:[i.jsx(Du,{className:"w-4 h-4 mr-1"}),"Edit"]}),i.jsx("button",{className:"btn btn-outline p-2",children:i.jsx(W1,{className:"w-4 h-4"})}),i.jsx("button",{className:"btn btn-outline p-2 text-red-600 hover:text-red-700",children:i.jsx(Sr,{className:"w-4 h-4"})})]})]})]},r.id))}),n.length===0&&i.jsxs("div",{className:"text-center py-12",children:[i.jsx(un,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No templates yet"}),i.jsx("p",{className:"text-gray-600 mb-6",children:"Get started by creating your first email template"}),i.jsx(lt,{to:"/templates/new",className:"btn btn-primary",children:"Create Your First Template"})]})]})},Cg=N.createContext({dragDropManager:void 0});function kt(n){return"Minified Redux error #"+n+"; visit https://redux.js.org/Errors?code="+n+" for the full message or use the non-minified dev environment for full errors. "}var up=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),dp=function(){return Math.random().toString(36).substring(7).split("").join(".")},fp={INIT:"@@redux/INIT"+dp(),REPLACE:"@@redux/REPLACE"+dp()};function y2(n){if(typeof n!="object"||n===null)return!1;for(var r=n;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(n)===r}function Dg(n,r,s){var c;if(typeof r=="function"&&typeof s=="function"||typeof s=="function"&&typeof arguments[3]=="function")throw new Error(kt(0));if(typeof r=="function"&&typeof s>"u"&&(s=r,r=void 0),typeof s<"u"){if(typeof s!="function")throw new Error(kt(1));return s(Dg)(n,r)}if(typeof n!="function")throw new Error(kt(2));var u=n,f=r,h=[],g=h,y=!1;function p(){g===h&&(g=h.slice())}function x(){if(y)throw new Error(kt(3));return f}function w(C){if(typeof C!="function")throw new Error(kt(4));if(y)throw new Error(kt(5));var S=!0;return p(),g.push(C),function(){if(S){if(y)throw new Error(kt(6));S=!1,p();var k=g.indexOf(C);g.splice(k,1),h=null}}}function b(C){if(!y2(C))throw new Error(kt(7));if(typeof C.type>"u")throw new Error(kt(8));if(y)throw new Error(kt(9));try{y=!0,f=u(f,C)}finally{y=!1}for(var S=h=g,E=0;E<S.length;E++){var k=S[E];k()}return C}function R(C){if(typeof C!="function")throw new Error(kt(10));u=C,b({type:fp.REPLACE})}function T(){var C,S=w;return C={subscribe:function(k){if(typeof k!="object"||k===null)throw new Error(kt(11));function $(){k.next&&k.next(x())}$();var z=S($);return{unsubscribe:z}}},C[up]=function(){return this},C}return b({type:fp.INIT}),c={dispatch:b,subscribe:w,getState:x,replaceReducer:R},c[up]=T,c}function he(n,r,...s){if(x2()&&r===void 0)throw new Error("invariant requires an error message argument");if(!n){let c;if(r===void 0)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let u=0;c=new Error(r.replace(/%s/g,function(){return s[u++]})),c.name="Invariant Violation"}throw c.framesToPop=1,c}}function x2(){return typeof process<"u"&&!0}function v2(n,r,s){return r.split(".").reduce((c,u)=>c&&c[u]?c[u]:s||null,n)}function b2(n,r){return n.filter(s=>s!==r)}function Og(n){return typeof n=="object"}function N2(n,r){const s=new Map,c=f=>{s.set(f,s.has(f)?s.get(f)+1:1)};n.forEach(c),r.forEach(c);const u=[];return s.forEach((f,h)=>{f===1&&u.push(h)}),u}function w2(n,r){return n.filter(s=>r.indexOf(s)>-1)}const Au="dnd-core/INIT_COORDS",di="dnd-core/BEGIN_DRAG",Mu="dnd-core/PUBLISH_DRAG_SOURCE",fi="dnd-core/HOVER",hi="dnd-core/DROP",mi="dnd-core/END_DRAG";function hp(n,r){return{type:Au,payload:{sourceClientOffset:r||null,clientOffset:n||null}}}const S2={type:Au,payload:{clientOffset:null,sourceClientOffset:null}};function j2(n){return function(s=[],c={publishSource:!0}){const{publishSource:u=!0,clientOffset:f,getSourceClientOffset:h}=c,g=n.getMonitor(),y=n.getRegistry();n.dispatch(hp(f)),E2(s,g,y);const p=D2(s,g);if(p==null){n.dispatch(S2);return}let x=null;if(f){if(!h)throw new Error("getSourceClientOffset must be defined");T2(h),x=h(p)}n.dispatch(hp(f,x));const b=y.getSource(p).beginDrag(g,p);if(b==null)return;C2(b),y.pinSource(p);const R=y.getSourceType(p);return{type:di,payload:{itemType:R,item:b,sourceId:p,clientOffset:f||null,sourceClientOffset:x||null,isSourcePublic:!!u}}}}function E2(n,r,s){he(!r.isDragging(),"Cannot call beginDrag while dragging."),n.forEach(function(c){he(s.getSource(c),"Expected sourceIds to be registered.")})}function T2(n){he(typeof n=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function C2(n){he(Og(n),"Item must be an object.")}function D2(n,r){let s=null;for(let c=n.length-1;c>=0;c--)if(r.canDragSource(n[c])){s=n[c];break}return s}function O2(n,r,s){return r in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s,n}function R2(n){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{},c=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(c=c.concat(Object.getOwnPropertySymbols(s).filter(function(u){return Object.getOwnPropertyDescriptor(s,u).enumerable}))),c.forEach(function(u){O2(n,u,s[u])})}return n}function _2(n){return function(s={}){const c=n.getMonitor(),u=n.getRegistry();A2(c),U2(c).forEach((h,g)=>{const y=M2(h,g,u,c),p={type:hi,payload:{dropResult:R2({},s,y)}};n.dispatch(p)})}}function A2(n){he(n.isDragging(),"Cannot call drop while not dragging."),he(!n.didDrop(),"Cannot call drop twice during one drag operation.")}function M2(n,r,s,c){const u=s.getTarget(n);let f=u?u.drop(c,n):void 0;return k2(f),typeof f>"u"&&(f=r===0?{}:c.getDropResult()),f}function k2(n){he(typeof n>"u"||Og(n),"Drop result must either be an object or undefined.")}function U2(n){const r=n.getTargetIds().filter(n.canDropOnTarget,n);return r.reverse(),r}function z2(n){return function(){const s=n.getMonitor(),c=n.getRegistry();L2(s);const u=s.getSourceId();return u!=null&&(c.getSource(u,!0).endDrag(s,u),c.unpinSource()),{type:mi}}}function L2(n){he(n.isDragging(),"Cannot call endDrag while not dragging.")}function iu(n,r){return r===null?n===null:Array.isArray(n)?n.some(s=>s===r):n===r}function H2(n){return function(s,{clientOffset:c}={}){B2(s);const u=s.slice(0),f=n.getMonitor(),h=n.getRegistry(),g=f.getItemType();return Y2(u,h,g),q2(u,f,h),G2(u,f,h),{type:fi,payload:{targetIds:u,clientOffset:c||null}}}}function B2(n){he(Array.isArray(n),"Expected targetIds to be an array.")}function q2(n,r,s){he(r.isDragging(),"Cannot call hover while not dragging."),he(!r.didDrop(),"Cannot call hover after drop.");for(let c=0;c<n.length;c++){const u=n[c];he(n.lastIndexOf(u)===c,"Expected targetIds to be unique in the passed array.");const f=s.getTarget(u);he(f,"Expected targetIds to be registered.")}}function Y2(n,r,s){for(let c=n.length-1;c>=0;c--){const u=n[c],f=r.getTargetType(u);iu(f,s)||n.splice(c,1)}}function G2(n,r,s){n.forEach(function(c){s.getTarget(c).hover(r,c)})}function $2(n){return function(){if(n.getMonitor().isDragging())return{type:Mu}}}function V2(n){return{beginDrag:j2(n),publishDragSource:$2(n),hover:H2(n),drop:_2(n),endDrag:z2(n)}}class X2{receiveBackend(r){this.backend=r}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const r=this,{dispatch:s}=this.store;function c(f){return(...h)=>{const g=f.apply(r,h);typeof g<"u"&&s(g)}}const u=V2(this);return Object.keys(u).reduce((f,h)=>{const g=u[h];return f[h]=c(g),f},{})}dispatch(r){this.store.dispatch(r)}constructor(r,s){this.isSetUp=!1,this.handleRefCountChange=()=>{const c=this.store.getState().refCount>0;this.backend&&(c&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!c&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=r,this.monitor=s,r.subscribe(this.handleRefCountChange)}}function P2(n,r){return{x:n.x+r.x,y:n.y+r.y}}function Rg(n,r){return{x:n.x-r.x,y:n.y-r.y}}function Q2(n){const{clientOffset:r,initialClientOffset:s,initialSourceClientOffset:c}=n;return!r||!s||!c?null:Rg(P2(r,c),s)}function Z2(n){const{clientOffset:r,initialClientOffset:s}=n;return!r||!s?null:Rg(r,s)}const pr=[],ku=[];pr.__IS_NONE__=!0;ku.__IS_ALL__=!0;function K2(n,r){return n===pr?!1:n===ku||typeof r>"u"?!0:w2(r,n).length>0}class I2{subscribeToStateChange(r,s={}){const{handlerIds:c}=s;he(typeof r=="function","listener must be a function."),he(typeof c>"u"||Array.isArray(c),"handlerIds, when specified, must be an array of strings.");let u=this.store.getState().stateId;const f=()=>{const h=this.store.getState(),g=h.stateId;try{g===u||g===u+1&&!K2(h.dirtyHandlerIds,c)||r()}finally{u=g}};return this.store.subscribe(f)}subscribeToOffsetChange(r){he(typeof r=="function","listener must be a function.");let s=this.store.getState().dragOffset;const c=()=>{const u=this.store.getState().dragOffset;u!==s&&(s=u,r())};return this.store.subscribe(c)}canDragSource(r){if(!r)return!1;const s=this.registry.getSource(r);return he(s,`Expected to find a valid source. sourceId=${r}`),this.isDragging()?!1:s.canDrag(this,r)}canDropOnTarget(r){if(!r)return!1;const s=this.registry.getTarget(r);if(he(s,`Expected to find a valid target. targetId=${r}`),!this.isDragging()||this.didDrop())return!1;const c=this.registry.getTargetType(r),u=this.getItemType();return iu(c,u)&&s.canDrop(this,r)}isDragging(){return!!this.getItemType()}isDraggingSource(r){if(!r)return!1;const s=this.registry.getSource(r,!0);if(he(s,`Expected to find a valid source. sourceId=${r}`),!this.isDragging()||!this.isSourcePublic())return!1;const c=this.registry.getSourceType(r),u=this.getItemType();return c!==u?!1:s.isDragging(this,r)}isOverTarget(r,s={shallow:!1}){if(!r)return!1;const{shallow:c}=s;if(!this.isDragging())return!1;const u=this.registry.getTargetType(r),f=this.getItemType();if(f&&!iu(u,f))return!1;const h=this.getTargetIds();if(!h.length)return!1;const g=h.indexOf(r);return c?g===h.length-1:g>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return Q2(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return Z2(this.store.getState().dragOffset)}constructor(r,s){this.store=r,this.registry=s}}const mp=typeof global<"u"?global:self,_g=mp.MutationObserver||mp.WebKitMutationObserver;function Ag(n){return function(){const s=setTimeout(u,0),c=setInterval(u,50);function u(){clearTimeout(s),clearInterval(c),n()}}}function F2(n){let r=1;const s=new _g(n),c=document.createTextNode("");return s.observe(c,{characterData:!0}),function(){r=-r,c.data=r}}const J2=typeof _g=="function"?F2:Ag;class W2{enqueueTask(r){const{queue:s,requestFlush:c}=this;s.length||(c(),this.flushing=!0),s[s.length]=r}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:r}=this;for(;this.index<r.length;){const s=this.index;if(this.index++,r[s].call(),this.index>this.capacity){for(let c=0,u=r.length-this.index;c<u;c++)r[c]=r[c+this.index];r.length-=this.index,this.index=0}}r.length=0,this.index=0,this.flushing=!1},this.registerPendingError=r=>{this.pendingErrors.push(r),this.requestErrorThrow()},this.requestFlush=J2(this.flush),this.requestErrorThrow=Ag(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class ew{call(){try{this.task&&this.task()}catch(r){this.onError(r)}finally{this.task=null,this.release(this)}}constructor(r,s){this.onError=r,this.release=s,this.task=null}}class tw{create(r){const s=this.freeTasks,c=s.length?s.pop():new ew(this.onError,u=>s[s.length]=u);return c.task=r,c}constructor(r){this.onError=r,this.freeTasks=[]}}const Mg=new W2,aw=new tw(Mg.registerPendingError);function nw(n){Mg.enqueueTask(aw.create(n))}const Uu="dnd-core/ADD_SOURCE",zu="dnd-core/ADD_TARGET",Lu="dnd-core/REMOVE_SOURCE",pi="dnd-core/REMOVE_TARGET";function lw(n){return{type:Uu,payload:{sourceId:n}}}function rw(n){return{type:zu,payload:{targetId:n}}}function sw(n){return{type:Lu,payload:{sourceId:n}}}function iw(n){return{type:pi,payload:{targetId:n}}}function cw(n){he(typeof n.canDrag=="function","Expected canDrag to be a function."),he(typeof n.beginDrag=="function","Expected beginDrag to be a function."),he(typeof n.endDrag=="function","Expected endDrag to be a function.")}function ow(n){he(typeof n.canDrop=="function","Expected canDrop to be a function."),he(typeof n.hover=="function","Expected hover to be a function."),he(typeof n.drop=="function","Expected beginDrag to be a function.")}function cu(n,r){if(r&&Array.isArray(n)){n.forEach(s=>cu(s,!1));return}he(typeof n=="string"||typeof n=="symbol",r?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var Ut;(function(n){n.SOURCE="SOURCE",n.TARGET="TARGET"})(Ut||(Ut={}));let uw=0;function dw(){return uw++}function fw(n){const r=dw().toString();switch(n){case Ut.SOURCE:return`S${r}`;case Ut.TARGET:return`T${r}`;default:throw new Error(`Unknown Handler Role: ${n}`)}}function pp(n){switch(n[0]){case"S":return Ut.SOURCE;case"T":return Ut.TARGET;default:throw new Error(`Cannot parse handler ID: ${n}`)}}function gp(n,r){const s=n.entries();let c=!1;do{const{done:u,value:[,f]}=s.next();if(f===r)return!0;c=!!u}while(!c);return!1}class hw{addSource(r,s){cu(r),cw(s);const c=this.addHandler(Ut.SOURCE,r,s);return this.store.dispatch(lw(c)),c}addTarget(r,s){cu(r,!0),ow(s);const c=this.addHandler(Ut.TARGET,r,s);return this.store.dispatch(rw(c)),c}containsHandler(r){return gp(this.dragSources,r)||gp(this.dropTargets,r)}getSource(r,s=!1){return he(this.isSourceId(r),"Expected a valid source ID."),s&&r===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(r)}getTarget(r){return he(this.isTargetId(r),"Expected a valid target ID."),this.dropTargets.get(r)}getSourceType(r){return he(this.isSourceId(r),"Expected a valid source ID."),this.types.get(r)}getTargetType(r){return he(this.isTargetId(r),"Expected a valid target ID."),this.types.get(r)}isSourceId(r){return pp(r)===Ut.SOURCE}isTargetId(r){return pp(r)===Ut.TARGET}removeSource(r){he(this.getSource(r),"Expected an existing source."),this.store.dispatch(sw(r)),nw(()=>{this.dragSources.delete(r),this.types.delete(r)})}removeTarget(r){he(this.getTarget(r),"Expected an existing target."),this.store.dispatch(iw(r)),this.dropTargets.delete(r),this.types.delete(r)}pinSource(r){const s=this.getSource(r);he(s,"Expected an existing source."),this.pinnedSourceId=r,this.pinnedSource=s}unpinSource(){he(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(r,s,c){const u=fw(r);return this.types.set(u,s),r===Ut.SOURCE?this.dragSources.set(u,c):r===Ut.TARGET&&this.dropTargets.set(u,c),u}constructor(r){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=r}}const mw=(n,r)=>n===r;function pw(n,r){return!n&&!r?!0:!n||!r?!1:n.x===r.x&&n.y===r.y}function gw(n,r,s=mw){if(n.length!==r.length)return!1;for(let c=0;c<n.length;++c)if(!s(n[c],r[c]))return!1;return!0}function yw(n=pr,r){switch(r.type){case fi:break;case Uu:case zu:case pi:case Lu:return pr;case di:case Mu:case mi:case hi:default:return ku}const{targetIds:s=[],prevTargetIds:c=[]}=r.payload,u=N2(s,c);if(!(u.length>0||!gw(s,c)))return pr;const h=c[c.length-1],g=s[s.length-1];return h!==g&&(h&&u.push(h),g&&u.push(g)),u}function xw(n,r,s){return r in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s,n}function vw(n){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{},c=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(c=c.concat(Object.getOwnPropertySymbols(s).filter(function(u){return Object.getOwnPropertyDescriptor(s,u).enumerable}))),c.forEach(function(u){xw(n,u,s[u])})}return n}const yp={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function bw(n=yp,r){const{payload:s}=r;switch(r.type){case Au:case di:return{initialSourceClientOffset:s.sourceClientOffset,initialClientOffset:s.clientOffset,clientOffset:s.clientOffset};case fi:return pw(n.clientOffset,s.clientOffset)?n:vw({},n,{clientOffset:s.clientOffset});case mi:case hi:return yp;default:return n}}function Nw(n,r,s){return r in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s,n}function Wn(n){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{},c=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(c=c.concat(Object.getOwnPropertySymbols(s).filter(function(u){return Object.getOwnPropertyDescriptor(s,u).enumerable}))),c.forEach(function(u){Nw(n,u,s[u])})}return n}const ww={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function Sw(n=ww,r){const{payload:s}=r;switch(r.type){case di:return Wn({},n,{itemType:s.itemType,item:s.item,sourceId:s.sourceId,isSourcePublic:s.isSourcePublic,dropResult:null,didDrop:!1});case Mu:return Wn({},n,{isSourcePublic:!0});case fi:return Wn({},n,{targetIds:s.targetIds});case pi:return n.targetIds.indexOf(s.targetId)===-1?n:Wn({},n,{targetIds:b2(n.targetIds,s.targetId)});case hi:return Wn({},n,{dropResult:s.dropResult,didDrop:!0,targetIds:[]});case mi:return Wn({},n,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return n}}function jw(n=0,r){switch(r.type){case Uu:case zu:return n+1;case Lu:case pi:return n-1;default:return n}}function Ew(n=0){return n+1}function Tw(n,r,s){return r in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s,n}function Cw(n){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{},c=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(c=c.concat(Object.getOwnPropertySymbols(s).filter(function(u){return Object.getOwnPropertyDescriptor(s,u).enumerable}))),c.forEach(function(u){Tw(n,u,s[u])})}return n}function Dw(n={},r){return{dirtyHandlerIds:yw(n.dirtyHandlerIds,{type:r.type,payload:Cw({},r.payload,{prevTargetIds:v2(n,"dragOperation.targetIds",[])})}),dragOffset:bw(n.dragOffset,r),refCount:jw(n.refCount,r),dragOperation:Sw(n.dragOperation,r),stateId:Ew(n.stateId)}}function Ow(n,r=void 0,s={},c=!1){const u=Rw(c),f=new I2(u,new hw(u)),h=new X2(u,f),g=n(h,r,s);return h.receiveBackend(g),h}function Rw(n){const r=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return Dg(Dw,n&&r&&r({name:"dnd-core",instanceId:"dnd-core"}))}function _w(n,r){if(n==null)return{};var s=Aw(n,r),c,u;if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(n);for(u=0;u<f.length;u++)c=f[u],!(r.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(n,c)&&(s[c]=n[c])}return s}function Aw(n,r){if(n==null)return{};var s={},c=Object.keys(n),u,f;for(f=0;f<c.length;f++)u=c[f],!(r.indexOf(u)>=0)&&(s[u]=n[u]);return s}let xp=0;const Qs=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var Mw=N.memo(function(r){var{children:s}=r,c=_w(r,["children"]);const[u,f]=kw(c);return N.useEffect(()=>{if(f){const h=kg();return++xp,()=>{--xp===0&&(h[Qs]=null)}}},[]),i.jsx(Cg.Provider,{value:u,children:s})});function kw(n){if("manager"in n)return[{dragDropManager:n.manager},!1];const r=Uw(n.backend,n.context,n.options,n.debugMode),s=!n.context;return[r,s]}function Uw(n,r=kg(),s,c){const u=r;return u[Qs]||(u[Qs]={dragDropManager:Ow(n,r,s,c)}),u[Qs]}function kg(){return typeof global<"u"?global:window}var $o,vp;function zw(){return vp||(vp=1,$o=function n(r,s){if(r===s)return!0;if(r&&s&&typeof r=="object"&&typeof s=="object"){if(r.constructor!==s.constructor)return!1;var c,u,f;if(Array.isArray(r)){if(c=r.length,c!=s.length)return!1;for(u=c;u--!==0;)if(!n(r[u],s[u]))return!1;return!0}if(r.constructor===RegExp)return r.source===s.source&&r.flags===s.flags;if(r.valueOf!==Object.prototype.valueOf)return r.valueOf()===s.valueOf();if(r.toString!==Object.prototype.toString)return r.toString()===s.toString();if(f=Object.keys(r),c=f.length,c!==Object.keys(s).length)return!1;for(u=c;u--!==0;)if(!Object.prototype.hasOwnProperty.call(s,f[u]))return!1;for(u=c;u--!==0;){var h=f[u];if(!n(r[h],s[h]))return!1}return!0}return r!==r&&s!==s}),$o}var Lw=zw();const Hw=fu(Lw),hn=typeof window<"u"?N.useLayoutEffect:N.useEffect;function Bw(n,r,s){const[c,u]=N.useState(()=>r(n)),f=N.useCallback(()=>{const h=r(n);Hw(c,h)||(u(h),s&&s())},[c,n,s]);return hn(f),[c,f]}function qw(n,r,s){const[c,u]=Bw(n,r,s);return hn(function(){const h=n.getHandlerId();if(h!=null)return n.subscribeToStateChange(u,{handlerIds:[h]})},[n,u]),c}function Ug(n,r,s){return qw(r,n||(()=>({})),()=>s.reconnect())}function zg(n,r){const s=[];return typeof n!="function"&&s.push(n),N.useMemo(()=>typeof n=="function"?n():n,s)}function Yw(n){return N.useMemo(()=>n.hooks.dragSource(),[n])}function Gw(n){return N.useMemo(()=>n.hooks.dragPreview(),[n])}let Vo=!1,Xo=!1;class $w{receiveHandlerId(r){this.sourceId=r}getHandlerId(){return this.sourceId}canDrag(){he(!Vo,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Vo=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{Vo=!1}}isDragging(){if(!this.sourceId)return!1;he(!Xo,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Xo=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{Xo=!1}}subscribeToStateChange(r,s){return this.internalMonitor.subscribeToStateChange(r,s)}isDraggingSource(r){return this.internalMonitor.isDraggingSource(r)}isOverTarget(r,s){return this.internalMonitor.isOverTarget(r,s)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(r){return this.internalMonitor.subscribeToOffsetChange(r)}canDragSource(r){return this.internalMonitor.canDragSource(r)}canDropOnTarget(r){return this.internalMonitor.canDropOnTarget(r)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(r){this.sourceId=null,this.internalMonitor=r.getMonitor()}}let Po=!1;class Vw{receiveHandlerId(r){this.targetId=r}getHandlerId(){return this.targetId}subscribeToStateChange(r,s){return this.internalMonitor.subscribeToStateChange(r,s)}canDrop(){if(!this.targetId)return!1;he(!Po,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Po=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Po=!1}}isOver(r){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,r):!1}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(r){this.targetId=null,this.internalMonitor=r.getMonitor()}}function Xw(n,r,s){const c=s.getRegistry(),u=c.addTarget(n,r);return[u,()=>c.removeTarget(u)]}function Pw(n,r,s){const c=s.getRegistry(),u=c.addSource(n,r);return[u,()=>c.removeSource(u)]}function ou(n,r,s,c){let u;if(u!==void 0)return!!u;if(n===r)return!0;if(typeof n!="object"||!n||typeof r!="object"||!r)return!1;const f=Object.keys(n),h=Object.keys(r);if(f.length!==h.length)return!1;const g=Object.prototype.hasOwnProperty.bind(r);for(let y=0;y<f.length;y++){const p=f[y];if(!g(p))return!1;const x=n[p],w=r[p];if(u=void 0,u===!1||u===void 0&&x!==w)return!1}return!0}function uu(n){return n!==null&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function Qw(n){if(typeof n.type=="string")return;const r=n.type.displayName||n.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${r} into a <div>, or turn it into a drag source or a drop target itself.`)}function Zw(n){return(r=null,s=null)=>{if(!N.isValidElement(r)){const f=r;return n(f,s),f}const c=r;return Qw(c),Kw(c,s?f=>n(f,s):n)}}function Lg(n){const r={};return Object.keys(n).forEach(s=>{const c=n[s];if(s.endsWith("Ref"))r[s]=n[s];else{const u=Zw(c);r[s]=()=>u}}),r}function bp(n,r){typeof n=="function"?n(r):n.current=r}function Kw(n,r){const s=n.ref;return he(typeof s!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),s?N.cloneElement(n,{ref:c=>{bp(s,c),bp(r,c)}}):N.cloneElement(n,{ref:r})}class Iw{receiveHandlerId(r){this.handlerId!==r&&(this.handlerId=r,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(r){this.dragSourceOptionsInternal=r}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(r){this.dragPreviewOptionsInternal=r}reconnect(){const r=this.reconnectDragSource();this.reconnectDragPreview(r)}reconnectDragSource(){const r=this.dragSource,s=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return s&&this.disconnectDragSource(),this.handlerId?r?(s&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=r,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,r,this.dragSourceOptions)),s):(this.lastConnectedDragSource=r,s):s}reconnectDragPreview(r=!1){const s=this.dragPreview,c=r||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(c&&this.disconnectDragPreview(),!!this.handlerId){if(!s){this.lastConnectedDragPreview=s;return}c&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=s,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,s,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!ou(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!ou(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(r){this.hooks=Lg({dragSource:(s,c)=>{this.clearDragSource(),this.dragSourceOptions=c||null,uu(s)?this.dragSourceRef=s:this.dragSourceNode=s,this.reconnectDragSource()},dragPreview:(s,c)=>{this.clearDragPreview(),this.dragPreviewOptions=c||null,uu(s)?this.dragPreviewRef=s:this.dragPreviewNode=s,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=r}}class Fw{get connectTarget(){return this.dropTarget}reconnect(){const r=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();r&&this.disconnectDropTarget();const s=this.dropTarget;if(this.handlerId){if(!s){this.lastConnectedDropTarget=s;return}r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=s,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,s,this.dropTargetOptions))}}receiveHandlerId(r){r!==this.handlerId&&(this.handlerId=r,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(r){this.dropTargetOptionsInternal=r}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!ou(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(r){this.hooks=Lg({dropTarget:(s,c)=>{this.clearDropTarget(),this.dropTargetOptions=c,uu(s)?this.dropTargetRef=s:this.dropTargetNode=s,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=r}}function il(){const{dragDropManager:n}=N.useContext(Cg);return he(n!=null,"Expected drag drop context"),n}function Jw(n,r){const s=il(),c=N.useMemo(()=>new Iw(s.getBackend()),[s]);return hn(()=>(c.dragSourceOptions=n||null,c.reconnect(),()=>c.disconnectDragSource()),[c,n]),hn(()=>(c.dragPreviewOptions=r||null,c.reconnect(),()=>c.disconnectDragPreview()),[c,r]),c}function Ww(){const n=il();return N.useMemo(()=>new $w(n),[n])}class eS{beginDrag(){const r=this.spec,s=this.monitor;let c=null;return typeof r.item=="object"?c=r.item:typeof r.item=="function"?c=r.item(s):c={},c??null}canDrag(){const r=this.spec,s=this.monitor;return typeof r.canDrag=="boolean"?r.canDrag:typeof r.canDrag=="function"?r.canDrag(s):!0}isDragging(r,s){const c=this.spec,u=this.monitor,{isDragging:f}=c;return f?f(u):s===r.getSourceId()}endDrag(){const r=this.spec,s=this.monitor,c=this.connector,{end:u}=r;u&&u(s.getItem(),s),c.reconnect()}constructor(r,s,c){this.spec=r,this.monitor=s,this.connector=c}}function tS(n,r,s){const c=N.useMemo(()=>new eS(n,r,s),[r,s]);return N.useEffect(()=>{c.spec=n},[n]),c}function aS(n){return N.useMemo(()=>{const r=n.type;return he(r!=null,"spec.type must be defined"),r},[n])}function nS(n,r,s){const c=il(),u=tS(n,r,s),f=aS(n);hn(function(){if(f!=null){const[g,y]=Pw(f,u,c);return r.receiveHandlerId(g),s.receiveHandlerId(g),y}},[c,r,s,u,f])}function Hg(n,r){const s=zg(n);he(!s.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const c=Ww(),u=Jw(s.options,s.previewOptions);return nS(s,c,u),[Ug(s.collect,c,u),Yw(u),Gw(u)]}function lS(n){return N.useMemo(()=>n.hooks.dropTarget(),[n])}function rS(n){const r=il(),s=N.useMemo(()=>new Fw(r.getBackend()),[r]);return hn(()=>(s.dropTargetOptions=n||null,s.reconnect(),()=>s.disconnectDropTarget()),[n]),s}function sS(){const n=il();return N.useMemo(()=>new Vw(n),[n])}function iS(n){const{accept:r}=n;return N.useMemo(()=>(he(n.accept!=null,"accept must be defined"),Array.isArray(r)?r:[r]),[r])}class cS{canDrop(){const r=this.spec,s=this.monitor;return r.canDrop?r.canDrop(s.getItem(),s):!0}hover(){const r=this.spec,s=this.monitor;r.hover&&r.hover(s.getItem(),s)}drop(){const r=this.spec,s=this.monitor;if(r.drop)return r.drop(s.getItem(),s)}constructor(r,s){this.spec=r,this.monitor=s}}function oS(n,r){const s=N.useMemo(()=>new cS(n,r),[r]);return N.useEffect(()=>{s.spec=n},[n]),s}function uS(n,r,s){const c=il(),u=oS(n,r),f=iS(n);hn(function(){const[g,y]=Xw(f,u,c);return r.receiveHandlerId(g),s.receiveHandlerId(g),y},[c,r,u,s,f.map(h=>h.toString()).join("|")])}function dS(n,r){const s=zg(n),c=sS(),u=rS(s.options);return uS(s,c,u),[Ug(s.collect,c,u),lS(u)]}function Bg(n){let r=null;return()=>(r==null&&(r=n()),r)}function fS(n,r){return n.filter(s=>s!==r)}function hS(n,r){const s=new Set,c=f=>s.add(f);n.forEach(c),r.forEach(c);const u=[];return s.forEach(f=>u.push(f)),u}class mS{enter(r){const s=this.entered.length,c=u=>this.isNodeInDocument(u)&&(!u.contains||u.contains(r));return this.entered=hS(this.entered.filter(c),[r]),s===0&&this.entered.length>0}leave(r){const s=this.entered.length;return this.entered=fS(this.entered.filter(this.isNodeInDocument),r),s>0&&this.entered.length===0}reset(){this.entered=[]}constructor(r){this.entered=[],this.isNodeInDocument=r}}class pS{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(r=>{Object.defineProperty(this.item,r,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${r}" until the drop event.`),null}})})}loadDataTransfer(r){if(r){const s={};Object.keys(this.config.exposeProperties).forEach(c=>{const u=this.config.exposeProperties[c];u!=null&&(s[c]={value:u(r,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,s)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(r,s){return s===r.getSourceId()}endDrag(){}constructor(r){this.config=r,this.item={},this.initializeExposedProperties()}}const qg="__NATIVE_FILE__",Yg="__NATIVE_URL__",Gg="__NATIVE_TEXT__",$g="__NATIVE_HTML__",Np=Object.freeze(Object.defineProperty({__proto__:null,FILE:qg,HTML:$g,TEXT:Gg,URL:Yg},Symbol.toStringTag,{value:"Module"}));function Qo(n,r,s){const c=r.reduce((u,f)=>u||n.getData(f),"");return c??s}const du={[qg]:{exposeProperties:{files:n=>Array.prototype.slice.call(n.files),items:n=>n.items,dataTransfer:n=>n},matchesTypes:["Files"]},[$g]:{exposeProperties:{html:(n,r)=>Qo(n,r,""),dataTransfer:n=>n},matchesTypes:["Html","text/html"]},[Yg]:{exposeProperties:{urls:(n,r)=>Qo(n,r,"").split(`
`),dataTransfer:n=>n},matchesTypes:["Url","text/uri-list"]},[Gg]:{exposeProperties:{text:(n,r)=>Qo(n,r,""),dataTransfer:n=>n},matchesTypes:["Text","text/plain"]}};function gS(n,r){const s=du[n];if(!s)throw new Error(`native type ${n} has no configuration`);const c=new pS(s);return c.loadDataTransfer(r),c}function Zo(n){if(!n)return null;const r=Array.prototype.slice.call(n.types||[]);return Object.keys(du).filter(s=>{const c=du[s];return c!=null&&c.matchesTypes?c.matchesTypes.some(u=>r.indexOf(u)>-1):!1})[0]||null}const yS=Bg(()=>/firefox/i.test(navigator.userAgent)),Vg=Bg(()=>!!window.safari);class wp{interpolate(r){const{xs:s,ys:c,c1s:u,c2s:f,c3s:h}=this;let g=s.length-1;if(r===s[g])return c[g];let y=0,p=h.length-1,x;for(;y<=p;){x=Math.floor(.5*(y+p));const R=s[x];if(R<r)y=x+1;else if(R>r)p=x-1;else return c[x]}g=Math.max(0,p);const w=r-s[g],b=w*w;return c[g]+u[g]*w+f[g]*b+h[g]*w*b}constructor(r,s){const{length:c}=r,u=[];for(let R=0;R<c;R++)u.push(R);u.sort((R,T)=>r[R]<r[T]?-1:1);const f=[],h=[];let g,y;for(let R=0;R<c-1;R++)g=r[R+1]-r[R],y=s[R+1]-s[R],f.push(g),h.push(y/g);const p=[h[0]];for(let R=0;R<f.length-1;R++){const T=h[R],C=h[R+1];if(T*C<=0)p.push(0);else{g=f[R];const S=f[R+1],E=g+S;p.push(3*E/((E+S)/T+(E+g)/C))}}p.push(h[h.length-1]);const x=[],w=[];let b;for(let R=0;R<p.length-1;R++){b=h[R];const T=p[R],C=1/f[R],S=T+p[R+1]-b-b;x.push((b-T-S)*C),w.push(S*C*C)}this.xs=r,this.ys=s,this.c1s=p,this.c2s=x,this.c3s=w}}const xS=1;function Xg(n){const r=n.nodeType===xS?n:n.parentElement;if(!r)return null;const{top:s,left:c}=r.getBoundingClientRect();return{x:c,y:s}}function qs(n){return{x:n.clientX,y:n.clientY}}function vS(n){var r;return n.nodeName==="IMG"&&(yS()||!(!((r=document.documentElement)===null||r===void 0)&&r.contains(n)))}function bS(n,r,s,c){let u=n?r.width:s,f=n?r.height:c;return Vg()&&n&&(f/=window.devicePixelRatio,u/=window.devicePixelRatio),{dragPreviewWidth:u,dragPreviewHeight:f}}function NS(n,r,s,c,u){const f=vS(r),g=Xg(f?n:r),y={x:s.x-g.x,y:s.y-g.y},{offsetWidth:p,offsetHeight:x}=n,{anchorX:w,anchorY:b}=c,{dragPreviewWidth:R,dragPreviewHeight:T}=bS(f,r,p,x),C=()=>{let Z=new wp([0,.5,1],[y.y,y.y/x*T,y.y+T-x]).interpolate(b);return Vg()&&f&&(Z+=(window.devicePixelRatio-1)*T),Z},S=()=>new wp([0,.5,1],[y.x,y.x/p*R,y.x+R-p]).interpolate(w),{offsetX:E,offsetY:k}=u,$=E===0||E,z=k===0||k;return{x:$?E:S(),y:z?k:C()}}class wS{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var r;return!((r=this.globalContext)===null||r===void 0)&&r.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var r;return((r=this.optionsArgs)===null||r===void 0?void 0:r.rootElement)||this.window}constructor(r,s){this.ownerDocument=null,this.globalContext=r,this.optionsArgs=s}}function SS(n,r,s){return r in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s,n}function Sp(n){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{},c=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(c=c.concat(Object.getOwnPropertySymbols(s).filter(function(u){return Object.getOwnPropertyDescriptor(s,u).enumerable}))),c.forEach(function(u){SS(n,u,s[u])})}return n}class jS{profile(){var r,s;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((r=this.dragStartSourceIds)===null||r===void 0?void 0:r.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((s=this.dragOverTargetIds)===null||s===void 0?void 0:s.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const r=this.rootElement;if(r!==void 0){if(r.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");r.__isReactDndBackendSetUp=!0,this.addEventListeners(r)}}teardown(){const r=this.rootElement;if(r!==void 0&&(r.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var s;(s=this.window)===null||s===void 0||s.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(r,s,c){return this.sourcePreviewNodeOptions.set(r,c),this.sourcePreviewNodes.set(r,s),()=>{this.sourcePreviewNodes.delete(r),this.sourcePreviewNodeOptions.delete(r)}}connectDragSource(r,s,c){this.sourceNodes.set(r,s),this.sourceNodeOptions.set(r,c);const u=h=>this.handleDragStart(h,r),f=h=>this.handleSelectStart(h);return s.setAttribute("draggable","true"),s.addEventListener("dragstart",u),s.addEventListener("selectstart",f),()=>{this.sourceNodes.delete(r),this.sourceNodeOptions.delete(r),s.removeEventListener("dragstart",u),s.removeEventListener("selectstart",f),s.setAttribute("draggable","false")}}connectDropTarget(r,s){const c=h=>this.handleDragEnter(h,r),u=h=>this.handleDragOver(h,r),f=h=>this.handleDrop(h,r);return s.addEventListener("dragenter",c),s.addEventListener("dragover",u),s.addEventListener("drop",f),()=>{s.removeEventListener("dragenter",c),s.removeEventListener("dragover",u),s.removeEventListener("drop",f)}}addEventListeners(r){r.addEventListener&&(r.addEventListener("dragstart",this.handleTopDragStart),r.addEventListener("dragstart",this.handleTopDragStartCapture,!0),r.addEventListener("dragend",this.handleTopDragEndCapture,!0),r.addEventListener("dragenter",this.handleTopDragEnter),r.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),r.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),r.addEventListener("dragover",this.handleTopDragOver),r.addEventListener("dragover",this.handleTopDragOverCapture,!0),r.addEventListener("drop",this.handleTopDrop),r.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(r){r.removeEventListener&&(r.removeEventListener("dragstart",this.handleTopDragStart),r.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),r.removeEventListener("dragend",this.handleTopDragEndCapture,!0),r.removeEventListener("dragenter",this.handleTopDragEnter),r.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),r.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),r.removeEventListener("dragover",this.handleTopDragOver),r.removeEventListener("dragover",this.handleTopDragOverCapture,!0),r.removeEventListener("drop",this.handleTopDrop),r.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const r=this.monitor.getSourceId(),s=this.sourceNodeOptions.get(r);return Sp({dropEffect:this.altKeyPressed?"copy":"move"},s||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const r=this.monitor.getSourceId(),s=this.sourcePreviewNodeOptions.get(r);return Sp({anchorX:.5,anchorY:.5,captureDraggingState:!1},s||{})}isDraggingNativeItem(){const r=this.monitor.getItemType();return Object.keys(Np).some(s=>Np[s]===r)}beginDragNativeItem(r,s){this.clearCurrentDragSourceNode(),this.currentNativeSource=gS(r,s),this.currentNativeHandle=this.registry.addSource(r,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(r){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=r;const s=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var c;return(c=this.rootElement)===null||c===void 0?void 0:c.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},s)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var r;(r=this.window)===null||r===void 0||r.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(r,s){r.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(s))}handleDragEnter(r,s){this.dragEnterTargetIds.unshift(s)}handleDragOver(r,s){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(s)}handleDrop(r,s){this.dropTargetIds.unshift(s)}constructor(r,s,c){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=u=>{const f=this.sourceNodes.get(u);return f&&Xg(f)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=u=>!!(u&&this.document&&this.document.body&&this.document.body.contains(u)),this.endDragIfSourceWasRemovedFromDOM=()=>{const u=this.currentDragSourceNode;u==null||this.isNodeInDocument(u)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=u=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(u||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=u=>{if(u.defaultPrevented)return;const{dragStartSourceIds:f}=this;this.dragStartSourceIds=null;const h=qs(u);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(f||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:h});const{dataTransfer:g}=u,y=Zo(g);if(this.monitor.isDragging()){if(g&&typeof g.setDragImage=="function"){const x=this.monitor.getSourceId(),w=this.sourceNodes.get(x),b=this.sourcePreviewNodes.get(x)||w;if(b){const{anchorX:R,anchorY:T,offsetX:C,offsetY:S}=this.getCurrentSourcePreviewNodeOptions(),$=NS(w,b,h,{anchorX:R,anchorY:T},{offsetX:C,offsetY:S});g.setDragImage(b,$.x,$.y)}}try{g==null||g.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(u.target);const{captureDraggingState:p}=this.getCurrentSourcePreviewNodeOptions();p?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(y)this.beginDragNativeItem(y);else{if(g&&!g.types&&(u.target&&!u.target.hasAttribute||!u.target.hasAttribute("draggable")))return;u.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=u=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var f;(f=this.currentNativeSource)===null||f===void 0||f.loadDataTransfer(u.dataTransfer)}if(!this.enterLeaveCounter.enter(u.target)||this.monitor.isDragging())return;const{dataTransfer:g}=u,y=Zo(g);y&&this.beginDragNativeItem(y,g)},this.handleTopDragEnter=u=>{const{dragEnterTargetIds:f}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=u.altKey,f.length>0&&this.actions.hover(f,{clientOffset:qs(u)}),f.some(g=>this.monitor.canDropOnTarget(g))&&(u.preventDefault(),u.dataTransfer&&(u.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=u=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var f;(f=this.currentNativeSource)===null||f===void 0||f.loadDataTransfer(u.dataTransfer)}},this.handleTopDragOver=u=>{const{dragOverTargetIds:f}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){u.preventDefault(),u.dataTransfer&&(u.dataTransfer.dropEffect="none");return}this.altKeyPressed=u.altKey,this.lastClientOffset=qs(u),this.scheduleHover(f),(f||[]).some(g=>this.monitor.canDropOnTarget(g))?(u.preventDefault(),u.dataTransfer&&(u.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?u.preventDefault():(u.preventDefault(),u.dataTransfer&&(u.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=u=>{this.isDraggingNativeItem()&&u.preventDefault(),this.enterLeaveCounter.leave(u.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=u=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var f;u.preventDefault(),(f=this.currentNativeSource)===null||f===void 0||f.loadDataTransfer(u.dataTransfer)}else Zo(u.dataTransfer)&&u.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=u=>{const{dropTargetIds:f}=this;this.dropTargetIds=[],this.actions.hover(f,{clientOffset:qs(u)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=u=>{const f=u.target;typeof f.dragDrop=="function"&&(f.tagName==="INPUT"||f.tagName==="SELECT"||f.tagName==="TEXTAREA"||f.isContentEditable||(u.preventDefault(),f.dragDrop()))},this.options=new wS(s,c),this.actions=r.getActions(),this.monitor=r.getMonitor(),this.registry=r.getRegistry(),this.enterLeaveCounter=new mS(this.isNodeInDocument)}}const ES=function(r,s,c){return new jS(r,s,c)},Pg=({component:n,isSelected:r=!1,onUpdate:s})=>(()=>{var u,f,h,g,y,p,x,w,b,R,T,C,S,E,k,$,z,Y,Z;switch(n.type){case"text":return i.jsx("div",{style:n.styles,className:`${r?"outline-none":""}`,children:((u=n.content)==null?void 0:u.text)||"Your text here..."});case"heading":const se=((f=n.content)==null?void 0:f.level)||"h2";return i.jsx(se,{style:n.styles,className:`${r?"outline-none":""}`,children:((h=n.content)==null?void 0:h.text)||"Your Heading"});case"image":return i.jsx("img",{src:((g=n.content)==null?void 0:g.src)||"https://via.placeholder.com/300x200",alt:((y=n.content)==null?void 0:y.alt)||"Image",style:n.styles,className:`${r?"outline-none":""}`});case"button":return i.jsx("a",{href:((p=n.content)==null?void 0:p.href)||"#",style:n.styles,className:`${r?"outline-none":""}`,children:((x=n.content)==null?void 0:x.text)||"Click Here"});case"divider":return i.jsx("hr",{style:n.styles,className:`border-0 ${r?"outline-none":""}`});case"spacer":return i.jsx("div",{style:n.styles,className:`${r?"outline-none border border-dashed border-gray-300":""}`,children:r&&i.jsx("div",{className:"text-xs text-gray-400 text-center py-2",children:"Spacer"})});case"container":return i.jsx("div",{style:n.styles,className:`${r?"outline-none":""}`,children:((w=n.children)==null?void 0:w.map(I=>i.jsx(Pg,{component:I,onUpdate:s},I.id)))||i.jsx("div",{className:"text-gray-400 text-center py-4",children:"Drop components here"})});case"header":return i.jsxs("header",{style:n.styles,className:`${r?"outline-none":""}`,children:[((b=n.content)==null?void 0:b.logo)&&i.jsx("img",{src:n.content.logo,alt:"Logo",style:{maxHeight:"40px",marginBottom:"10px"}}),i.jsx("h1",{style:{margin:0,fontSize:"24px"},children:((R=n.content)==null?void 0:R.title)||"Your Company"})]});case"footer":return i.jsxs("footer",{style:n.styles,className:`${r?"outline-none":""}`,children:[i.jsx("p",{style:{margin:0},children:((T=n.content)==null?void 0:T.text)||"© 2024 Your Company. All rights reserved."}),((C=n.content)==null?void 0:C.links)&&n.content.links.length>0&&i.jsx("div",{style:{marginTop:"10px"},children:n.content.links.map((I,oe)=>i.jsx("a",{href:I.href,style:{marginRight:"15px",color:"inherit"},children:I.text},oe))})]});case"product-card":return i.jsxs("div",{style:n.styles,className:`${r?"outline-none":""}`,children:[((S=n.content)==null?void 0:S.image)&&i.jsx("img",{src:n.content.image,alt:((E=n.content)==null?void 0:E.title)||"Product",style:{width:"100%",marginBottom:"10px"}}),i.jsx("h3",{style:{margin:"0 0 8px 0",fontSize:"18px"},children:((k=n.content)==null?void 0:k.title)||"Product Name"}),i.jsx("p",{style:{margin:"0 0 10px 0",color:"#666"},children:(($=n.content)==null?void 0:$.description)||"Product description here..."}),i.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[i.jsx("span",{style:{fontSize:"20px",fontWeight:"bold"},children:((z=n.content)==null?void 0:z.price)||"$99.99"}),i.jsx("a",{href:"#",style:{backgroundColor:"#3b82f6",color:"white",padding:"8px 16px",textDecoration:"none",borderRadius:"4px"},children:((Y=n.content)==null?void 0:Y.buttonText)||"Buy Now"})]})]});case"social-media":const Q=((Z=n.content)==null?void 0:Z.platforms)||["facebook","twitter","instagram"];return i.jsx("div",{style:n.styles,className:`${r?"outline-none":""}`,children:i.jsx("div",{style:{display:"flex",justifyContent:"center",gap:"15px"},children:Q.map(I=>i.jsx("a",{href:"#",style:{display:"inline-block",width:"32px",height:"32px",backgroundColor:TS(I),borderRadius:"50%",textAlign:"center",lineHeight:"32px",color:"white",textDecoration:"none"},children:CS(I)},I))})});default:return i.jsxs("div",{style:n.styles,className:`p-4 border border-dashed border-gray-300 ${r?"outline-none":""}`,children:["Unknown component type: ",n.type]})}})(),TS=n=>({facebook:"#1877f2",twitter:"#1da1f2",instagram:"#e4405f",linkedin:"#0077b5",youtube:"#ff0000",pinterest:"#bd081c"})[n]||"#666666",CS=n=>({facebook:"f",twitter:"t",instagram:"i",linkedin:"in",youtube:"y",pinterest:"p"})[n]||"?",DS=({component:n,isSelected:r,onSelect:s,onUpdate:c,onDelete:u,zoom:f})=>{var C;const h=N.useRef(null),[,g]=N.useState(!1),[,y]=N.useState(null),[{isDragging:p},x]=Hg({type:"existing-component",item:{type:"existing-component",id:n.id,styles:n.styles},collect:S=>({isDragging:S.isDragging()})}),w=S=>{S.stopPropagation(),s()},b=S=>{if(S.stopPropagation(),n.type==="text"||n.type==="heading"){const E=S.target;if(E.contentEditable!=="true"){E.contentEditable="true",E.focus();const k=()=>{E.contentEditable="false",c({content:{...n.content,text:E.textContent||""}}),E.removeEventListener("blur",k)};E.addEventListener("blur",k)}}},R=(S,E)=>{var Q,I;S.stopPropagation(),g(!0),y(E);const k=S.clientX,$=S.clientY,z=parseInt(((Q=n.styles)==null?void 0:Q.width)||"100"),Y=parseInt(((I=n.styles)==null?void 0:I.height)||"100"),Z=oe=>{const ve=oe.clientX-k,Re=oe.clientY-$;let te=z,J=Y;E.includes("right")&&(te=Math.max(20,z+ve)),E.includes("left")&&(te=Math.max(20,z-ve)),E.includes("bottom")&&(J=Math.max(20,Y+Re)),E.includes("top")&&(J=Math.max(20,Y-Re)),c({styles:{...n.styles,width:`${te}px`,height:`${J}px`}})},se=()=>{g(!1),y(null),document.removeEventListener("mousemove",Z),document.removeEventListener("mouseup",se)};document.addEventListener("mousemove",Z),document.addEventListener("mouseup",se)},T=S=>{S.stopPropagation(),confirm("Are you sure you want to delete this component?")&&(u==null||u(n.id))};return x(h),i.jsxs("div",{ref:h,className:`absolute cursor-pointer transition-all duration-200 ${r?"ring-2 ring-blue-500 ring-opacity-50":""} ${p?"opacity-50":""}`,style:{left:n.position.x,top:n.position.y,zIndex:((C=n.styles)==null?void 0:C.zIndex)||1,transform:`scale(${f})`,transformOrigin:"top left"},onMouseDown:w,onDoubleClick:b,children:[i.jsx(Pg,{component:n,isSelected:r,onUpdate:c}),r&&i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"absolute inset-0 border-2 border-blue-500 border-dashed pointer-events-none"}),i.jsxs("div",{className:"absolute inset-0",children:[i.jsx("div",{className:"absolute -top-1 -left-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-nw-resize pointer-events-auto",onMouseDown:S=>R(S,"top-left")}),i.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-ne-resize pointer-events-auto",onMouseDown:S=>R(S,"top-right")}),i.jsx("div",{className:"absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-sw-resize pointer-events-auto",onMouseDown:S=>R(S,"bottom-left")}),i.jsx("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-se-resize pointer-events-auto",onMouseDown:S=>R(S,"bottom-right")}),i.jsx("div",{className:"absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-n-resize pointer-events-auto",onMouseDown:S=>R(S,"top")}),i.jsx("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-s-resize pointer-events-auto",onMouseDown:S=>R(S,"bottom")}),i.jsx("div",{className:"absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-w-resize pointer-events-auto",onMouseDown:S=>R(S,"left")}),i.jsx("div",{className:"absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-e-resize pointer-events-auto",onMouseDown:S=>R(S,"right")})]}),i.jsxs("div",{className:"absolute -top-8 left-0 flex space-x-1 pointer-events-auto",children:[i.jsx("button",{className:"p-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600",title:"Move",children:i.jsx(TN,{className:"w-3 h-3"})}),i.jsx("button",{onClick:T,className:"p-1 bg-red-500 text-white rounded text-xs hover:bg-red-600",title:"Delete",children:i.jsx(Sr,{className:"w-3 h-3"})})]})]})]})},OS=({component:n,zoom:r})=>{var c,u;const s={position:"absolute",left:n.position.x,top:n.position.y,width:((c=n.styles)==null?void 0:c.width)||"auto",height:((u=n.styles)==null?void 0:u.height)||"auto",border:"2px solid #3b82f6",borderRadius:"4px",pointerEvents:"none",zIndex:9999,transform:`scale(${r})`,transformOrigin:"top left"};return i.jsxs("div",{style:s,children:[i.jsx("div",{className:"absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"}),i.jsx("div",{className:"absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"})]})},RS=({size:n=20,color:r="#e5e7eb",opacity:s=.5})=>{const c={position:"absolute",top:0,left:0,right:0,bottom:0,pointerEvents:"none",backgroundImage:`
      linear-gradient(to right, ${r} 1px, transparent 1px),
      linear-gradient(to bottom, ${r} 1px, transparent 1px)
    `,backgroundSize:`${n}px ${n}px`,opacity:s,zIndex:1};return i.jsx("div",{style:c})},_S=({zoom:n,onZoomChange:r})=>{const s=[.25,.5,.75,1,1.25,1.5,2],c=()=>{const h=s.findIndex(y=>y>=n),g=Math.min(h+1,s.length-1);r(s[g])},u=()=>{const h=s.findIndex(y=>y>=n),g=Math.max(h-1,0);r(s[g])},f=()=>{r(1)};return i.jsxs("div",{className:"absolute top-4 right-4 flex items-center space-x-2 bg-white rounded-lg shadow-md border border-gray-200 p-2 z-10",children:[i.jsx("button",{onClick:u,disabled:n<=s[0],className:"p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",title:"Zoom Out",children:i.jsx(d2,{className:"w-4 h-4"})}),i.jsx("select",{value:n,onChange:h=>r(parseFloat(h.target.value)),className:"px-2 py-1 text-sm border border-gray-300 rounded",children:s.map(h=>i.jsxs("option",{value:h,children:[Math.round(h*100),"%"]},h))}),i.jsx("button",{onClick:c,disabled:n>=s[s.length-1],className:"p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",title:"Zoom In",children:i.jsx(o2,{className:"w-4 h-4"})}),i.jsx("div",{className:"w-px h-4 bg-gray-300"}),i.jsx("button",{onClick:f,className:"p-1 rounded hover:bg-gray-100",title:"Reset Zoom",children:i.jsx(Eg,{className:"w-4 h-4"})})]})},AS=({components:n,selectedComponent:r,onComponentSelect:s,onComponentUpdate:c,onComponentAdd:u,onComponentDelete:f,zoom:h,onZoomChange:g,showGrid:y,previewMode:p})=>{const x=N.useRef(null),[{isOver:w},b]=dS({accept:["component","existing-component"],drop:(E,k)=>{if(!x.current)return;const $=x.current.getBoundingClientRect(),z=k.getClientOffset();if(z){const Y=(z.x-$.left)/h,Z=(z.y-$.top)/h;if(E.type==="component"){const se={id:`component-${Date.now()}`,type:E.componentType,content:E.defaultContent||{},styles:{...E.defaultStyles,position:"absolute",left:Y,top:Z,zIndex:n.length+1},position:{x:Y,y:Z}};u(se)}else E.type==="existing-component"&&c(E.id,{position:{x:Y,y:Z},styles:{...E.styles,left:Y,top:Z}})}},collect:E=>({isOver:E.isOver()})}),R=N.useCallback(E=>{E.target===x.current&&s(null)},[s]),T=N.useCallback(E=>{if(r)switch(E.key){case"Delete":case"Backspace":f(r);break;case"ArrowUp":E.preventDefault(),C(0,-1);break;case"ArrowDown":E.preventDefault(),C(0,1);break;case"ArrowLeft":E.preventDefault(),C(-1,0);break;case"ArrowRight":E.preventDefault(),C(1,0);break}},[r]),C=(E,k)=>{var Z,se;if(!r)return;const $=n.find(Q=>Q.id===r);if(!$)return;const z=(((Z=$.position)==null?void 0:Z.x)||0)+E,Y=(((se=$.position)==null?void 0:se.y)||0)+k;c(r,{position:{x:z,y:Y},styles:{...$.styles,left:z,top:Y}})};Op.useEffect(()=>(document.addEventListener("keydown",T),()=>document.removeEventListener("keydown",T)),[T]);const S=()=>{switch(p){case"mobile":return 375;case"tablet":return 768;case"desktop":return 600;default:return 600}};return b(x),i.jsxs("div",{className:"relative flex-1 overflow-auto bg-gray-100 p-8",children:[i.jsx(_S,{zoom:h,onZoomChange:g}),i.jsx("div",{className:"relative mx-auto bg-white shadow-lg",style:{width:S(),minHeight:800,transform:`scale(${h})`,transformOrigin:"top center"},children:i.jsxs("div",{ref:x,className:`relative w-full h-full ${w?"bg-blue-50":""}`,onClick:R,style:{minHeight:800},children:[y&&i.jsx(RS,{}),n.map(E=>i.jsx(DS,{component:E,isSelected:r===E.id,onSelect:()=>s(E.id),onUpdate:k=>c(E.id,k),onDelete:f,zoom:h},E.id)),r&&i.jsx(OS,{component:n.find(E=>E.id===r),zoom:h})]})})]})},MS=({component:n,onAdd:r})=>{const[{isDragging:s},c]=Hg({type:"component",item:{type:"component",componentType:n.componentType,defaultContent:n.defaultContent,defaultStyles:n.defaultStyles},collect:u=>({isDragging:u.isDragging()})});return i.jsxs("div",{ref:c,className:`p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${s?"opacity-50":""}`,onClick:r,children:[i.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[n.icon,i.jsx("span",{className:"text-sm font-medium text-gray-700",children:n.name})]}),i.jsx("div",{className:"text-xs text-gray-500 bg-gray-100 p-2 rounded",dangerouslySetInnerHTML:{__html:n.preview}})]})},kS=({onComponentAdd:n})=>{const[r,s]=N.useState(["basic","layout"]),c=h=>{s(g=>g.includes(h)?g.filter(y=>y!==h):[...g,h])},u=[{id:"basic",name:"Basic Elements",icon:i.jsx(mr,{className:"w-4 h-4"}),components:[{id:"text",name:"Text Block",icon:i.jsx(mr,{className:"w-4 h-4 text-blue-600"}),componentType:"text",defaultContent:{text:"Your text here..."},defaultStyles:{fontSize:"16px",color:"#374151",fontFamily:"Arial, sans-serif",lineHeight:"1.5",padding:"10px"},preview:'<p style="font-size: 14px; margin: 0;">Your text here...</p>'},{id:"heading",name:"Heading",icon:i.jsx(mr,{className:"w-4 h-4 text-purple-600"}),componentType:"heading",defaultContent:{text:"Your Heading",level:"h2"},defaultStyles:{fontSize:"24px",color:"#1f2937",fontFamily:"Arial, sans-serif",fontWeight:"bold",padding:"10px"},preview:'<h3 style="font-size: 16px; margin: 0; font-weight: bold;">Your Heading</h3>'},{id:"image",name:"Image",icon:i.jsx(Tu,{className:"w-4 h-4 text-green-600"}),componentType:"image",defaultContent:{src:"https://via.placeholder.com/300x200",alt:"Image description"},defaultStyles:{width:"300px",height:"200px",borderRadius:"4px"},preview:'<div style="width: 60px; height: 40px; background: #e5e7eb; border-radius: 2px;"></div>'},{id:"button",name:"Button",icon:i.jsx(Sg,{className:"w-4 h-4 text-red-600"}),componentType:"button",defaultContent:{text:"Click Here",href:"#"},defaultStyles:{backgroundColor:"#3b82f6",color:"#ffffff",padding:"12px 24px",borderRadius:"6px",textDecoration:"none",display:"inline-block",fontWeight:"500"},preview:'<div style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 3px; font-size: 12px; text-align: center;">Click Here</div>'}]},{id:"layout",name:"Layout Elements",icon:i.jsx(fr,{className:"w-4 h-4"}),components:[{id:"divider",name:"Divider",icon:i.jsx(wg,{className:"w-4 h-4 text-gray-600"}),componentType:"divider",defaultContent:{},defaultStyles:{height:"1px",backgroundColor:"#e5e7eb",margin:"20px 0",width:"100%"},preview:'<div style="height: 1px; background: #e5e7eb; width: 100%;"></div>'},{id:"spacer",name:"Spacer",icon:i.jsx(su,{className:"w-4 h-4 text-gray-600"}),componentType:"spacer",defaultContent:{},defaultStyles:{height:"40px",width:"100%"},preview:'<div style="height: 20px; background: transparent; border: 1px dashed #d1d5db;"></div>'},{id:"container",name:"Container",icon:i.jsx(su,{className:"w-4 h-4 text-indigo-600"}),componentType:"container",defaultContent:{},defaultStyles:{padding:"20px",backgroundColor:"#f9fafb",borderRadius:"8px",border:"1px solid #e5e7eb"},preview:'<div style="background: #f9fafb; border: 1px solid #e5e7eb; padding: 8px; border-radius: 4px; font-size: 10px;">Container</div>'}]},{id:"content",name:"Content Blocks",icon:i.jsx(ha,{className:"w-4 h-4"}),components:[{id:"header",name:"Header",icon:i.jsx(fr,{className:"w-4 h-4 text-blue-600"}),componentType:"header",defaultContent:{logo:"https://via.placeholder.com/120x40",title:"Your Company"},defaultStyles:{backgroundColor:"#1f2937",color:"#ffffff",padding:"20px",textAlign:"center"},preview:'<div style="background: #1f2937; color: white; padding: 8px; text-align: center; font-size: 10px;">Header</div>'},{id:"footer",name:"Footer",icon:i.jsx(fr,{className:"w-4 h-4 text-gray-600"}),componentType:"footer",defaultContent:{text:"© 2024 Your Company. All rights reserved.",links:[]},defaultStyles:{backgroundColor:"#f3f4f6",color:"#6b7280",padding:"20px",textAlign:"center",fontSize:"14px"},preview:'<div style="background: #f3f4f6; color: #6b7280; padding: 8px; text-align: center; font-size: 10px;">Footer</div>'},{id:"product-card",name:"Product Card",icon:i.jsx(PN,{className:"w-4 h-4 text-green-600"}),componentType:"product-card",defaultContent:{image:"https://via.placeholder.com/200x200",title:"Product Name",description:"Product description here...",price:"$99.99",buttonText:"Buy Now"},defaultStyles:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"16px",backgroundColor:"#ffffff"},preview:'<div style="border: 1px solid #e5e7eb; padding: 6px; border-radius: 4px; font-size: 10px;">Product Card</div>'},{id:"social-media",name:"Social Media",icon:i.jsx(fn,{className:"w-4 h-4 text-blue-500"}),componentType:"social-media",defaultContent:{platforms:["facebook","twitter","instagram","linkedin"]},defaultStyles:{textAlign:"center",padding:"20px"},preview:'<div style="text-align: center; font-size: 10px;">🔗 Social Links</div>'}]}],f=h=>{const g={id:`component-${Date.now()}`,type:h.componentType,content:h.defaultContent,styles:{...h.defaultStyles,position:"relative",zIndex:1},position:{x:0,y:0}};n(g)};return i.jsxs("div",{className:"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Components"}),i.jsx("div",{className:"space-y-4",children:u.map(h=>i.jsxs("div",{className:"border border-gray-200 rounded-lg",children:[i.jsxs("button",{onClick:()=>c(h.id),className:"w-full flex items-center justify-between p-3 text-left hover:bg-gray-50",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[h.icon,i.jsx("span",{className:"font-medium text-gray-700",children:h.name})]}),r.includes(h.id)?i.jsx(Eu,{className:"w-4 h-4 text-gray-500"}):i.jsx(bg,{className:"w-4 h-4 text-gray-500"})]}),r.includes(h.id)&&i.jsx("div",{className:"p-3 pt-0 space-y-3",children:h.components.map(g=>i.jsx(MS,{component:g,onAdd:()=>f(g)},g.id))})]},h.id))})]})},US=["#000000","#ffffff","#f3f4f6","#e5e7eb","#d1d5db","#9ca3af","#6b7280","#374151","#1f2937","#111827","#fef2f2","#fee2e2","#fecaca","#f87171","#ef4444","#dc2626","#b91c1c","#991b1b","#7f1d1d","#fef3c7","#fde68a","#fcd34d","#f59e0b","#d97706","#b45309","#92400e","#78350f","#ecfdf5","#d1fae5","#a7f3d0","#6ee7b7","#34d399","#10b981","#059669","#047857","#065f46","#eff6ff","#dbeafe","#bfdbfe","#93c5fd","#60a5fa","#3b82f6","#2563eb","#1d4ed8","#1e40af","#f3e8ff","#e9d5ff","#d8b4fe","#c084fc","#a855f7","#9333ea","#7c3aed","#6d28d9","#5b21b6"],jp=({label:n,value:r,onChange:s})=>{const[c,u]=N.useState(!1),[f,h]=N.useState(r),g=N.useRef(null);N.useEffect(()=>{const w=b=>{g.current&&!g.current.contains(b.target)&&u(!1)};return document.addEventListener("mousedown",w),()=>document.removeEventListener("mousedown",w)},[]);const y=w=>{s(w),h(w),u(!1)},p=w=>{const b=w.target.value;h(b),s(b)},x=r==="transparent"?"transparent":r;return i.jsxs("div",{className:"relative",ref:g,children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:n}),i.jsxs("button",{type:"button",onClick:()=>u(!c),className:"w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-5 h-5 rounded border border-gray-300",style:{backgroundColor:r==="transparent"?"transparent":r,backgroundImage:r==="transparent"?"linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)":"none",backgroundSize:r==="transparent"?"8px 8px":"auto",backgroundPosition:r==="transparent"?"0 0, 0 4px, 4px -4px, -4px 0px":"auto"}}),i.jsx("span",{className:"text-gray-700",children:x})]}),i.jsx(jg,{className:"w-4 h-4 text-gray-400"})]}),c&&i.jsxs("div",{className:"absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-3",children:[i.jsxs("div",{className:"mb-3",children:[i.jsx("div",{className:"text-xs font-medium text-gray-700 mb-2",children:"Preset Colors"}),i.jsxs("div",{className:"grid grid-cols-8 gap-1",children:[i.jsx("button",{onClick:()=>y("transparent"),className:"w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform",style:{backgroundImage:"linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)",backgroundSize:"8px 8px",backgroundPosition:"0 0, 0 4px, 4px -4px, -4px 0px"},title:"Transparent"}),US.map(w=>i.jsx("button",{onClick:()=>y(w),className:"w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform",style:{backgroundColor:w},title:w},w))]})]}),i.jsxs("div",{children:[i.jsx("div",{className:"text-xs font-medium text-gray-700 mb-2",children:"Custom Color"}),i.jsxs("div",{className:"flex space-x-2",children:[i.jsx("input",{type:"color",value:f==="transparent"?"#ffffff":f,onChange:p,className:"w-8 h-8 border border-gray-300 rounded cursor-pointer"}),i.jsx("input",{type:"text",value:f,onChange:w=>{h(w.target.value),s(w.target.value)},className:"flex-1 px-2 py-1 border border-gray-300 rounded text-sm",placeholder:"#000000"})]})]}),i.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200",children:i.jsxs("div",{className:"flex space-x-2",children:[i.jsx("button",{onClick:()=>y("transparent"),className:"flex-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Transparent"}),i.jsx("button",{onClick:()=>y("#ffffff"),className:"flex-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"White"}),i.jsx("button",{onClick:()=>y("#000000"),className:"flex-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:"Black"})]})})]})]})},Ko=[{family:"Arial, sans-serif",name:"Arial",category:"Sans Serif"},{family:"Helvetica, Arial, sans-serif",name:"Helvetica",category:"Sans Serif"},{family:'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',name:"Segoe UI",category:"Sans Serif"},{family:"Verdana, Geneva, Tahoma, sans-serif",name:"Verdana",category:"Sans Serif"},{family:"Tahoma, Verdana, Geneva, sans-serif",name:"Tahoma",category:"Sans Serif"},{family:'"Trebuchet MS", Helvetica, sans-serif',name:"Trebuchet MS",category:"Sans Serif"},{family:'"Lucida Sans Unicode", "Lucida Grande", sans-serif',name:"Lucida Sans",category:"Sans Serif"},{family:'Georgia, "Times New Roman", Times, serif',name:"Georgia",category:"Serif"},{family:'"Times New Roman", Times, serif',name:"Times New Roman",category:"Serif"},{family:'"Palatino Linotype", "Book Antiqua", Palatino, serif',name:"Palatino",category:"Serif"},{family:'"Book Antiqua", Palatino, "Palatino Linotype", serif',name:"Book Antiqua",category:"Serif"},{family:'"Courier New", Courier, monospace',name:"Courier New",category:"Monospace"},{family:'"Lucida Console", Monaco, monospace',name:"Lucida Console",category:"Monospace"},{family:"Impact, Charcoal, sans-serif",name:"Impact",category:"Display"},{family:'"Arial Black", Gadget, sans-serif',name:"Arial Black",category:"Display"},{family:"Comic Sans MS, cursive",name:"Comic Sans MS",category:"Display"}],zS=({value:n,onChange:r})=>{const[s,c]=N.useState(!1),[u,f]=N.useState(""),h=Ko.find(x=>x.family===n)||Ko[0],g=Ko.filter(x=>x.name.toLowerCase().includes(u.toLowerCase())||x.category.toLowerCase().includes(u.toLowerCase())),y=g.reduce((x,w)=>{const b=w.category;return x[b]||(x[b]=[]),x[b].push(w),x},{}),p=x=>{r(x),c(!1),f("")};return i.jsxs("div",{className:"relative",children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Font Family"}),i.jsxs("button",{type:"button",onClick:()=>c(!s),className:"w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50",children:[i.jsx("span",{style:{fontFamily:h.family},children:h.name}),i.jsx(Eu,{className:"w-4 h-4 text-gray-400"})]}),s&&i.jsxs("div",{className:"absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden",children:[i.jsx("div",{className:"p-3 border-b border-gray-200",children:i.jsx("input",{type:"text",placeholder:"Search fonts...",value:u,onChange:x=>f(x.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",autoFocus:!0})}),i.jsx("div",{className:"max-h-64 overflow-y-auto",children:Object.entries(y).map(([x,w])=>i.jsxs("div",{children:[i.jsx("div",{className:"px-3 py-2 bg-gray-50 text-xs font-medium text-gray-700 uppercase tracking-wide",children:x}),w.map(b=>i.jsx("button",{onClick:()=>p(b.family),className:`w-full text-left px-3 py-2 hover:bg-gray-50 transition-colors ${b.family===n?"bg-blue-50 text-blue-700":"text-gray-700"}`,style:{fontFamily:b.family},children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{children:b.name}),i.jsx("span",{className:"text-xs text-gray-400",children:"Aa"})]})},b.family))]},x))}),g.length===0&&i.jsxs("div",{className:"p-4 text-center text-gray-500 text-sm",children:['No fonts found matching "',u,'"']})]})]})},Ep=(n="0")=>{const r=n.split(" ").map(s=>s.replace("px",""));switch(r.length){case 1:return{top:r[0],right:r[0],bottom:r[0],left:r[0]};case 2:return{top:r[0],right:r[1],bottom:r[0],left:r[1]};case 3:return{top:r[0],right:r[1],bottom:r[2],left:r[1]};case 4:return{top:r[0],right:r[1],bottom:r[2],left:r[3]};default:return{top:"0",right:"0",bottom:"0",left:"0"}}},Tp=n=>{const{top:r,right:s,bottom:c,left:u}=n;return r===s&&s===c&&c===u?`${r}px`:r===c&&s===u?`${r}px ${s}px`:s===u?`${r}px ${s}px ${c}px`:`${r}px ${s}px ${c}px ${u}px`},LS=({padding:n="0",margin:r="0",onPaddingChange:s,onMarginChange:c})=>{const[u,f]=N.useState(Ep(n)),[h,g]=N.useState(Ep(r)),[y,p]=N.useState(!0),[x,w]=N.useState(!0),b=(C,S)=>{const E={...u};y?(E.top=S,E.right=S,E.bottom=S,E.left=S):E[C]=S,f(E),s(Tp(E))},R=(C,S)=>{const E={...h};x?(E.top=S,E.right=S,E.bottom=S,E.left=S):E[C]=S,g(E),c(Tp(E))},T=({label:C,values:S,linked:E,onLinkedChange:k,onValueChange:$})=>i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("label",{className:"text-sm font-medium text-gray-700",children:C}),i.jsx("button",{type:"button",onClick:()=>k(!E),className:`p-1 rounded ${E?"text-blue-600":"text-gray-400"}`,title:E?"Unlink values":"Link values",children:i.jsx(su,{className:"w-3 h-3"})})]}),i.jsx("div",{className:"relative",children:i.jsxs("div",{className:"relative bg-gray-100 rounded-lg p-4",children:[i.jsx("div",{className:"bg-white border-2 border-dashed border-gray-300 rounded p-4",children:i.jsx("div",{className:"bg-gray-200 rounded h-8 flex items-center justify-center text-xs text-gray-600",children:"Element"})}),i.jsx("input",{type:"number",value:S.top,onChange:z=>$("top",z.target.value),className:"absolute top-1 left-1/2 transform -translate-x-1/2 w-12 h-6 text-xs text-center border border-gray-300 rounded",placeholder:"0"}),i.jsx("input",{type:"number",value:S.right,onChange:z=>$("right",z.target.value),className:"absolute top-1/2 right-1 transform -translate-y-1/2 w-12 h-6 text-xs text-center border border-gray-300 rounded",placeholder:"0"}),i.jsx("input",{type:"number",value:S.bottom,onChange:z=>$("bottom",z.target.value),className:"absolute bottom-1 left-1/2 transform -translate-x-1/2 w-12 h-6 text-xs text-center border border-gray-300 rounded",placeholder:"0"}),i.jsx("input",{type:"number",value:S.left,onChange:z=>$("left",z.target.value),className:"absolute top-1/2 left-1 transform -translate-y-1/2 w-12 h-6 text-xs text-center border border-gray-300 rounded",placeholder:"0"})]})}),i.jsx("div",{className:"flex space-x-1",children:["0","4","8","12","16","20","24"].map(z=>i.jsx("button",{type:"button",onClick:()=>$("top",z),className:"flex-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded",children:z},z))})]});return i.jsxs("div",{className:"space-y-4",children:[i.jsx(T,{label:"Padding",values:u,linked:y,onLinkedChange:p,onValueChange:b}),i.jsx(T,{label:"Margin",values:h,linked:x,onLinkedChange:w,onValueChange:R})]})},Io={uploadImage:async n=>{const r=new FormData;return r.append("image",n),Ve.upload("/uploads/image",r)},getUploads:async()=>Ve.get("/uploads"),deleteUpload:async n=>Ve.delete(`/uploads/${n}`),getImageUrl:n=>`/api/uploads/${n}`},HS=({isOpen:n,onClose:r,onImageSelect:s})=>{const[c,u]=N.useState([]),[f,h]=N.useState(!1),[g,y]=N.useState(!1);N.useEffect(()=>{n&&p()},[n]);const p=async()=>{try{const S=await Io.getUploads();u(S)}catch(S){console.error("Failed to load uploads:",S)}},x=async S=>{if(!(!S||S.length===0)){h(!0);try{const E=Array.from(S).map($=>Io.uploadImage($)),k=await Promise.all(E);u($=>[...k,...$])}catch(E){console.error("Failed to upload images:",E),alert("Failed to upload images. Please try again.")}finally{h(!1)}}},w=S=>{S.preventDefault(),y(!1),x(S.dataTransfer.files)},b=S=>{S.preventDefault(),y(!0)},R=S=>{S.preventDefault(),y(!1)},T=async S=>{if(confirm("Are you sure you want to delete this image?"))try{await Io.deleteUpload(S),u(E=>E.filter(k=>k.id!==S))}catch(E){console.error("Failed to delete upload:",E),alert("Failed to delete image. Please try again.")}},C=S=>{s(S.url),r()};return n?i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:i.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden",children:[i.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[i.jsx("h2",{className:"text-xl font-semibold",children:"Insert Image"}),i.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-600",children:i.jsx(jr,{className:"w-6 h-6"})})]}),i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:`border-2 border-dashed rounded-lg p-8 text-center mb-6 transition-colors ${g?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"}`,onDrop:w,onDragOver:b,onDragLeave:R,children:[i.jsx(Ou,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),i.jsx("p",{className:"text-lg font-medium text-gray-900 mb-2",children:"Drop images here or click to upload"}),i.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Supports JPEG, PNG, GIF, and WebP (max 5MB each)"}),i.jsx("input",{type:"file",multiple:!0,accept:"image/*",onChange:S=>x(S.target.files),className:"hidden",id:"image-upload"}),i.jsx("label",{htmlFor:"image-upload",className:"btn btn-primary cursor-pointer",children:"Choose Files"})]}),f&&i.jsxs("div",{className:"text-center py-4",children:[i.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),i.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Uploading images..."})]}),i.jsx("div",{className:"max-h-96 overflow-y-auto",children:c.length===0?i.jsxs("div",{className:"text-center py-12",children:[i.jsx(Tu,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),i.jsx("p",{className:"text-gray-500",children:"No images uploaded yet"})]}):i.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:c.map(S=>i.jsxs("div",{className:"relative group border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer",onClick:()=>C(S),children:[i.jsx("img",{src:S.url,alt:S.original_name,className:"w-full h-32 object-cover"}),i.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center",children:i.jsx("button",{onClick:E=>{E.stopPropagation(),T(S.id)},className:"opacity-0 group-hover:opacity-100 bg-red-600 text-white p-2 rounded-full hover:bg-red-700 transition-all",children:i.jsx(Sr,{className:"w-4 h-4"})})}),i.jsxs("div",{className:"p-2",children:[i.jsx("p",{className:"text-xs text-gray-600 truncate",children:S.original_name}),i.jsxs("p",{className:"text-xs text-gray-400",children:[(S.size/1024).toFixed(1)," KB"]})]})]},S.id))})})]}),i.jsx("div",{className:"flex justify-end p-6 border-t bg-gray-50",children:i.jsx("button",{onClick:r,className:"btn btn-outline",children:"Cancel"})})]})}):null},BS=({selectedComponent:n,onComponentUpdate:r,previewMode:s,onPreviewModeChange:c})=>{var b,R,T,C,S,E,k,$,z,Y,Z,se,Q,I,oe,ve,Re;const[u,f]=N.useState(["content","typography","layout","appearance"]),[h,g]=N.useState(!1),y=te=>{f(J=>J.includes(te)?J.filter(be=>be!==te):[...J,te])},p=te=>{n&&r({styles:{...n.styles,...te}})},x=te=>{n&&r({content:{...n.content,...te}})},w=({id:te,title:J,icon:be,children:B})=>i.jsxs("div",{className:"border border-gray-200 rounded-lg mb-3",children:[i.jsxs("button",{onClick:()=>y(te),className:"w-full flex items-center justify-between p-3 text-left hover:bg-gray-50",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[be,i.jsx("span",{className:"font-medium text-gray-700",children:J})]}),u.includes(te)?i.jsx(Eu,{className:"w-4 h-4 text-gray-500"}):i.jsx(bg,{className:"w-4 h-4 text-gray-500"})]}),u.includes(te)&&i.jsx("div",{className:"p-3 pt-0 space-y-3",children:B})]});return n?i.jsxs("div",{className:"w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Properties"}),i.jsxs("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preview Mode"}),i.jsxs("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[i.jsxs("button",{onClick:()=>c("desktop"),className:`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${s==="desktop"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Fs,{className:"w-4 h-4 mr-1"}),"Desktop"]}),i.jsxs("button",{onClick:()=>c("tablet"),className:`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${s==="tablet"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Ws,{className:"w-4 h-4 mr-1"}),"Tablet"]}),i.jsxs("button",{onClick:()=>c("mobile"),className:`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${s==="mobile"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Js,{className:"w-4 h-4 mr-1"}),"Mobile"]})]})]}),i.jsxs("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:[i.jsxs("div",{className:"text-sm font-medium text-gray-900 capitalize",children:[n.type," Element"]}),i.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",n.id]})]}),(n.type==="text"||n.type==="heading"||n.type==="button")&&i.jsxs(w,{id:"content",title:"Content",icon:i.jsx(mr,{className:"w-4 h-4"}),children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Text"}),i.jsx("textarea",{value:((b=n.content)==null?void 0:b.text)||"",onChange:te=>x({text:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",rows:3})]}),n.type==="button"&&i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Link URL"}),i.jsx("input",{type:"url",value:((R=n.content)==null?void 0:R.href)||"",onChange:te=>x({href:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",placeholder:"https://example.com"})]})]}),n.type==="image"&&i.jsxs(w,{id:"content",title:"Image",icon:i.jsx(Tu,{className:"w-4 h-4"}),children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Image Source"}),i.jsxs("div",{className:"flex space-x-2",children:[i.jsx("input",{type:"url",value:((T=n.content)==null?void 0:T.src)||"",onChange:te=>x({src:te.target.value}),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm",placeholder:"https://example.com/image.jpg"}),i.jsx("button",{onClick:()=>g(!0),className:"px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm",children:"Upload"})]})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Alt Text"}),i.jsx("input",{type:"text",value:((C=n.content)==null?void 0:C.alt)||"",onChange:te=>x({alt:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",placeholder:"Image description"})]})]}),i.jsxs(w,{id:"typography",title:"Typography",icon:i.jsx(mr,{className:"w-4 h-4"}),children:[i.jsx(zS,{value:((S=n.styles)==null?void 0:S.fontFamily)||"Arial, sans-serif",onChange:te=>p({fontFamily:te})}),i.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Size"}),i.jsx("input",{type:"number",value:parseInt(((E=n.styles)==null?void 0:E.fontSize)||"16"),onChange:te=>p({fontSize:`${te.target.value}px`}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Weight"}),i.jsxs("select",{value:((k=n.styles)==null?void 0:k.fontWeight)||"normal",onChange:te=>p({fontWeight:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",children:[i.jsx("option",{value:"normal",children:"Normal"}),i.jsx("option",{value:"bold",children:"Bold"}),i.jsx("option",{value:"lighter",children:"Light"}),i.jsx("option",{value:"bolder",children:"Bolder"})]})]})]}),i.jsx(jp,{label:"Text Color",value:(($=n.styles)==null?void 0:$.color)||"#000000",onChange:te=>p({color:te})}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Text Align"}),i.jsxs("select",{value:((z=n.styles)==null?void 0:z.textAlign)||"left",onChange:te=>p({textAlign:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",children:[i.jsx("option",{value:"left",children:"Left"}),i.jsx("option",{value:"center",children:"Center"}),i.jsx("option",{value:"right",children:"Right"}),i.jsx("option",{value:"justify",children:"Justify"})]})]})]}),i.jsxs(w,{id:"layout",title:"Layout & Spacing",icon:i.jsx(fr,{className:"w-4 h-4"}),children:[i.jsx(LS,{padding:(Y=n.styles)==null?void 0:Y.padding,margin:(Z=n.styles)==null?void 0:Z.margin,onPaddingChange:te=>p({padding:te}),onMarginChange:te=>p({margin:te})}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Size Controls"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[i.jsxs("button",{onClick:()=>{var J;const te=parseInt(((J=n.styles)==null?void 0:J.width)||"100");p({width:`${Math.max(10,te-10)}px`})},className:"flex items-center justify-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm",children:[i.jsx(wg,{className:"w-4 h-4 mr-1"}),"Smaller"]}),i.jsxs("button",{onClick:()=>{var J;const te=parseInt(((J=n.styles)==null?void 0:J.width)||"100");p({width:`${te+10}px`})},className:"flex items-center justify-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm",children:[i.jsx(tl,{className:"w-4 h-4 mr-1"}),"Larger"]})]}),i.jsxs("button",{onClick:()=>{p({width:n.type==="image"?"300px":"auto",height:n.type==="image"?"200px":"auto"})},className:"w-full flex items-center justify-center px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md text-sm",children:[i.jsx(Eg,{className:"w-4 h-4 mr-1"}),"Reset Size"]})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Width"}),i.jsx("input",{type:"text",value:((se=n.styles)==null?void 0:se.width)||"auto",onChange:te=>p({width:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",placeholder:"auto, 100px, 50%"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Height"}),i.jsx("input",{type:"text",value:((Q=n.styles)==null?void 0:Q.height)||"auto",onChange:te=>p({height:te.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",placeholder:"auto, 100px, 50%"})]})]})]}),i.jsxs(w,{id:"appearance",title:"Appearance",icon:i.jsx(jg,{className:"w-4 h-4"}),children:[i.jsx(jp,{label:"Background Color",value:((I=n.styles)==null?void 0:I.backgroundColor)||"transparent",onChange:te=>p({backgroundColor:te})}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Border Radius"}),i.jsx("input",{type:"number",value:parseInt(((oe=n.styles)==null?void 0:oe.borderRadius)||"0"),onChange:te=>p({borderRadius:`${te.target.value}px`}),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Opacity"}),i.jsx("input",{type:"range",min:"0",max:"1",step:"0.1",value:((ve=n.styles)==null?void 0:ve.opacity)||1,onChange:te=>p({opacity:parseFloat(te.target.value)}),className:"w-full"}),i.jsxs("div",{className:"text-xs text-gray-500 text-center",children:[Math.round((((Re=n.styles)==null?void 0:Re.opacity)||1)*100),"%"]})]})]}),i.jsx(HS,{isOpen:h,onClose:()=>g(!1),onImageSelect:te=>{x({src:te}),g(!1)}})]}):i.jsxs("div",{className:"w-80 bg-white border-l border-gray-200 p-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Properties"}),i.jsxs("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preview Mode"}),i.jsxs("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[i.jsxs("button",{onClick:()=>c("desktop"),className:`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${s==="desktop"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Fs,{className:"w-4 h-4 mr-1"}),"Desktop"]}),i.jsxs("button",{onClick:()=>c("tablet"),className:`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${s==="tablet"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Ws,{className:"w-4 h-4 mr-1"}),"Tablet"]}),i.jsxs("button",{onClick:()=>c("mobile"),className:`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${s==="mobile"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Js,{className:"w-4 h-4 mr-1"}),"Mobile"]})]})]}),i.jsxs("div",{className:"text-center text-gray-500 py-8",children:[i.jsx(fr,{className:"w-12 h-12 mx-auto mb-3 text-gray-300"}),i.jsx("p",{className:"text-sm",children:"Select an element to edit its properties"})]})]})},qS=({templateName:n,onTemplateNameChange:r,viewMode:s,onViewModeChange:c,previewMode:u,onPreviewModeChange:f,showGrid:h,onToggleGrid:g,canUndo:y,canRedo:p,onUndo:x,onRedo:w,onSave:b,onPreview:R,onExport:T,onImport:C,onSendTest:S,isDirty:E})=>i.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-3",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"text",value:n,onChange:k=>r(k.target.value),className:"text-lg font-semibold bg-transparent border-none outline-none focus:bg-gray-50 px-2 py-1 rounded",placeholder:"Untitled Template"}),E&&i.jsx("span",{className:"w-2 h-2 bg-orange-400 rounded-full",title:"Unsaved changes"})]}),i.jsxs("div",{className:"flex items-center space-x-1",children:[i.jsx("button",{onClick:x,disabled:!y,className:"p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",title:"Undo (Ctrl+Z)",children:i.jsx(n2,{className:"w-4 h-4"})}),i.jsx("button",{onClick:w,disabled:!p,className:"p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",title:"Redo (Ctrl+Y)",children:i.jsx(UN,{className:"w-4 h-4"})})]}),i.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[i.jsxs("button",{onClick:()=>c("visual"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${s==="visual"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(Ya,{className:"w-4 h-4 mr-1 inline"}),"Visual"]}),i.jsxs("button",{onClick:()=>c("code"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${s==="code"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[i.jsx(F1,{className:"w-4 h-4 mr-1 inline"}),"Code"]})]})]}),i.jsx("div",{className:"flex items-center space-x-3",children:s==="visual"&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[i.jsx("button",{onClick:()=>f("desktop"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${u==="desktop"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,title:"Desktop Preview",children:i.jsx(Fs,{className:"w-4 h-4"})}),i.jsx("button",{onClick:()=>f("tablet"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${u==="tablet"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,title:"Tablet Preview",children:i.jsx(Ws,{className:"w-4 h-4"})}),i.jsx("button",{onClick:()=>f("mobile"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${u==="mobile"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,title:"Mobile Preview",children:i.jsx(Js,{className:"w-4 h-4"})})]}),i.jsx("button",{onClick:g,className:`p-2 rounded transition-colors ${h?"bg-blue-100 text-blue-600":"hover:bg-gray-100 text-gray-600"}`,title:"Toggle Grid",children:i.jsx(iN,{className:"w-4 h-4"})})]})}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsxs("div",{className:"flex items-center space-x-1",children:[i.jsx("button",{onClick:C,className:"p-2 rounded hover:bg-gray-100 text-gray-600",title:"Import Template",children:i.jsx(Ou,{className:"w-4 h-4"})}),i.jsx("button",{onClick:T,className:"p-2 rounded hover:bg-gray-100 text-gray-600",title:"Export HTML",children:i.jsx(Ng,{className:"w-4 h-4"})})]}),i.jsx("div",{className:"w-px h-6 bg-gray-300"}),i.jsxs("button",{onClick:S,className:"px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md flex items-center",children:[i.jsx(Cu,{className:"w-4 h-4 mr-1"}),"Send Test"]}),i.jsxs("button",{onClick:R,className:"px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md flex items-center",children:[i.jsx(Ya,{className:"w-4 h-4 mr-1"}),"Preview"]}),i.jsxs("button",{onClick:b,className:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 flex items-center",children:[i.jsx(hr,{className:"w-4 h-4 mr-1"}),"Save Template"]})]})]})}),YS=()=>localStorage.getItem("auth_token")||null,el=Le.create({baseURL:"/api",headers:{"Content-Type":"application/json"}});el.interceptors.request.use(n=>{const r=YS();return r&&(n.headers.Authorization=`Bearer ${r}`),n});const qa={getTemplates:async()=>(await el.get("/templates")).data.data||[],getTemplate:async n=>{try{const r=await el.get(`/templates/${n}`);if(!r.data.data)throw new Error("Template not found");return r.data.data}catch(r){throw r}},createTemplate:async n=>{const r=await el.post("/templates",n);if(!r.data.data)throw new Error("Failed to create template");return r.data.data},updateTemplate:async(n,r)=>{const s=await el.put(`/templates/${n}`,r);if(!s.data.data)throw new Error("Failed to update template");return s.data.data},deleteTemplate:async n=>{await el.delete(`/templates/${n}`)},generateHTML:n=>{const r=c=>{var f,h,g,y,p,x,w,b,R,T,C,S,E,k,$,z,Y,Z,se;const u=Object.entries(c.styles||{}).map(([Q,I])=>`${Q.replace(/([A-Z])/g,"-$1").toLowerCase()}: ${I}`).join("; ");switch(c.type){case"text":return`<div style="${u}">${((f=c.content)==null?void 0:f.text)||""}</div>`;case"heading":const Q=((h=c.content)==null?void 0:h.level)||"h2";return`<${Q} style="${u}">${((g=c.content)==null?void 0:g.text)||""}</${Q}>`;case"image":return`<img src="${((y=c.content)==null?void 0:y.src)||""}" alt="${((p=c.content)==null?void 0:p.alt)||""}" style="${u}" />`;case"button":return`<a href="${((x=c.content)==null?void 0:x.href)||"#"}" style="${u}">${((w=c.content)==null?void 0:w.text)||""}</a>`;case"divider":return`<hr style="${u}" />`;case"spacer":return`<div style="${u}"></div>`;case"container":const I=((b=c.children)==null?void 0:b.map(r).join(""))||"";return`<div style="${u}">${I}</div>`;case"header":return`
            <header style="${u}">
              ${(R=c.content)!=null&&R.logo?`<img src="${c.content.logo}" alt="Logo" style="max-height: 40px; margin-bottom: 10px;" />`:""}
              <h1 style="margin: 0; font-size: 24px;">${((T=c.content)==null?void 0:T.title)||"Your Company"}</h1>
            </header>
          `;case"footer":return`
            <footer style="${u}">
              <p style="margin: 0;">${((C=c.content)==null?void 0:C.text)||"© 2024 Your Company. All rights reserved."}</p>
              ${(S=c.content)!=null&&S.links?c.content.links.map(ve=>`<a href="${ve.href}" style="margin-right: 15px; color: inherit;">${ve.text}</a>`).join(""):""}
            </footer>
          `;case"product-card":return`
            <div style="${u}">
              ${(E=c.content)!=null&&E.image?`<img src="${c.content.image}" alt="${((k=c.content)==null?void 0:k.title)||"Product"}" style="width: 100%; margin-bottom: 10px;" />`:""}
              <h3 style="margin: 0 0 8px 0; font-size: 18px;">${(($=c.content)==null?void 0:$.title)||"Product Name"}</h3>
              <p style="margin: 0 0 10px 0; color: #666;">${((z=c.content)==null?void 0:z.description)||"Product description here..."}</p>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 20px; font-weight: bold;">${((Y=c.content)==null?void 0:Y.price)||"$99.99"}</span>
                <a href="#" style="background-color: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">${((Z=c.content)==null?void 0:Z.buttonText)||"Buy Now"}</a>
              </div>
            </div>
          `;case"social-media":const oe=((se=c.content)==null?void 0:se.platforms)||["facebook","twitter","instagram"];return`
            <div style="${u}">
              <div style="display: flex; justify-content: center; gap: 15px;">
                ${oe.map(ve=>`
                  <a href="#" style="display: inline-block; width: 32px; height: 32px; background-color: ${GS(ve)}; border-radius: 50%; text-align: center; line-height: 32px; color: white; text-decoration: none;">
                    ${$S(ve)}
                  </a>
                `).join("")}
              </div>
            </div>
          `;default:return`<div style="${u}">Unknown component: ${c.type}</div>`}};return`
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Email Template</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto;">
            ${n.map(r).join("")}
          </div>
        </body>
      </html>
    `},sendTestEmail:async(n,r)=>{const s=await fetch("/api/email/test",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({email:r,template_name:n.name,html_content:n.html_content})});if(!s.ok){const c=await s.json();throw new Error(c.message||"Failed to send test email")}},exportTemplate:(n,r="html")=>{let s,c,u;r==="html"?(s=n.html_content,c=`${n.name||"template"}.html`,u="text/html"):(s=JSON.stringify(n,null,2),c=`${n.name||"template"}.json`,u="application/json");const f=new Blob([s],{type:u}),h=URL.createObjectURL(f),g=document.createElement("a");g.href=h,g.download=c,document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(h)}},GS=n=>({facebook:"#1877f2",twitter:"#1da1f2",instagram:"#e4405f",linkedin:"#0077b5",youtube:"#ff0000",pinterest:"#bd081c"})[n]||"#666666",$S=n=>({facebook:"f",twitter:"t",instagram:"i",linkedin:"in",youtube:"y",pinterest:"p"})[n]||"?",VS=({isOpen:n,onClose:r,htmlContent:s,templateName:c})=>{const[u,f]=N.useState("desktop"),[h,g]=N.useState(!1),[y,p]=N.useState("gmail");if(!n)return null;const x=()=>{switch(u){case"mobile":return"375px";case"tablet":return"768px";case"desktop":return"100%";default:return"100%"}},w=()=>{const b={fontFamily:"Arial, sans-serif",backgroundColor:h?"#1f1f1f":"#ffffff",color:h?"#ffffff":"#000000"};switch(y){case"gmail":return{...b,padding:"20px",backgroundColor:h?"#1f1f1f":"#f6f8fc"};case"outlook":return{...b,padding:"16px",backgroundColor:h?"#1f1f1f":"#ffffff"};case"apple-mail":return{...b,padding:"20px",backgroundColor:h?"#1c1c1e":"#ffffff"};default:return b}};return i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:i.jsxs("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col",children:[i.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[i.jsxs("div",{children:[i.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Email Preview"}),i.jsx("p",{className:"text-sm text-gray-600",children:c})]}),i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[i.jsx("button",{onClick:()=>f("desktop"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${u==="desktop"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:i.jsx(Fs,{className:"w-4 h-4"})}),i.jsx("button",{onClick:()=>f("tablet"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${u==="tablet"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:i.jsx(Ws,{className:"w-4 h-4"})}),i.jsx("button",{onClick:()=>f("mobile"),className:`px-3 py-1 text-sm font-medium rounded-md transition-colors ${u==="mobile"?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:i.jsx(Js,{className:"w-4 h-4"})})]}),i.jsxs("select",{value:y,onChange:b=>p(b.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[i.jsx("option",{value:"gmail",children:"Gmail"}),i.jsx("option",{value:"outlook",children:"Outlook"}),i.jsx("option",{value:"apple-mail",children:"Apple Mail"}),i.jsx("option",{value:"generic",children:"Generic"})]}),i.jsx("button",{onClick:()=>g(!h),className:`p-2 rounded-md transition-colors ${h?"bg-gray-800 text-yellow-400":"bg-gray-100 text-gray-600"}`,title:"Toggle Dark Mode",children:h?i.jsx(FN,{className:"w-4 h-4"}):i.jsx(SN,{className:"w-4 h-4"})}),i.jsx("button",{onClick:r,className:"p-2 text-gray-400 hover:text-gray-600",children:i.jsx(jr,{className:"w-5 h-5"})})]})]}),i.jsx("div",{className:"flex-1 overflow-auto bg-gray-100 p-6",children:i.jsx("div",{className:"flex justify-center",children:i.jsxs("div",{className:"bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300",style:{width:x(),maxWidth:"100%"},children:[i.jsxs("div",{className:"bg-gray-50 border-b px-4 py-3 flex items-center space-x-3",children:[i.jsx(ha,{className:"w-4 h-4 text-gray-500"}),i.jsxs("div",{className:"flex-1",children:[i.jsx("div",{className:"text-sm font-medium text-gray-900",children:c||"Email Template"}),i.jsx("div",{className:"text-xs text-gray-500",children:"from: <EMAIL>"})]}),i.jsx("div",{className:"text-xs text-gray-500",children:new Date().toLocaleDateString()})]}),i.jsx("div",{style:w(),children:i.jsx("div",{dangerouslySetInnerHTML:{__html:s},style:{maxWidth:"100%",wordWrap:"break-word"}})})]})})}),i.jsx("div",{className:"border-t p-4 bg-gray-50",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"text-sm text-gray-600",children:["Preview shows how your email will appear in ",y.replace("-"," "),h?" (Dark Mode)":""]}),i.jsxs("div",{className:"flex space-x-2",children:[i.jsx("button",{onClick:r,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Close"}),i.jsx("button",{onClick:()=>{console.log("Sending test email...")},className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700",children:"Send Test Email"})]})]})})]})})},XS=(n,r)=>{switch(r.type){case"ADD_COMPONENT":const s=[...n.components,r.payload];return{...n,components:s,selectedComponent:r.payload.id,history:[...n.history.slice(0,n.historyIndex+1),s],historyIndex:n.historyIndex+1,isDirty:!0};case"UPDATE_COMPONENT":const c=n.components.map(f=>f.id===r.payload.id?{...f,...r.payload.updates}:f);return{...n,components:c,history:[...n.history.slice(0,n.historyIndex+1),c],historyIndex:n.historyIndex+1,isDirty:!0};case"DELETE_COMPONENT":const u=n.components.filter(f=>f.id!==r.payload);return{...n,components:u,selectedComponent:n.selectedComponent===r.payload?null:n.selectedComponent,history:[...n.history.slice(0,n.historyIndex+1),u],historyIndex:n.historyIndex+1,isDirty:!0};case"SELECT_COMPONENT":return{...n,selectedComponent:r.payload};case"SET_ZOOM":return{...n,zoom:r.payload};case"TOGGLE_GRID":return{...n,showGrid:!n.showGrid};case"SET_PREVIEW_MODE":return{...n,previewMode:r.payload};case"UNDO":return n.historyIndex>0?{...n,components:n.history[n.historyIndex-1],historyIndex:n.historyIndex-1,isDirty:!0}:n;case"REDO":return n.historyIndex<n.history.length-1?{...n,components:n.history[n.historyIndex+1],historyIndex:n.historyIndex+1,isDirty:!0}:n;case"SAVE_STATE":return{...n,isDirty:!1};case"RESET_COMPONENTS":return{...n,components:[],selectedComponent:null,history:[[]],historyIndex:0,isDirty:!1};case"LOAD_COMPONENTS":return{...n,components:r.payload,history:[r.payload],historyIndex:0,isDirty:!1};case"SYNC_FROM_HTML":return{...n,isDirty:!0};default:return n}},Cp=()=>{const n=ai(),{id:r}=Lp(),s=!!r,[c,u]=N.useState(""),[f,h]=N.useState(""),[g,y]=N.useState("visual"),[p,x]=N.useState(""),[w,b]=N.useState(!1),[R,T]=N.useState(!1);N.useEffect(()=>(w?document.body.classList.add("cursor-wait"):document.body.classList.remove("cursor-wait"),()=>{document.body.classList.remove("cursor-wait")}),[w]);const[C,S]=N.useReducer(XS,{components:[],selectedComponent:null,history:[[]],historyIndex:0,zoom:1,showGrid:!0,previewMode:"desktop",isDirty:!1}),[E,k]=N.useState(r||null),[$,z]=N.useState(""),Y=()=>JSON.stringify({components:C.components,name:c,description:f,html_content:Z()})!==$;N.useEffect(()=>{(async()=>{var ne;if(s&&r)try{b(!0);const ee=await qa.getTemplate(r);u(ee.name),h(ee.description||""),x(ee.html_content),ee.components&&Array.isArray(ee.components)?S({type:"LOAD_COMPONENTS",payload:ee.components}):S({type:"RESET_COMPONENTS"}),S({type:"SAVE_STATE"}),z(JSON.stringify({components:ee.components||[],name:ee.name,description:ee.description||"",html_content:ee.html_content}))}catch(ee){Le.isAxiosError(ee)&&((ne=ee.response)==null?void 0:ne.status)===404?(alert("Template not found. It may have been deleted or you don't have permission to access it."),n("/templates")):(console.error("Failed to load template:",ee),alert("An error occurred while loading the template. Please try again later."))}finally{b(!1)}})()},[s,r,n]),N.useEffect(()=>{const P=ne=>{if(Y())return ne.preventDefault(),ne.returnValue="You have unsaved changes. Are you sure you want to leave?"};return window.addEventListener("beforeunload",P),()=>window.removeEventListener("beforeunload",P)},[Y]);const Z=N.useCallback(()=>g==="code"?p:qa.generateHTML(C.components),[C.components,p,g]),se=N.useCallback(P=>{x(P),S({type:"SYNC_FROM_HTML"})},[]),Q=N.useCallback(P=>{if(g==="code"&&P==="visual")try{S({type:"SYNC_FROM_HTML"})}catch(ne){console.error("Failed to sync from HTML:",ne)}y(P)},[g]);N.useEffect(()=>{if(!C.isDirty||!c.trim())return;const P=setTimeout(async()=>{try{const ne={name:c,description:f,html_content:Z(),components:C.components,settings:{width:600,backgroundColor:"#ffffff",fontFamily:"Arial, sans-serif"}};if(E)await qa.updateTemplate(E,ne),S({type:"SAVE_STATE"}),console.log("Template auto-saved (update)");else if(c.trim()){const ee=await qa.createTemplate(ne);ee&&ee.id&&(k(ee.id.toString()),S({type:"SAVE_STATE"}),window.history.replaceState(null,"",`/templates/edit/${ee.id}`),console.log("Template auto-saved (created)"))}z(JSON.stringify({components:C.components,name:c,description:f,html_content:Z()}))}catch(ne){console.error("Auto-save failed:",ne)}},2e3);return()=>clearTimeout(P)},[C.isDirty,C.components,c,f,Z,E]);const I=N.useCallback(P=>{S({type:"ADD_COMPONENT",payload:P})},[]),oe=N.useCallback((P,ne)=>{S({type:"UPDATE_COMPONENT",payload:{id:P,updates:ne}})},[]),ve=N.useCallback(P=>{S({type:"DELETE_COMPONENT",payload:P})},[]),Re=N.useCallback(P=>{S({type:"SELECT_COMPONENT",payload:P})},[]),te=N.useCallback(P=>{S({type:"SET_ZOOM",payload:P})},[]),J=N.useCallback(()=>{S({type:"TOGGLE_GRID"})},[]),be=N.useCallback(P=>{S({type:"SET_PREVIEW_MODE",payload:P})},[]),B=N.useCallback(()=>{S({type:"UNDO"})},[]),K=N.useCallback(()=>{S({type:"REDO"})},[]),ie=async()=>{if(!c.trim()){alert("Please enter a template name before saving.");return}try{b(!0);const P={name:c,description:f,html_content:Z(),components:C.components,settings:{width:600,backgroundColor:"#ffffff",fontFamily:"Arial, sans-serif"}};if(E)await qa.updateTemplate(E,P),alert("Template saved successfully!");else{const ne=await qa.createTemplate(P);ne&&ne.id&&(k(ne.id.toString()),window.history.replaceState(null,"",`/templates/edit/${ne.id}`),alert("Template created successfully!"))}S({type:"SAVE_STATE"}),z(JSON.stringify({components:C.components,name:c,description:f,html_content:Z()}))}catch(P){console.error("Failed to save template:",P),alert("Failed to save template. Please try again.")}finally{b(!1)}},je=()=>{T(!0)},D=()=>{const P={name:c,description:f,html_content:Z(),components:C.components};qa.exportTemplate(P,"html")},X=()=>{const P=document.createElement("input");P.type="file",P.accept=".html",P.onchange=ne=>{var et;const ee=(et=ne.target.files)==null?void 0:et[0];if(ee){const _e=new FileReader;_e.onload=$t=>{var Ga;const cl=(Ga=$t.target)==null?void 0:Ga.result;x(cl)},_e.readAsText(ee)}},P.click()},W=async()=>{const P=prompt("Enter email address for test:");if(P)try{const ne={name:c,description:f,html_content:Z(),components:C.components};await qa.sendTestEmail(ne,P)}catch(ne){console.error("Failed to send test email:",ne)}},F=C.selectedComponent&&C.components.find(P=>P.id===C.selectedComponent)||null;return i.jsx(Mw,{backend:ES,children:i.jsxs("div",{className:"h-screen flex flex-col",children:[w&&i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:i.jsxs("div",{className:"bg-white rounded-lg p-6 flex items-center space-x-3",children:[i.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"}),i.jsx("span",{className:"text-gray-700",children:"Loading..."})]})}),i.jsx("div",{className:"bg-white border-b px-6 py-2",children:i.jsxs("button",{onClick:()=>{Y()?window.confirm("You have unsaved changes. Are you sure you want to leave?")&&n("/templates"):n("/templates")},className:"flex items-center text-gray-600 hover:text-gray-900",children:[i.jsx(xg,{className:"w-4 h-4 mr-2"}),"Back to Templates"]})}),i.jsx(qS,{templateName:c,onTemplateNameChange:u,viewMode:g,onViewModeChange:Q,previewMode:C.previewMode,onPreviewModeChange:be,showGrid:C.showGrid,onToggleGrid:J,canUndo:C.historyIndex>0,canRedo:C.historyIndex<C.history.length-1,onUndo:B,onRedo:K,onSave:ie,onPreview:je,onExport:D,onImport:X,onSendTest:W,isDirty:C.isDirty}),i.jsx("div",{className:"bg-white border-b px-6 py-4",children:i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),i.jsx("input",{type:"text",value:f,onChange:P=>h(P.target.value),className:"input",placeholder:"Enter template description"})]})})}),i.jsx("div",{className:"flex-1 flex overflow-hidden",children:g==="visual"?i.jsxs(i.Fragment,{children:[i.jsx(kS,{onComponentAdd:I}),i.jsx(AS,{components:C.components,selectedComponent:C.selectedComponent,onComponentSelect:Re,onComponentUpdate:oe,onComponentAdd:I,onComponentDelete:ve,zoom:C.zoom,onZoomChange:te,showGrid:C.showGrid,previewMode:C.previewMode}),i.jsx(BS,{selectedComponent:F,onComponentUpdate:P=>{C.selectedComponent&&oe(C.selectedComponent,P)},previewMode:C.previewMode,onPreviewModeChange:be})]}):i.jsx("div",{className:"flex-1 p-6",children:i.jsx("div",{className:"h-full",children:i.jsx("textarea",{value:p,onChange:P=>se(P.target.value),className:"w-full h-full p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your HTML content here..."})})})}),i.jsx(VS,{isOpen:R,onClose:()=>T(!1),htmlContent:Z(),templateName:c})]})})},PS=()=>{const[n,r]=N.useState([]),[s,c]=N.useState(!0),[u,f]=N.useState(null),{showError:h,showSuccess:g}=_u();N.useEffect(()=>{y()},[]);const y=async()=>{try{c(!0),f(null);const b=await jt.get("/campaigns");r(b||[])}catch(b){console.error("Failed to fetch campaigns:",b),f("Failed to load campaigns"),h("Failed to load campaigns")}finally{c(!1)}},p=async b=>{if(confirm("Are you sure you want to delete this campaign? This action cannot be undone."))try{await jt.delete(`/campaigns/${b}`),r(n.filter(R=>R.id!==b)),g("Campaign deleted successfully")}catch(R){console.error("Failed to delete campaign:",R),h("Failed to delete campaign")}},x=b=>{switch(b){case"sent":return"bg-green-100 text-green-800";case"sending":return"bg-blue-100 text-blue-800";case"scheduled":return"bg-yellow-100 text-yellow-800";case"draft":return"bg-gray-100 text-gray-800";case"paused":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=b=>{switch(b){case"sending":return i.jsx(AN,{className:"w-4 h-4"});case"paused":return i.jsx(RN,{className:"w-4 h-4"});default:return i.jsx(ha,{className:"w-4 h-4"})}};return s?i.jsx("div",{className:"p-6",children:i.jsxs("div",{className:"flex items-center justify-center py-12",children:[i.jsx(hN,{className:"w-8 h-8 animate-spin text-primary-600"}),i.jsx("span",{className:"ml-2 text-gray-600",children:"Loading campaigns..."})]})}):u?i.jsx("div",{className:"p-6",children:i.jsxs("div",{className:"text-center py-12",children:[i.jsx(ha,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Failed to load campaigns"}),i.jsx("p",{className:"text-gray-600 mb-6",children:u}),i.jsx("button",{onClick:y,className:"btn btn-primary",children:"Try Again"})]})}):i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center justify-between mb-8",children:[i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Email Campaigns"}),i.jsx("p",{className:"text-gray-600",children:"Create and manage your email campaigns"})]}),i.jsxs(lt,{to:"/campaigns/new",className:"btn btn-primary flex items-center",children:[i.jsx(tl,{className:"w-4 h-4 mr-2"}),"Create Campaign"]})]}),i.jsx("div",{className:"card overflow-hidden",children:i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[i.jsx("thead",{className:"bg-gray-50",children:i.jsxs("tr",{children:[i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campaign"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Recipients"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Delivered"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Opened"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Clicked"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),i.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),i.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map(b=>i.jsxs("tr",{className:"hover:bg-gray-50",children:[i.jsx("td",{className:"px-6 py-4",children:i.jsxs("div",{children:[i.jsx("div",{className:"text-sm font-medium text-gray-900",children:b.name}),i.jsx("div",{className:"text-sm text-gray-500",children:b.subject})]})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:i.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${x(b.status)}`,children:[w(b.status),i.jsx("span",{className:"ml-1 capitalize",children:b.status})]})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.total_recipients>0?b.total_recipients.toLocaleString():"-"}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.delivered_count>0?i.jsxs(i.Fragment,{children:[b.delivered_count.toLocaleString(),b.total_recipients>0&&i.jsxs("span",{className:"text-gray-500 ml-1",children:["(",(b.delivered_count/b.total_recipients*100).toFixed(1),"%)"]})]}):"-"}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.opened_count>0?i.jsxs(i.Fragment,{children:[b.opened_count.toLocaleString(),b.delivered_count>0&&i.jsxs("span",{className:"text-gray-500 ml-1",children:["(",(b.opened_count/b.delivered_count*100).toFixed(1),"%)"]})]}):"-"}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.clicked_count>0?i.jsxs(i.Fragment,{children:[b.clicked_count.toLocaleString(),b.delivered_count>0&&i.jsxs("span",{className:"text-gray-500 ml-1",children:["(",(b.clicked_count/b.delivered_count*100).toFixed(1),"%)"]})]}):"-"}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b.sent_at?i.jsxs("div",{children:[i.jsx("div",{children:"Sent"}),i.jsx("div",{children:new Date(b.sent_at).toLocaleDateString()})]}):b.scheduled_at?i.jsxs("div",{children:[i.jsx("div",{children:"Scheduled"}),i.jsx("div",{children:new Date(b.scheduled_at).toLocaleDateString()})]}):i.jsxs("div",{children:[i.jsx("div",{children:"Created"}),i.jsx("div",{children:new Date(b.created_at).toLocaleDateString()})]})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:i.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[b.status==="sent"&&i.jsx("button",{className:"text-blue-600 hover:text-blue-900",children:i.jsx(vg,{className:"w-4 h-4"})}),(b.status==="draft"||b.status==="scheduled")&&i.jsx(lt,{to:`/campaigns/edit/${b.id}`,className:"text-gray-600 hover:text-gray-900",children:i.jsx(Du,{className:"w-4 h-4"})}),i.jsx("button",{onClick:()=>p(b.id),className:"text-red-600 hover:text-red-900",title:"Delete campaign",children:i.jsx(Sr,{className:"w-4 h-4"})})]})})]},b.id))})]})})}),n.length===0&&i.jsxs("div",{className:"text-center py-12",children:[i.jsx(ha,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No campaigns yet"}),i.jsx("p",{className:"text-gray-600 mb-6",children:"Get started by creating your first email campaign"}),i.jsx(lt,{to:"/campaigns/new",className:"btn btn-primary",children:"Create Your First Campaign"})]})]})},Dp=()=>{const n=ai(),{id:r}=Lp(),s=!!r,[c,u]=N.useState(1),[f,h]=N.useState({name:"",subject:"",template_id:"",recipient_lists:[],scheduled_at:"",send_immediately:!0}),[g,y]=N.useState([]),[p,x]=N.useState([]),[w,b]=N.useState(!1),[R,T]=N.useState(null),C=[{id:1,name:"Campaign Details",icon:un},{id:2,name:"Select Template",icon:un},{id:3,name:"Choose Recipients",icon:fn},{id:4,name:"Schedule & Send",icon:q1}];N.useEffect(()=>{S(),E(),s&&r&&k(r)},[s,r]);const S=async()=>{try{const Q=await jt.get("/templates");y(Q.map(I=>({id:I.id,name:I.name,description:I.description||"Email template"})))}catch(Q){console.error("Failed to fetch templates:",Q),T("Failed to load templates")}},E=async()=>{try{const I=(await jt.get("/contacts")).filter(oe=>oe.status==="active");x([{id:1,name:"All Active Contacts",count:I.length},{id:2,name:"Recent Subscribers",count:Math.floor(I.length*.3)},{id:3,name:"Engaged Users",count:Math.floor(I.length*.6)}])}catch(Q){console.error("Failed to fetch contacts:",Q),T("Failed to load contacts")}},k=async Q=>{try{const I=await jt.get(`/campaigns/${Q}`);h({name:I.name,subject:I.subject,template_id:I.template_id.toString(),recipient_lists:[],scheduled_at:I.scheduled_at||"",send_immediately:!I.scheduled_at})}catch(I){console.error("Failed to fetch campaign:",I),T("Failed to load campaign")}},$=()=>{c<C.length&&u(c+1)},z=()=>{c>1&&u(c-1)},Y=async()=>{var Q,I;try{b(!0),T(null);const oe={...f,status:"draft",template_id:parseInt(f.template_id),scheduled_at:f.send_immediately?null:f.scheduled_at};s&&r?await jt.put(`/campaigns/${r}`,oe):await jt.post("/campaigns",oe),n("/campaigns")}catch(oe){console.error("Failed to save campaign:",oe),T(((I=(Q=oe.response)==null?void 0:Q.data)==null?void 0:I.message)||"Failed to save campaign")}finally{b(!1)}},Z=async()=>{var Q,I;try{b(!0),T(null);const oe={...f,status:f.send_immediately?"sending":"scheduled",template_id:parseInt(f.template_id),scheduled_at:f.send_immediately?null:f.scheduled_at};let ve=r;s?await jt.put(`/campaigns/${r}`,oe):ve=(await jt.post("/campaigns",oe)).id,f.send_immediately&&ve&&await jt.post(`/campaigns/${ve}/send`),n("/campaigns")}catch(oe){console.error("Failed to send campaign:",oe),T(((I=(Q=oe.response)==null?void 0:Q.data)==null?void 0:I.message)||"Failed to send campaign")}finally{b(!1)}},se=()=>{switch(c){case 1:return i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Campaign Name"}),i.jsx("input",{type:"text",value:f.name,onChange:Q=>h({...f,name:Q.target.value}),className:"input",placeholder:"Enter campaign name"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Subject"}),i.jsx("input",{type:"text",value:f.subject,onChange:Q=>h({...f,subject:Q.target.value}),className:"input",placeholder:"Enter email subject line"})]})]});case 2:return i.jsxs("div",{className:"space-y-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Choose a Template"}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:g.map(Q=>i.jsxs("div",{className:`card p-4 cursor-pointer transition-colors ${f.template_id===Q.id.toString()?"ring-2 ring-primary-500 bg-primary-50":"hover:bg-gray-50"}`,onClick:()=>h({...f,template_id:Q.id.toString()}),children:[i.jsx("div",{className:"h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center",children:i.jsx(un,{className:"w-8 h-8 text-gray-400"})}),i.jsx("h4",{className:"font-medium text-gray-900",children:Q.name}),i.jsx("p",{className:"text-sm text-gray-600",children:Q.description})]},Q.id))})]});case 3:return i.jsxs("div",{className:"space-y-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Select Recipients"}),i.jsx("div",{className:"space-y-3",children:p.map(Q=>i.jsxs("div",{className:"flex items-center p-4 border border-gray-200 rounded-lg",children:[i.jsx("input",{type:"checkbox",id:`list-${Q.id}`,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:f.recipient_lists.includes(Q.id),onChange:I=>{I.target.checked?h({...f,recipient_lists:[...f.recipient_lists,Q.id]}):h({...f,recipient_lists:f.recipient_lists.filter(oe=>oe!==Q.id)})}}),i.jsxs("label",{htmlFor:`list-${Q.id}`,className:"ml-3 flex-1",children:[i.jsx("div",{className:"font-medium text-gray-900",children:Q.name}),i.jsxs("div",{className:"text-sm text-gray-600",children:[Q.count," contacts"]})]})]},Q.id))})]});case 4:return i.jsxs("div",{className:"space-y-6",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Schedule & Send"}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"radio",id:"send-now",name:"send-timing",checked:f.send_immediately,onChange:()=>h({...f,send_immediately:!0}),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),i.jsx("label",{htmlFor:"send-now",className:"ml-3 text-sm font-medium text-gray-700",children:"Send immediately"})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"radio",id:"schedule",name:"send-timing",checked:!f.send_immediately,onChange:()=>h({...f,send_immediately:!1}),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),i.jsx("label",{htmlFor:"schedule",className:"ml-3 text-sm font-medium text-gray-700",children:"Schedule for later"})]}),!f.send_immediately&&i.jsx("div",{className:"ml-7",children:i.jsx("input",{type:"datetime-local",value:f.scheduled_at,onChange:Q=>h({...f,scheduled_at:Q.target.value}),className:"input"})})]}),i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[i.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"Campaign Summary"}),i.jsxs("div",{className:"space-y-2 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Campaign Name:"}),i.jsx("span",{className:"font-medium",children:f.name||"Untitled Campaign"})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Subject:"}),i.jsx("span",{className:"font-medium",children:f.subject||"No subject"})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Recipients:"}),i.jsxs("span",{className:"font-medium",children:[f.recipient_lists.reduce((Q,I)=>{const oe=p.find(ve=>ve.id===I);return Q+((oe==null?void 0:oe.count)||0)},0)," contacts"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-gray-600",children:"Send Time:"}),i.jsx("span",{className:"font-medium",children:f.send_immediately?"Immediately":f.scheduled_at||"Not scheduled"})]})]})]})]});default:return null}};return i.jsx("div",{className:"p-6",children:i.jsxs("div",{className:"max-w-4xl mx-auto",children:[i.jsx("div",{className:"flex items-center justify-between mb-8",children:i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsx("button",{onClick:()=>n("/campaigns"),className:"btn btn-outline p-2",children:i.jsx(xg,{className:"w-4 h-4"})}),i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:s?"Edit Campaign":"Create Campaign"}),i.jsxs("p",{className:"text-gray-600",children:["Step ",c," of ",C.length]})]})]})}),i.jsx("div",{className:"mb-8",children:i.jsx("div",{className:"flex items-center justify-between",children:C.map((Q,I)=>i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:`flex items-center justify-center w-10 h-10 rounded-full ${c>=Q.id?"bg-primary-600 text-white":"bg-gray-200 text-gray-600"}`,children:i.jsx(Q.icon,{className:"w-5 h-5"})}),i.jsx("div",{className:"ml-3",children:i.jsx("div",{className:`text-sm font-medium ${c>=Q.id?"text-primary-600":"text-gray-500"}`,children:Q.name})}),I<C.length-1&&i.jsx("div",{className:`w-16 h-0.5 mx-4 ${c>Q.id?"bg-primary-600":"bg-gray-200"}`})]},Q.id))})}),R&&i.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"text-red-800",children:i.jsx("p",{className:"text-sm",children:R})}),i.jsx("button",{onClick:()=>T(null),className:"ml-auto text-red-600 hover:text-red-800",children:"×"})]})}),i.jsx("div",{className:"card p-6 mb-8",children:se()}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("button",{onClick:z,disabled:c===1,className:"btn btn-outline disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),i.jsxs("div",{className:"flex items-center space-x-3",children:[i.jsxs("button",{onClick:Y,disabled:w,className:"btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed",children:[i.jsx(hr,{className:"w-4 h-4 mr-2"}),w?"Saving...":"Save Draft"]}),c===C.length?i.jsxs("button",{onClick:Z,disabled:w,className:"btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[i.jsx(Cu,{className:"w-4 h-4 mr-2"}),w?"Processing...":f.send_immediately?"Send Campaign":"Schedule Campaign"]}):i.jsx("button",{onClick:$,className:"btn btn-primary",children:"Next"})]})]})]})})},dr={getContacts:async()=>Ve.get("/contacts"),getContact:async n=>Ve.get(`/contacts/${n}`),createContact:async n=>Ve.post("/contacts",n),updateContact:async(n,r)=>Ve.put(`/contacts/${n}`,r),deleteContact:async n=>Ve.delete(`/contacts/${n}`),importContacts:async n=>{const r=new FormData;return r.append("csv",n),Ve.upload("/contacts/import",r)},exportContacts:async()=>{const n=await fetch("/api/contacts/export",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}});if(!n.ok)throw new Error("Failed to export contacts");return n.blob()}},QS=({isOpen:n,onClose:r,onContactAdded:s})=>{const[c,u]=N.useState({name:"",email:"",status:"active"}),[f,h]=N.useState(!1),g=async p=>{if(p.preventDefault(),!c.email.trim()){alert("Email is required");return}try{h(!0);const x=await dr.createContact(c);s(x),u({name:"",email:"",status:"active"}),r()}catch(x){console.error("Failed to create contact:",x),alert("Failed to create contact. Please try again.")}finally{h(!1)}},y=()=>{u({name:"",email:"",status:"active"}),r()};return n?i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:i.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[i.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[i.jsx("h2",{className:"text-xl font-semibold",children:"Add New Contact"}),i.jsx("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:i.jsx(jr,{className:"w-6 h-6"})})]}),i.jsxs("form",{onSubmit:g,className:"p-6",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),i.jsx("input",{type:"text",value:c.name,onChange:p=>u(x=>({...x,name:p.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter contact name"})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),i.jsx("input",{type:"email",value:c.email,onChange:p=>u(x=>({...x,email:p.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email address",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),i.jsxs("select",{value:c.status,onChange:p=>u(x=>({...x,status:p.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[i.jsx("option",{value:"active",children:"Active"}),i.jsx("option",{value:"unsubscribed",children:"Unsubscribed"}),i.jsx("option",{value:"bounced",children:"Bounced"})]})]})]}),i.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[i.jsx("button",{type:"button",onClick:y,className:"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md",children:"Cancel"}),i.jsx("button",{type:"submit",disabled:f,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Adding...":"Add Contact"})]})]})]})}):null},ZS=()=>{const[n,r]=N.useState(""),[s,c]=N.useState([]),[u,f]=N.useState([]),[h,g]=N.useState(!0),[y,p]=N.useState(!1);N.useEffect(()=>{x()},[]);const x=async()=>{try{g(!0);const z=await dr.getContacts();f(z)}catch(z){console.error("Failed to load contacts:",z),alert("Failed to load contacts. Please try again.")}finally{g(!1)}},w=async z=>{try{g(!0);const Y=await dr.importContacts(z);alert(`Import completed! ${Y.imported} contacts imported, ${Y.skipped} skipped.`),await x()}catch(Y){console.error("Failed to import contacts:",Y),alert("Failed to import contacts. Please try again.")}finally{g(!1)}},b=async z=>{if(confirm("Are you sure you want to delete this contact?"))try{await dr.deleteContact(z),f(Y=>Y.filter(Z=>Z.id!==z)),c(Y=>Y.filter(Z=>Z!==z))}catch(Y){console.error("Failed to delete contact:",Y),alert("Failed to delete contact. Please try again.")}},R=async()=>{try{const z=await dr.exportContacts(),Y=window.URL.createObjectURL(z),Z=document.createElement("a");Z.href=Y,Z.download="contacts.csv",document.body.appendChild(Z),Z.click(),window.URL.revokeObjectURL(Y),document.body.removeChild(Z)}catch(z){console.error("Failed to export contacts:",z),alert("Failed to export contacts. Please try again.")}},T=z=>{f(Y=>[z,...Y])},C=[{id:1,name:"All Contacts",count:u.length},{id:2,name:"Active",count:u.filter(z=>z.status==="active").length},{id:3,name:"Unsubscribed",count:u.filter(z=>z.status==="unsubscribed").length}],S=z=>{switch(z){case"active":return"bg-green-100 text-green-800";case"unsubscribed":return"bg-yellow-100 text-yellow-800";case"bounced":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},E=u.filter(z=>z.name.toLowerCase().includes(n.toLowerCase())||z.email.toLowerCase().includes(n.toLowerCase())),k=z=>{c(z?E.map(Y=>Y.id):[])},$=(z,Y)=>{c(Y?[...s,z]:s.filter(Z=>Z!==z))};return h?i.jsx("div",{className:"p-6 flex items-center justify-center min-h-96",children:i.jsxs("div",{className:"text-center",children:[i.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),i.jsx("p",{className:"text-gray-600",children:"Loading contacts..."})]})}):i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center justify-between mb-8",children:[i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Contacts"}),i.jsx("p",{className:"text-gray-600",children:"Manage your email contacts and lists"})]}),i.jsxs("div",{className:"flex items-center space-x-3",children:[i.jsx("input",{type:"file",accept:".csv",onChange:z=>{var Z;const Y=(Z=z.target.files)==null?void 0:Z[0];Y&&(w(Y),z.target.value="")},className:"hidden",id:"csv-import"}),i.jsxs("label",{htmlFor:"csv-import",className:"btn btn-outline flex items-center cursor-pointer",children:[i.jsx(Ou,{className:"w-4 h-4 mr-2"}),"Import CSV"]}),i.jsxs("button",{onClick:R,className:"btn btn-outline flex items-center",children:[i.jsx(Ng,{className:"w-4 h-4 mr-2"}),"Export"]}),i.jsxs("button",{onClick:()=>p(!0),className:"btn btn-primary flex items-center",children:[i.jsx(tl,{className:"w-4 h-4 mr-2"}),"Add Contact"]})]})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:C.map(z=>i.jsx("div",{className:"card p-6",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:i.jsx(fn,{className:"w-6 h-6 text-blue-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:z.name}),i.jsxs("p",{className:"text-sm text-gray-600",children:[z.count," contacts"]})]})]})},z.id))}),i.jsx("div",{className:"card p-6 mb-6",children:i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("div",{className:"flex-1 relative",children:[i.jsx(YN,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),i.jsx("input",{type:"text",placeholder:"Search contacts...",value:n,onChange:z=>r(z.target.value),className:"input pl-10"})]}),i.jsxs("button",{className:"btn btn-outline flex items-center",children:[i.jsx(rN,{className:"w-4 h-4 mr-2"}),"Filter"]})]})}),i.jsxs("div",{className:"card overflow-hidden",children:[s.length>0&&i.jsx("div",{className:"bg-blue-50 border-b px-6 py-3",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("span",{className:"text-sm text-blue-700",children:[s.length," contact(s) selected"]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800",children:"Add to List"}),i.jsx("button",{className:"text-sm text-red-600 hover:text-red-800",children:"Delete Selected"})]})]})}),i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[i.jsx("thead",{className:"bg-gray-50",children:i.jsxs("tr",{children:[i.jsx("th",{className:"px-6 py-3 text-left",children:i.jsx("input",{type:"checkbox",checked:s.length===E.length&&E.length>0,onChange:z=>k(z.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added"}),i.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),i.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map(z=>i.jsxs("tr",{className:"hover:bg-gray-50",children:[i.jsx("td",{className:"px-6 py-4",children:i.jsx("input",{type:"checkbox",checked:s.includes(z.id),onChange:Y=>$(z.id,Y.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})}),i.jsx("td",{className:"px-6 py-4",children:i.jsxs("div",{children:[i.jsx("div",{className:"text-sm font-medium text-gray-900",children:z.name}),i.jsx("div",{className:"text-sm text-gray-500",children:z.email})]})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:i.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${S(z.status)}`,children:z.status})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(z.created_at).toLocaleDateString()}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:i.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[i.jsx("button",{className:"text-gray-600 hover:text-gray-900",children:i.jsx(Du,{className:"w-4 h-4"})}),i.jsx("button",{onClick:()=>b(z.id),className:"text-red-600 hover:text-red-900",children:i.jsx(Sr,{className:"w-4 h-4"})})]})})]},z.id))})]})})]}),E.length===0&&i.jsxs("div",{className:"text-center py-12",children:[i.jsx(fn,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),i.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:n?"No contacts found":"No contacts yet"}),i.jsx("p",{className:"text-gray-600 mb-6",children:n?"Try adjusting your search terms":"Get started by adding your first contact or importing a CSV file"}),!n&&i.jsx("button",{className:"btn btn-primary",children:"Add Your First Contact"})]}),i.jsx(QS,{isOpen:y,onClose:()=>p(!1),onContactAdded:T})]})},KS=()=>{const[n,r]=N.useState("30d"),[s,c]=N.useState({totalSent:0,totalDelivered:0,totalOpened:0,totalClicked:0,deliveryRate:0,openRate:0,clickRate:0,unsubscribeRate:0}),[u,f]=N.useState([]),[h,g]=N.useState(!0),[y,p]=N.useState(null);N.useEffect(()=>{x()},[n]);const x=async()=>{try{g(!0),p(null);const b=await ei.getDashboardStats(),R=b.emailsSent||0,T=Math.round(R*.96),C=Math.round(R*(b.openRate/100)),S=Math.round(R*(b.clickRate/100));c({totalSent:R,totalDelivered:T,totalOpened:C,totalClicked:S,deliveryRate:R>0?T/R*100:0,openRate:b.openRate||0,clickRate:b.clickRate||0,unsubscribeRate:.8});const E=await ei.getCampaignPerformance();f(E||[])}catch(b){console.error("Failed to fetch analytics data:",b),p("Failed to load analytics data")}finally{g(!1)}},w=[...u].sort((b,R)=>R.openRate-b.openRate).slice(0,5);return h?i.jsx("div",{className:"p-6",children:i.jsx("div",{className:"flex items-center justify-center h-64",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})})}):y?i.jsx("div",{className:"p-6",children:i.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:i.jsx("div",{className:"flex",children:i.jsxs("div",{className:"ml-3",children:[i.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error Loading Analytics"}),i.jsx("div",{className:"mt-2 text-sm text-red-700",children:i.jsx("p",{children:y})}),i.jsx("div",{className:"mt-4",children:i.jsx("button",{onClick:x,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"Try Again"})})]})})})}):i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center justify-between mb-8",children:[i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Analytics"}),i.jsx("p",{className:"text-gray-600",children:"Track your email campaign performance"})]}),i.jsx("div",{className:"flex items-center space-x-3",children:i.jsxs("select",{value:n,onChange:b=>r(b.target.value),className:"input",children:[i.jsx("option",{value:"7d",children:"Last 7 days"}),i.jsx("option",{value:"30d",children:"Last 30 days"}),i.jsx("option",{value:"90d",children:"Last 90 days"}),i.jsx("option",{value:"1y",children:"Last year"})]})})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[i.jsx("div",{className:"card p-6",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:i.jsx(ha,{className:"w-6 h-6 text-blue-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Emails Sent"}),i.jsx("p",{className:"text-2xl font-bold text-gray-900",children:s.totalSent.toLocaleString()})]})]})}),i.jsx("div",{className:"card p-6",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:i.jsx(Tg,{className:"w-6 h-6 text-green-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Delivery Rate"}),i.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[s.deliveryRate,"%"]})]})]})}),i.jsx("div",{className:"card p-6",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:i.jsx(Ya,{className:"w-6 h-6 text-purple-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Open Rate"}),i.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[s.openRate,"%"]})]})]})}),i.jsx("div",{className:"card p-6",children:i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:i.jsx(Sg,{className:"w-6 h-6 text-orange-600"})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Click Rate"}),i.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[s.clickRate,"%"]})]})]})})]}),i.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[i.jsxs("div",{className:"card p-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Breakdown"}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[i.jsx("span",{className:"text-gray-600",children:"Delivered"}),i.jsxs("span",{className:"font-medium",children:[s.totalDelivered.toLocaleString()," (",s.deliveryRate,"%)"]})]}),i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:i.jsx("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${s.deliveryRate}%`}})})]}),i.jsxs("div",{children:[i.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[i.jsx("span",{className:"text-gray-600",children:"Opened"}),i.jsxs("span",{className:"font-medium",children:[s.totalOpened.toLocaleString()," (",s.openRate,"%)"]})]}),i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:i.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${s.openRate}%`}})})]}),i.jsxs("div",{children:[i.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[i.jsx("span",{className:"text-gray-600",children:"Clicked"}),i.jsxs("span",{className:"font-medium",children:[s.totalClicked.toLocaleString()," (",s.clickRate,"%)"]})]}),i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:i.jsx("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:`${s.clickRate}%`}})})]}),i.jsxs("div",{children:[i.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[i.jsx("span",{className:"text-gray-600",children:"Unsubscribed"}),i.jsxs("span",{className:"font-medium",children:[s.unsubscribeRate,"%"]})]}),i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:i.jsx("div",{className:"bg-red-600 h-2 rounded-full",style:{width:`${s.unsubscribeRate}%`}})})]})]})]}),i.jsxs("div",{className:"card p-6",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Top Performing Campaigns"}),i.jsx("div",{className:"space-y-3",children:w.map((b,R)=>i.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium",children:R+1}),i.jsxs("div",{className:"ml-3",children:[i.jsx("div",{className:"text-sm font-medium text-gray-900",children:b.name}),i.jsx("div",{className:"text-xs text-gray-500",children:new Date(b.sentAt).toLocaleDateString()})]})]}),i.jsxs("div",{className:"text-right",children:[i.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[b.openRate,"%"]}),i.jsx("div",{className:"text-xs text-gray-500",children:"open rate"})]})]},b.id))})]})]}),i.jsxs("div",{className:"card",children:[i.jsx("div",{className:"p-6 border-b",children:i.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Campaign Performance"})}),i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[i.jsx("thead",{className:"bg-gray-50",children:i.jsxs("tr",{children:[i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campaign"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sent"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Delivered"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Opened"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Clicked"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Open Rate"}),i.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Click Rate"})]})}),i.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map(b=>i.jsxs("tr",{className:"hover:bg-gray-50",children:[i.jsx("td",{className:"px-6 py-4",children:i.jsxs("div",{children:[i.jsx("div",{className:"text-sm font-medium text-gray-900",children:b.name}),i.jsx("div",{className:"text-sm text-gray-500",children:new Date(b.sentAt).toLocaleDateString()})]})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.sent.toLocaleString()}),i.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[b.delivered.toLocaleString(),i.jsxs("span",{className:"text-gray-500 ml-1",children:["(",(b.delivered/b.sent*100).toFixed(1),"%)"]})]}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.opened.toLocaleString()}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b.clicked.toLocaleString()}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:i.jsxs("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${b.openRate>=25?"bg-green-100 text-green-800":b.openRate>=20?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[b.openRate,"%"]})}),i.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:i.jsxs("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${b.clickRate>=4?"bg-green-100 text-green-800":b.clickRate>=3?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[b.clickRate,"%"]})})]},b.id))})]})})]})]})},IS=()=>{const{user:n,updateUser:r}=wr(),{showSuccess:s,showError:c}=_u(),[u,f]=N.useState("profile"),[h,g]=N.useState(!1),[y,p]=N.useState(!1),[x,w]=N.useState(!1),[b,R]=N.useState(!1),[T,C]=N.useState({name:(n==null?void 0:n.name)||"",email:(n==null?void 0:n.email)||""}),[S,E]=N.useState({}),[k,$]=N.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[z,Y]=N.useState({}),[Z,se]=N.useState({emailNotifications:!0,campaignUpdates:!0,systemAlerts:!0,weeklyReports:!1});N.useEffect(()=>{n&&C({name:n.name,email:n.email})},[n]);const Q=()=>{const J={};return T.name.trim()||(J.name="Name is required"),T.email.trim()?/\S+@\S+\.\S+/.test(T.email)||(J.email="Please enter a valid email address"):J.email="Email is required",E(J),Object.keys(J).length===0},I=()=>{const J={};return k.currentPassword||(J.currentPassword="Current password is required"),k.newPassword?k.newPassword.length<6&&(J.newPassword="Password must be at least 6 characters long"):J.newPassword="New password is required",k.confirmPassword?k.newPassword!==k.confirmPassword&&(J.confirmPassword="Passwords do not match"):J.confirmPassword="Please confirm your new password",Y(J),Object.keys(J).length===0},oe=async J=>{var be,B;if(J.preventDefault(),!!Q()){g(!0);try{const K=await jt.put("/user/profile",T);r(K.user),s("Profile updated successfully")}catch(K){c(((B=(be=K.response)==null?void 0:be.data)==null?void 0:B.message)||"Failed to update profile")}finally{g(!1)}}},ve=async J=>{var be,B;if(J.preventDefault(),!!I()){g(!0);try{await jt.put("/user/password",{current_password:k.currentPassword,new_password:k.newPassword}),$({currentPassword:"",newPassword:"",confirmPassword:""}),s("Password updated successfully")}catch(K){c(((B=(be=K.response)==null?void 0:be.data)==null?void 0:B.message)||"Failed to update password")}finally{g(!1)}}},Re=async J=>{var be,B;J.preventDefault(),g(!0);try{await jt.put("/user/notifications",Z),s("Notification settings updated successfully")}catch(K){c(((B=(be=K.response)==null?void 0:be.data)==null?void 0:B.message)||"Failed to update notification settings")}finally{g(!1)}},te=[{id:"profile",name:"Profile Information",icon:Ru},{id:"password",name:"Change Password",icon:ru},{id:"notifications",name:"Notifications",icon:H1}];return i.jsxs("div",{className:"p-6 max-w-4xl mx-auto",children:[i.jsxs("div",{className:"mb-8",children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Account Settings"}),i.jsx("p",{className:"text-gray-600",children:"Manage your account information and preferences"})]}),i.jsx("div",{className:"border-b border-gray-200 mb-8",children:i.jsx("nav",{className:"-mb-px flex space-x-8",children:te.map(J=>{const be=J.icon;return i.jsxs("button",{onClick:()=>f(J.id),className:`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${u===J.id?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[i.jsx(be,{className:"w-4 h-4 mr-2"}),J.name]},J.id)})})}),u==="profile"&&i.jsxs("div",{className:"card p-6",children:[i.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Profile Information"}),i.jsxs("form",{onSubmit:oe,className:"space-y-6",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),i.jsx("input",{type:"text",id:"name",value:T.name,onChange:J=>C({...T,name:J.target.value}),className:`input ${S.name?"border-red-500":""}`,placeholder:"Enter your full name"}),S.name&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.name})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),i.jsx("input",{type:"email",id:"email",value:T.email,onChange:J=>C({...T,email:J.target.value}),className:`input ${S.email?"border-red-500":""}`,placeholder:"Enter your email address"}),S.email&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:S.email})]}),i.jsx("div",{className:"flex justify-end",children:i.jsxs("button",{type:"submit",disabled:h,className:"btn btn-primary flex items-center",children:[i.jsx(hr,{className:"w-4 h-4 mr-2"}),h?"Saving...":"Save Changes"]})})]})]}),u==="password"&&i.jsxs("div",{className:"card p-6",children:[i.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Change Password"}),i.jsxs("form",{onSubmit:ve,className:"space-y-6",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:y?"text":"password",id:"currentPassword",value:k.currentPassword,onChange:J=>$({...k,currentPassword:J.target.value}),className:`input pr-10 ${z.currentPassword?"border-red-500":""}`,placeholder:"Enter your current password"}),i.jsx("button",{type:"button",onClick:()=>p(!y),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:y?i.jsx(Ps,{className:"w-4 h-4 text-gray-400"}):i.jsx(Ya,{className:"w-4 h-4 text-gray-400"})})]}),z.currentPassword&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:z.currentPassword})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:x?"text":"password",id:"newPassword",value:k.newPassword,onChange:J=>$({...k,newPassword:J.target.value}),className:`input pr-10 ${z.newPassword?"border-red-500":""}`,placeholder:"Enter your new password"}),i.jsx("button",{type:"button",onClick:()=>w(!x),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:x?i.jsx(Ps,{className:"w-4 h-4 text-gray-400"}):i.jsx(Ya,{className:"w-4 h-4 text-gray-400"})})]}),z.newPassword&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:z.newPassword})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:b?"text":"password",id:"confirmPassword",value:k.confirmPassword,onChange:J=>$({...k,confirmPassword:J.target.value}),className:`input pr-10 ${z.confirmPassword?"border-red-500":""}`,placeholder:"Confirm your new password"}),i.jsx("button",{type:"button",onClick:()=>R(!b),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:b?i.jsx(Ps,{className:"w-4 h-4 text-gray-400"}):i.jsx(Ya,{className:"w-4 h-4 text-gray-400"})})]}),z.confirmPassword&&i.jsx("p",{className:"mt-1 text-sm text-red-600",children:z.confirmPassword})]}),i.jsx("div",{className:"flex justify-end",children:i.jsxs("button",{type:"submit",disabled:h,className:"btn btn-primary flex items-center",children:[i.jsx(hr,{className:"w-4 h-4 mr-2"}),h?"Updating...":"Update Password"]})})]})]}),u==="notifications"&&i.jsxs("div",{className:"card p-6",children:[i.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Notification Preferences"}),i.jsxs("form",{onSubmit:Re,className:"space-y-6",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Email Notifications"}),i.jsx("p",{className:"text-sm text-gray-500",children:"Receive notifications via email"})]}),i.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[i.jsx("input",{type:"checkbox",checked:Z.emailNotifications,onChange:J=>se({...Z,emailNotifications:J.target.checked}),className:"sr-only peer"}),i.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Campaign Updates"}),i.jsx("p",{className:"text-sm text-gray-500",children:"Get notified about campaign status changes"})]}),i.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[i.jsx("input",{type:"checkbox",checked:Z.campaignUpdates,onChange:J=>se({...Z,campaignUpdates:J.target.checked}),className:"sr-only peer"}),i.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"System Alerts"}),i.jsx("p",{className:"text-sm text-gray-500",children:"Important system notifications and alerts"})]}),i.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[i.jsx("input",{type:"checkbox",checked:Z.systemAlerts,onChange:J=>se({...Z,systemAlerts:J.target.checked}),className:"sr-only peer"}),i.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Weekly Reports"}),i.jsx("p",{className:"text-sm text-gray-500",children:"Receive weekly performance summaries"})]}),i.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[i.jsx("input",{type:"checkbox",checked:Z.weeklyReports,onChange:J=>se({...Z,weeklyReports:J.target.checked}),className:"sr-only peer"}),i.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]})]}),i.jsx("div",{className:"flex justify-end",children:i.jsxs("button",{type:"submit",disabled:h,className:"btn btn-primary flex items-center",children:[i.jsx(hr,{className:"w-4 h-4 mr-2"}),h?"Saving...":"Save Preferences"]})})]})]})]})},FS=({children:n})=>{const{user:r,logout:s}=wr(),c=pa(),[u,f]=N.useState(!1),[h,g]=N.useState(!1),y=[{name:"Dashboard",href:"/dashboard",icon:dN},{name:"Templates",href:"/templates",icon:un},{name:"Campaigns",href:"/campaigns",icon:ha},{name:"Contacts",href:"/contacts",icon:fn},{name:"Analytics",href:"/analytics",icon:vg}],p=x=>c.pathname===x||c.pathname.startsWith(x+"/");return i.jsxs("div",{className:"flex h-screen bg-gray-100",children:[i.jsxs("div",{className:`${u?"block":"hidden"} fixed inset-0 z-50 lg:hidden`,children:[i.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>f(!1)}),i.jsxs("div",{className:"fixed top-0 left-0 flex flex-col w-64 h-full bg-white shadow-xl",children:[i.jsxs("div",{className:"flex items-center justify-between h-16 px-4 border-b",children:[i.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Email Marketing"}),i.jsx("button",{onClick:()=>f(!1),className:"text-gray-500 hover:text-gray-700",children:i.jsx(jr,{className:"w-6 h-6"})})]}),i.jsx("nav",{className:"flex-1 px-4 py-4 space-y-2",children:y.map(x=>{const w=x.icon;return i.jsxs(lt,{to:x.href,className:`flex items-center px-3 py-2 text-sm font-medium rounded-md ${p(x.href)?"bg-primary-100 text-primary-700":"text-gray-700 hover:bg-gray-100"}`,onClick:()=>f(!1),children:[i.jsx(w,{className:"w-5 h-5 mr-3"}),x.name]},x.name)})})]})]}),i.jsxs("div",{className:"hidden lg:flex lg:flex-col lg:w-64 lg:bg-white lg:border-r",children:[i.jsx("div",{className:"flex items-center h-16 px-4 border-b",children:i.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Email Marketing"})}),i.jsx("nav",{className:"flex-1 px-4 py-4 space-y-2",children:y.map(x=>{const w=x.icon;return i.jsxs(lt,{to:x.href,className:`flex items-center px-3 py-2 text-sm font-medium rounded-md ${p(x.href)?"bg-primary-100 text-primary-700":"text-gray-700 hover:bg-gray-100"}`,children:[i.jsx(w,{className:"w-5 h-5 mr-3"}),x.name]},x.name)})})]}),i.jsxs("div",{className:"flex flex-col flex-1 overflow-hidden",children:[i.jsx("header",{className:"bg-white border-b",children:i.jsxs("div",{className:"flex items-center justify-between h-16 px-4",children:[i.jsx("button",{onClick:()=>f(!0),className:"text-gray-500 hover:text-gray-700 lg:hidden",children:i.jsx(vN,{className:"w-6 h-6"})}),i.jsx("div",{className:"flex items-center space-x-4",children:i.jsxs("div",{className:"relative",children:[i.jsxs("button",{onClick:()=>g(!h),className:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md",children:[i.jsx(Ru,{className:"w-5 h-5 text-gray-500"}),i.jsx("span",{children:r==null?void 0:r.name})]}),h&&i.jsxs("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border",children:[i.jsxs(lt,{to:"/profile",onClick:()=>g(!1),className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[i.jsx(VN,{className:"w-4 h-4 mr-2"}),"Account Settings"]}),i.jsxs("button",{onClick:()=>{g(!1),s()},className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[i.jsx(gN,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})})]})}),i.jsx("main",{className:"flex-1 overflow-auto",children:n})]})]})};function JS(){const{notifications:n,removeNotification:r}=_u();return i.jsx(O1,{children:i.jsxs("div",{className:"min-h-screen bg-gray-50",children:[i.jsxs(Qm,{children:[i.jsx(ft,{path:"/login",element:i.jsx(m2,{})}),i.jsx(ft,{path:"/*",element:i.jsx(R1,{children:i.jsx(FS,{children:i.jsxs(Qm,{children:[i.jsx(ft,{path:"/",element:i.jsx(vu,{to:"/dashboard",replace:!0})}),i.jsx(ft,{path:"/dashboard",element:i.jsx(p2,{})}),i.jsx(ft,{path:"/templates",element:i.jsx(g2,{})}),i.jsx(ft,{path:"/templates/new",element:i.jsx(Cp,{})}),i.jsx(ft,{path:"/templates/edit/:id",element:i.jsx(Cp,{})}),i.jsx(ft,{path:"/campaigns",element:i.jsx(PS,{})}),i.jsx(ft,{path:"/campaigns/new",element:i.jsx(Dp,{})}),i.jsx(ft,{path:"/campaigns/edit/:id",element:i.jsx(Dp,{})}),i.jsx(ft,{path:"/contacts",element:i.jsx(ZS,{})}),i.jsx(ft,{path:"/analytics",element:i.jsx(KS,{})}),i.jsx(ft,{path:"/profile",element:i.jsx(IS,{})})]})})})})]}),i.jsx(h2,{notifications:n,onClose:r})]})})}Cx.createRoot(document.getElementById("root")).render(i.jsx(Op.StrictMode,{children:i.jsx(Yv,{children:i.jsx(JS,{})})}));
//# sourceMappingURL=index-BtVPnFwL.js.map
