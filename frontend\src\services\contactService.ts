import { apiService } from './api'

export interface Contact {
  id: number
  name: string
  email: string
  status: 'active' | 'unsubscribed' | 'bounced'
  created_at: string
  updated_at: string
}

export interface ContactImportResult {
  imported: number
  skipped: number
  errors: string[]
}

export const contactService = {
  // Get all contacts
  getContacts: async (): Promise<Contact[]> => {
    return apiService.get<Contact[]>('/contacts')
  },

  // Get a single contact
  getContact: async (id: number): Promise<Contact> => {
    return apiService.get<Contact>(`/contacts/${id}`)
  },

  // Create a new contact
  createContact: async (contactData: Omit<Contact, 'id' | 'created_at' | 'updated_at'>): Promise<Contact> => {
    return apiService.post<Contact>('/contacts', contactData)
  },

  // Update a contact
  updateContact: async (id: number, contactData: Partial<Contact>): Promise<Contact> => {
    return apiService.put<Contact>(`/contacts/${id}`, contactData)
  },

  // Delete a contact
  deleteContact: async (id: number): Promise<void> => {
    return apiService.delete<void>(`/contacts/${id}`)
  },

  // Import contacts from CSV
  importContacts: async (csvFile: File): Promise<ContactImportResult> => {
    const formData = new FormData()
    formData.append('csv', csvFile)
    
    return apiService.upload<ContactImportResult>('/contacts/import', formData)
  },

  // Export contacts to CSV
  exportContacts: async (): Promise<Blob> => {
    const response = await fetch('/api/contacts/export', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })
    
    if (!response.ok) {
      throw new Error('Failed to export contacts')
    }
    
    return response.blob()
  }
}
