import React, { useState, useReducer, useCallback, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { ArrowLeft } from 'lucide-react'

// Enhanced Template Editor Components
import { TemplateEditorCanvas } from '../components/TemplateEditor/TemplateEditorCanvas'
import { ComponentLibrary } from '../components/TemplateEditor/ComponentLibrary'
import { PropertiesPanel } from '../components/TemplateEditor/PropertiesPanel'
import { TemplateEditorToolbar } from '../components/TemplateEditor/TemplateEditorToolbar'

// Types
import { TemplateComponent, TemplateEditorState, TemplateEditorAction } from '../types/templateEditor'

// API
import { templateEditorApi, TemplateData } from '../services/templateEditorApi'

// Components
import { PreviewModal } from '../components/TemplateEditor/PreviewModal'
import axios from 'axios'

// Template Editor Reducer
const templateEditorReducer = (state: TemplateEditorState, action: TemplateEditorAction): TemplateEditorState => {
  switch (action.type) {
    case 'ADD_COMPONENT':
      const newComponents = [...state.components, action.payload]
      return {
        ...state,
        components: newComponents,
        selectedComponent: action.payload.id,
        history: [...state.history.slice(0, state.historyIndex + 1), newComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }

    case 'UPDATE_COMPONENT':
      const updatedComponents = state.components.map(comp =>
        comp.id === action.payload.id ? { ...comp, ...action.payload.updates } : comp
      )
      return {
        ...state,
        components: updatedComponents,
        history: [...state.history.slice(0, state.historyIndex + 1), updatedComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }

    case 'DELETE_COMPONENT':
      const filteredComponents = state.components.filter(comp => comp.id !== action.payload)
      return {
        ...state,
        components: filteredComponents,
        selectedComponent: state.selectedComponent === action.payload ? null : state.selectedComponent,
        history: [...state.history.slice(0, state.historyIndex + 1), filteredComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }

    case 'SELECT_COMPONENT':
      return {
        ...state,
        selectedComponent: action.payload
      }

    case 'SET_ZOOM':
      return {
        ...state,
        zoom: action.payload
      }

    case 'TOGGLE_GRID':
      return {
        ...state,
        showGrid: !state.showGrid
      }

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        previewMode: action.payload
      }

    case 'UNDO':
      if (state.historyIndex > 0) {
        return {
          ...state,
          components: state.history[state.historyIndex - 1],
          historyIndex: state.historyIndex - 1,
          isDirty: true
        }
      }
      return state

    case 'REDO':
      if (state.historyIndex < state.history.length - 1) {
        return {
          ...state,
          components: state.history[state.historyIndex + 1],
          historyIndex: state.historyIndex + 1,
          isDirty: true
        }
      }
      return state

    case 'SAVE_STATE':
      return {
        ...state,
        isDirty: false
      }

    case 'RESET_COMPONENTS':
      return {
        ...state,
        components: [],
        selectedComponent: null,
        history: [[]],
        historyIndex: 0,
        isDirty: false
      }

    case 'LOAD_COMPONENTS':
      return {
        ...state,
        components: action.payload,
        history: [action.payload],
        historyIndex: 0,
        isDirty: false
      }

    case 'SYNC_FROM_HTML':
      return {
        ...state,
        isDirty: true
      }

    default:
      return state
  }
}

const TemplateEditor: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)

  // Template metadata
  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [viewMode, setViewMode] = useState<'visual' | 'code'>('visual')
  const [htmlContent, setHtmlContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  // Add loading overlay when isLoading is true
  useEffect(() => {
    if (isLoading) {
      document.body.classList.add('cursor-wait')
    } else {
      document.body.classList.remove('cursor-wait')
    }
    return () => {
      document.body.classList.remove('cursor-wait')
    }
  }, [isLoading])

  // Template editor state
  const [state, dispatch] = useReducer(templateEditorReducer, {
    components: [],
    selectedComponent: null,
    history: [[]],
    historyIndex: 0,
    zoom: 1,
    showGrid: true,
    previewMode: 'desktop',
    isDirty: false
  })

  // Track if template has been saved at least once
  const [templateId, setTemplateId] = useState<string | null>(id || null)
  const [lastSavedState, setLastSavedState] = useState<string>('')

  // Check if there are unsaved changes
  const hasUnsavedChanges = () => {
    const currentState = JSON.stringify({
      components: state.components,
      name: templateName,
      description: templateDescription,
      html_content: generateHTML()
    })
    return currentState !== lastSavedState
  }

  // Load template data if editing
  useEffect(() => {
    const loadTemplate = async () => {
      if (isEditing && id) {
        try {
          setIsLoading(true)
          const template = await templateEditorApi.getTemplate(id)

          setTemplateName(template.name)
          setTemplateDescription(template.description || '')
          setHtmlContent(template.html_content)

          // Load components directly
          if (template.components && Array.isArray(template.components)) {
            dispatch({
              type: 'LOAD_COMPONENTS',
              payload: template.components
            })
          } else {
            dispatch({ type: 'RESET_COMPONENTS' })
          }

          // Mark as clean after loading
          dispatch({ type: 'SAVE_STATE' })
          setLastSavedState(JSON.stringify({
            components: template.components || [],
            name: template.name,
            description: template.description || '',
            html_content: template.html_content
          }))
        } catch (error) {
          if (axios.isAxiosError(error) && error.response?.status === 404) {
            // Handle 404 specifically
            alert(`Template not found. It may have been deleted or you don't have permission to access it.`)
            navigate('/templates') // Redirect to templates list
          } else {
            // Handle other errors
            console.error('Failed to load template:', error)
            alert('An error occurred while loading the template. Please try again later.')
          }
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadTemplate()
  }, [isEditing, id, navigate])

  // Warn user about unsaved changes when leaving the page
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges()) {
        e.preventDefault()
        return (e.returnValue = 'You have unsaved changes. Are you sure you want to leave?')
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Generate HTML from components
  const generateHTML = useCallback(() => {
    if (viewMode === 'code') {
      return htmlContent
    }
    return templateEditorApi.generateHTML(state.components)
  }, [state.components, htmlContent, viewMode])

  // Handle HTML content changes in code mode
  const handleHtmlContentChange = useCallback((newHtmlContent: string) => {
    setHtmlContent(newHtmlContent)
    dispatch({ type: 'SYNC_FROM_HTML' })
  }, [])

  // Sync components when switching from code to visual mode
  const handleViewModeChange = useCallback((newViewMode: 'visual' | 'code') => {
    if (viewMode === 'code' && newViewMode === 'visual') {
      // Try to parse HTML and extract components
      try {
        // For now, just mark as dirty to trigger save
        dispatch({ type: 'SYNC_FROM_HTML' })
      } catch (error) {
        console.error('Failed to sync from HTML:', error)
      }
    }
    setViewMode(newViewMode)
  }, [viewMode])



  // Auto-save functionality
  useEffect(() => {
    if (!state.isDirty || !templateName.trim()) return

    const autoSaveTimer = setTimeout(async () => {
      try {
        const templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'> = {
          name: templateName,
          description: templateDescription,
          html_content: generateHTML(),
          components: state.components,
          settings: {
            width: 600,
            backgroundColor: '#ffffff',
            fontFamily: 'Arial, sans-serif'
          }
        }

        if (templateId) {
          // Update existing template
          await templateEditorApi.updateTemplate(templateId, templateData)
          dispatch({ type: 'SAVE_STATE' })
          console.log('Template auto-saved (update)')
        } else if (templateName.trim()) {
          // Create new template
          const newTemplate = await templateEditorApi.createTemplate(templateData)
          if (newTemplate && newTemplate.id) {
            setTemplateId(newTemplate.id.toString())
            dispatch({ type: 'SAVE_STATE' })
            // Update URL to reflect the new template ID
            window.history.replaceState(null, '', `/templates/edit/${newTemplate.id}`)
            console.log('Template auto-saved (created)')
          }
        }

        // Update last saved state
        setLastSavedState(JSON.stringify({
          components: state.components,
          name: templateName,
          description: templateDescription,
          html_content: generateHTML()
        }))
      } catch (error) {
        console.error('Auto-save failed:', error)
      }
    }, 2000) // Auto-save after 2 seconds of inactivity

    return () => clearTimeout(autoSaveTimer)
  }, [state.isDirty, state.components, templateName, templateDescription, generateHTML, templateId])

  // Event handlers
  const handleComponentAdd = useCallback((component: TemplateComponent) => {
    dispatch({ type: 'ADD_COMPONENT', payload: component })
  }, [])

  const handleComponentUpdate = useCallback((id: string, updates: Partial<TemplateComponent>) => {
    dispatch({ type: 'UPDATE_COMPONENT', payload: { id, updates } })
  }, [])

  const handleComponentDelete = useCallback((id: string) => {
    dispatch({ type: 'DELETE_COMPONENT', payload: id })
  }, [])

  const handleComponentSelect = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_COMPONENT', payload: id })
  }, [])

  const handleZoomChange = useCallback((zoom: number) => {
    dispatch({ type: 'SET_ZOOM', payload: zoom })
  }, [])

  const handleToggleGrid = useCallback(() => {
    dispatch({ type: 'TOGGLE_GRID' })
  }, [])

  const handlePreviewModeChange = useCallback((mode: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: mode })
  }, [])

  const handleUndo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const handleRedo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const handleSave = async () => {
    if (!templateName.trim()) {
      alert('Please enter a template name before saving.')
      return
    }

    try {
      setIsLoading(true)

      const templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'> = {
        name: templateName,
        description: templateDescription,
        html_content: generateHTML(),
        components: state.components,
        settings: {
          width: 600,
          backgroundColor: '#ffffff',
          fontFamily: 'Arial, sans-serif'
        }
      }

      if (templateId) {
        // Update existing template
        await templateEditorApi.updateTemplate(templateId, templateData)
        alert('Template saved successfully!')
      } else {
        // Create new template
        const newTemplate = await templateEditorApi.createTemplate(templateData)
        if (newTemplate && newTemplate.id) {
          setTemplateId(newTemplate.id.toString())
          // Update URL to reflect the new template ID
          window.history.replaceState(null, '', `/templates/edit/${newTemplate.id}`)
          alert('Template created successfully!')
        }
      }

      dispatch({ type: 'SAVE_STATE' })
      setLastSavedState(JSON.stringify({
        components: state.components,
        name: templateName,
        description: templateDescription,
        html_content: generateHTML()
      }))
    } catch (error) {
      console.error('Failed to save template:', error)
      alert('Failed to save template. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    setShowPreview(true)
  }

  const handleExport = () => {
    const templateData: TemplateData = {
      name: templateName,
      description: templateDescription,
      html_content: generateHTML(),
      components: state.components
    }
    templateEditorApi.exportTemplate(templateData, 'html')
  }

  const handleImport = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.html'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const html = e.target?.result as string
          setHtmlContent(html)
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  const handleSendTest = async () => {
    const email = prompt('Enter email address for test:')
    if (email) {
      try {
        const templateData: TemplateData = {
          name: templateName,
          description: templateDescription,
          html_content: generateHTML(),
          components: state.components
        }
        await templateEditorApi.sendTestEmail(templateData, email)
        // TODO: Show success notification
      } catch (error) {
        console.error('Failed to send test email:', error)
        // TODO: Show error notification
      }
    }
  }

  const selectedComponentData = state.selectedComponent
    ? state.components.find(c => c.id === state.selectedComponent) || null
    : null

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col">
        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
              <span className="text-gray-700">Loading...</span>
            </div>
          </div>
        )}
        {/* Back Button */}
        <div className="bg-white border-b px-6 py-2">
          <button
            onClick={() => {
              if (hasUnsavedChanges()) {
                const confirmed = window.confirm('You have unsaved changes. Are you sure you want to leave?')
                if (confirmed) {
                  navigate('/templates')
                }
              } else {
                navigate('/templates')
              }
            }}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Templates
          </button>
        </div>

        {/* Toolbar */}
        <TemplateEditorToolbar
          templateName={templateName}
          onTemplateNameChange={setTemplateName}
          viewMode={viewMode}
          onViewModeChange={handleViewModeChange}
          previewMode={state.previewMode}
          onPreviewModeChange={handlePreviewModeChange}
          showGrid={state.showGrid}
          onToggleGrid={handleToggleGrid}
          canUndo={state.historyIndex > 0}
          canRedo={state.historyIndex < state.history.length - 1}
          onUndo={handleUndo}
          onRedo={handleRedo}
          onSave={handleSave}
          onPreview={handlePreview}
          onExport={handleExport}
          onImport={handleImport}
          onSendTest={handleSendTest}
          isDirty={state.isDirty}
        />

        {/* Template Settings */}
        <div className="bg-white border-b px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <input
                type="text"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                className="input"
                placeholder="Enter template description"
              />
            </div>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 flex overflow-hidden">
          {viewMode === 'visual' ? (
            <>
              {/* Component Library */}
              <ComponentLibrary onComponentAdd={handleComponentAdd} />

              {/* Canvas */}
              <TemplateEditorCanvas
                components={state.components}
                selectedComponent={state.selectedComponent}
                onComponentSelect={handleComponentSelect}
                onComponentUpdate={handleComponentUpdate}
                onComponentAdd={handleComponentAdd}
                onComponentDelete={handleComponentDelete}
                zoom={state.zoom}
                onZoomChange={handleZoomChange}
                showGrid={state.showGrid}
                previewMode={state.previewMode}
              />

              {/* Properties Panel */}
              <PropertiesPanel
                selectedComponent={selectedComponentData}
                onComponentUpdate={(updates) => {
                  if (state.selectedComponent) {
                    handleComponentUpdate(state.selectedComponent, updates)
                  }
                }}
                previewMode={state.previewMode}
                onPreviewModeChange={handlePreviewModeChange}
              />
            </>
          ) : (
            /* Code Editor */
            <div className="flex-1 p-6">
              <div className="h-full">
                <textarea
                  value={htmlContent}
                  onChange={(e) => handleHtmlContentChange(e.target.value)}
                  className="w-full h-full p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your HTML content here..."
                />
              </div>
            </div>
          )}
        </div>

        {/* Preview Modal */}
        <PreviewModal
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          htmlContent={generateHTML()}
          templateName={templateName}
        />
      </div>
    </DndProvider>
  )
}

export default TemplateEditor


