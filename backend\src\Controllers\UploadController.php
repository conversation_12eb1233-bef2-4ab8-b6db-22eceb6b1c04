<?php

namespace App\Controllers;

class UploadController extends BaseController
{
    private string $uploadDir;
    private array $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    private int $maxFileSize = 5 * 1024 * 1024; // 5MB
    
    public function __construct()
    {
        parent::__construct();
        $this->uploadDir = __DIR__ . '/../../uploads/';
        
        // Create upload directory if it doesn't exist
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    public function uploadImage(): void
    {
        $user = $this->requireAuth();
        
        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            $this->errorResponse('No image file uploaded or upload error', 400);
            return;
        }
        
        $file = $_FILES['image'];
        
        // Validate file type
        if (!in_array($file['type'], $this->allowedTypes)) {
            $this->errorResponse('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.', 400);
            return;
        }
        
        // Validate file size
        if ($file['size'] > $this->maxFileSize) {
            $this->errorResponse('File size too large. Maximum size is 5MB.', 400);
            return;
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('img_') . '.' . $extension;
        $filePath = $this->uploadDir . $filename;
        
        try {
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                $this->errorResponse('Failed to save uploaded file', 500);
                return;
            }
            
            // Save to database
            $stmt = $this->db->prepare("
                INSERT INTO uploads (user_id, filename, original_name, file_path, file_size, mime_type)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user['id'],
                $filename,
                $file['name'],
                $filePath,
                $file['size'],
                $file['type']
            ]);
            
            $uploadId = $this->db->lastInsertId();
            
            // Return upload info
            $this->successResponse([
                'id' => $uploadId,
                'filename' => $filename,
                'original_name' => $file['name'],
                'url' => '/api/uploads/' . $filename,
                'size' => $file['size'],
                'type' => $file['type']
            ], 'Image uploaded successfully');
            
        } catch (\Exception $e) {
            // Clean up file if database insert fails
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            $this->errorResponse('Failed to save upload: ' . $e->getMessage(), 500);
        }
    }
    
    public function getImage(string $filename): void
    {
        $filePath = $this->uploadDir . $filename;
        
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'File not found']);
            return;
        }
        
        // Get file info from database
        $stmt = $this->db->prepare("SELECT * FROM uploads WHERE filename = ?");
        $stmt->execute([$filename]);
        $upload = $stmt->fetch();
        
        if (!$upload) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'File not found']);
            return;
        }
        
        // Set appropriate headers
        header('Content-Type: ' . $upload['mime_type']);
        header('Content-Length: ' . $upload['file_size']);
        header('Cache-Control: public, max-age=31536000'); // Cache for 1 year
        
        // Output file
        readfile($filePath);
    }
    
    public function listUploads(): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                SELECT id, filename, original_name, file_size, mime_type, created_at
                FROM uploads 
                WHERE user_id = ? 
                ORDER BY created_at DESC
            ");
            $stmt->execute([$user['id']]);
            $uploads = $stmt->fetchAll();
            
            // Add URL to each upload
            foreach ($uploads as &$upload) {
                $upload['url'] = '/api/uploads/' . $upload['filename'];
            }
            
            $this->successResponse($uploads);
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch uploads', 500);
        }
    }
    
    public function deleteUpload(string $id): void
    {
        $user = $this->requireAuth();
        
        try {
            // Get upload info
            $stmt = $this->db->prepare("
                SELECT * FROM uploads 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $user['id']]);
            $upload = $stmt->fetch();
            
            if (!$upload) {
                $this->errorResponse('Upload not found', 404);
                return;
            }
            
            // Delete file
            if (file_exists($upload['file_path'])) {
                unlink($upload['file_path']);
            }
            
            // Delete from database
            $stmt = $this->db->prepare("DELETE FROM uploads WHERE id = ?");
            $stmt->execute([$id]);
            
            $this->successResponse(null, 'Upload deleted successfully');
            
        } catch (\Exception $e) {
            $this->errorResponse('Failed to delete upload: ' . $e->getMessage(), 500);
        }
    }
}
