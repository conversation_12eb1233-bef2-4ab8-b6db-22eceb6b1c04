<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3001');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Simple autoloader for now (will be replaced by Composer autoloader)
spl_autoload_register(function ($class) {
    $prefix = 'App\\';
    $base_dir = __DIR__ . '/../src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

use App\Router;
use App\Controllers\AuthController;
use App\Controllers\TemplateController;
use App\Controllers\ContactController;
use App\Controllers\CampaignController;
use App\Controllers\EmailController;
use App\Controllers\UploadController;
use App\Controllers\DashboardController;
use App\Controllers\UserController;

try {
    $router = new Router();
    


    // Auth routes
    $router->post('/api/auth/login', [AuthController::class, 'login']);
    $router->post('/api/auth/register', [AuthController::class, 'register']);
    $router->get('/api/auth/verify', [AuthController::class, 'verify']);
    $router->post('/api/auth/logout', [AuthController::class, 'logout']);
    
    // Template routes
    $router->get('/api/templates', [TemplateController::class, 'index']);
    $router->post('/api/templates', [TemplateController::class, 'store']);
    $router->post('/api/templates/prebuilt', [TemplateController::class, 'createPrebuiltTemplates']);
    $router->get('/api/templates/{id}', [TemplateController::class, 'show']);
    $router->put('/api/templates/{id}', [TemplateController::class, 'update']);
    $router->delete('/api/templates/{id}', [TemplateController::class, 'destroy']);
    
    // Contact routes
    $router->get('/api/contacts', [ContactController::class, 'index']);
    $router->post('/api/contacts', [ContactController::class, 'store']);
    $router->get('/api/contacts/{id}', [ContactController::class, 'show']);
    $router->put('/api/contacts/{id}', [ContactController::class, 'update']);
    $router->delete('/api/contacts/{id}', [ContactController::class, 'destroy']);
    $router->post('/api/contacts/import', [ContactController::class, 'import']);
    
    // Campaign routes
    $router->get('/api/campaigns', [CampaignController::class, 'index']);
    $router->post('/api/campaigns', [CampaignController::class, 'store']);
    $router->get('/api/campaigns/{id}', [CampaignController::class, 'show']);
    $router->put('/api/campaigns/{id}', [CampaignController::class, 'update']);
    $router->delete('/api/campaigns/{id}', [CampaignController::class, 'destroy']);
    $router->post('/api/campaigns/{id}/send', [CampaignController::class, 'send']);

    // Email routes
    $router->post('/api/email/test', [EmailController::class, 'sendTestEmail']);
    $router->get('/api/email/config/validate', [EmailController::class, 'validateEmailConfig']);
    $router->post('/api/email/campaign/send', [EmailController::class, 'sendCampaignEmails']);

    // Upload routes
    $router->post('/api/uploads/image', [UploadController::class, 'uploadImage']);
    $router->get('/api/uploads/{filename}', [UploadController::class, 'getImage']);
    $router->get('/api/uploads', [UploadController::class, 'listUploads']);
    $router->delete('/api/uploads/{id}', [UploadController::class, 'deleteUpload']);

    // Dashboard routes
    $router->get('/api/dashboard/stats', [DashboardController::class, 'getStats']);
    $router->get('/api/dashboard/activity', [DashboardController::class, 'getActivity']);
    $router->get('/api/dashboard/campaigns', [DashboardController::class, 'getCampaigns']);
    $router->get('/api/dashboard/contacts/growth', [DashboardController::class, 'getContactGrowthData']);

    // User profile routes
    $router->get('/api/user/profile', [UserController::class, 'getProfile']);
    $router->put('/api/user/profile', [UserController::class, 'updateProfile']);
    $router->put('/api/user/password', [UserController::class, 'updatePassword']);
    $router->get('/api/user/notifications', [UserController::class, 'getNotificationSettings']);
    $router->put('/api/user/notifications', [UserController::class, 'updateNotificationSettings']);

    $router->dispatch();
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}
