<?php

namespace App\Services;

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    private PHPMailer $mailer;
    private array $config;

    public function __construct()
    {
        $this->loadConfiguration();
        $this->mailer = new PHPMailer(true);
        $this->configureMailer();
    }

    private function loadConfiguration(): void
    {
        // Load environment variables from .env file
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }
                if (strpos($line, '=') !== false) {
                    list($name, $value) = explode('=', $line, 2);
                    $_ENV[trim($name)] = trim($value);
                }
            }
        }

        $this->config = [
            'host' => $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com',
            'port' => (int)($_ENV['SMTP_PORT'] ?? 587),
            'username' => $_ENV['SMTP_USERNAME'] ?? '',
            'password' => $_ENV['SMTP_PASSWORD'] ?? '',
            'encryption' => $_ENV['SMTP_ENCRYPTION'] ?? 'tls',
            'from_email' => $_ENV['SMTP_FROM_EMAIL'] ?? '<EMAIL>',
            'from_name' => $_ENV['SMTP_FROM_NAME'] ?? 'Email Marketing Platform'
        ];
    }

    private function configureMailer(): void
    {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['username'];
            $this->mailer->Password = $this->config['password'];

            // Set encryption and port based on configuration
            if (strtolower($this->config['encryption']) === 'ssl') {
                $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                $this->mailer->Port = $this->config['port'] ?: 465;
            } else {
                $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                $this->mailer->Port = $this->config['port'] ?: 587;
            }

            // Default sender
            $this->mailer->setFrom(
                $this->config['from_email'],
                $this->config['from_name']
            );

            // Additional settings for better compatibility
            $this->mailer->SMTPOptions = [
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ];

            // Disable debug output in production
            $this->mailer->SMTPDebug = SMTP::DEBUG_OFF;
            $this->mailer->Debugoutput = 'error_log';

            // Set timeout
            $this->mailer->Timeout = 30;

        } catch (Exception $e) {
            error_log("PHPMailer configuration failed: " . $e->getMessage());
            throw new \Exception("Email configuration failed: " . $e->getMessage());
        }
    }
    
    public function sendEmail(string $to, string $subject, string $htmlBody, string $textBody = '', array $attachments = []): bool
    {
        try {
            // Clear any previous recipients
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            // Recipients
            $this->mailer->addAddress($to);
            
            // Content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $htmlBody;
            $this->mailer->AltBody = $textBody ?: strip_tags($htmlBody);
            
            // Add attachments if any
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $this->mailer->addAttachment(
                        $attachment['path'],
                        $attachment['name'] ?? basename($attachment['path'])
                    );
                }
            }
            
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function sendTestEmail(string $to, string $templateName, string $htmlContent): bool
    {
        $subject = "Test Email: {$templateName}";
        $htmlBody = $this->wrapInEmailTemplate($htmlContent, $templateName);
        
        return $this->sendEmail($to, $subject, $htmlBody);
    }
    
    public function sendCampaignEmail(string $to, string $subject, string $htmlContent, array $personalizations = []): bool
    {
        // Apply personalizations
        $personalizedContent = $this->applyPersonalizations($htmlContent, $personalizations);
        $personalizedSubject = $this->applyPersonalizations($subject, $personalizations);
        
        return $this->sendEmail($to, $personalizedSubject, $personalizedContent);
    }
    
    private function wrapInEmailTemplate(string $content, string $title = ''): string
    {
        return "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
                .email-container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
                .email-header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
                .email-content { padding: 20px; }
                .email-footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='email-header'>
                    <h1>{$title}</h1>
                </div>
                <div class='email-content'>
                    {$content}
                </div>
                <div class='email-footer'>
                    <p>This is a test email from Email Marketing Platform</p>
                    <p>If you received this email in error, please ignore it.</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    private function applyPersonalizations(string $content, array $personalizations): string
    {
        foreach ($personalizations as $key => $value) {
            $content = str_replace("{{" . $key . "}}", $value, $content);
        }
        return $content;
    }
    
    public function validateEmailConfiguration(): array
    {
        $errors = [];

        if (empty($this->config['host'])) {
            $errors[] = 'SMTP_HOST is not configured';
        }

        if (empty($this->config['username'])) {
            $errors[] = 'SMTP_USERNAME is not configured';
        }

        if (empty($this->config['password'])) {
            $errors[] = 'SMTP_PASSWORD is not configured';
        }

        if (empty($this->config['from_email']) || !filter_var($this->config['from_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'SMTP_FROM_EMAIL is not configured or invalid';
        }

        if (!in_array($this->config['port'], [25, 465, 587, 2525])) {
            $errors[] = 'SMTP_PORT should be one of: 25, 465, 587, 2525';
        }

        if (!in_array(strtolower($this->config['encryption']), ['tls', 'ssl', 'starttls'])) {
            $errors[] = 'SMTP_ENCRYPTION should be one of: tls, ssl, starttls';
        }

        return $errors;
    }
    
    public function testConnection(): bool
    {
        try {
            return $this->mailer->smtpConnect();
        } catch (Exception $e) {
            error_log("SMTP connection test failed: " . $e->getMessage());
            return false;
        }
    }
}
