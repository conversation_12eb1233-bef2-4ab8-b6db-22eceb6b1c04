import React from 'react'
import { TemplateComponent } from '../../types/templateEditor'

interface ComponentRendererProps {
  component: TemplateComponent
  isSelected?: boolean
  onUpdate?: (updates: Partial<TemplateComponent>) => void
}

export const ComponentRenderer: React.FC<ComponentRendererProps> = ({
  component,
  isSelected = false,
  onUpdate
}) => {
  const renderComponent = () => {
    switch (component.type) {
      case 'text':
        return (
          <div
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            {component.content?.text || 'Your text here...'}
          </div>
        )

      case 'heading':
        const HeadingTag = (component.content?.level || 'h2') as keyof React.JSX.IntrinsicElements
        return (
          <HeadingTag
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            {component.content?.text || 'Your Heading'}
          </HeadingTag>
        )

      case 'image':
        return (
          <img
            src={component.content?.src || 'https://via.placeholder.com/300x200'}
            alt={component.content?.alt || 'Image'}
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          />
        )

      case 'button':
        return (
          <a
            href={component.content?.href || '#'}
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            {component.content?.text || 'Click Here'}
          </a>
        )

      case 'divider':
        return (
          <hr
            style={component.styles}
            className={`border-0 ${isSelected ? 'outline-none' : ''}`}
          />
        )

      case 'spacer':
        return (
          <div
            style={component.styles}
            className={`${isSelected ? 'outline-none border border-dashed border-gray-300' : ''}`}
          >
            {isSelected && (
              <div className="text-xs text-gray-400 text-center py-2">
                Spacer
              </div>
            )}
          </div>
        )

      case 'container':
        return (
          <div
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            {component.children?.map((child) => (
              <ComponentRenderer
                key={child.id}
                component={child}
                onUpdate={onUpdate}
              />
            )) || (
              <div className="text-gray-400 text-center py-4">
                Drop components here
              </div>
            )}
          </div>
        )

      case 'header':
        return (
          <header
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            {component.content?.logo && (
              <img
                src={component.content.logo}
                alt="Logo"
                style={{ maxHeight: '40px', marginBottom: '10px' }}
              />
            )}
            <h1 style={{ margin: 0, fontSize: '24px' }}>
              {component.content?.title || 'Your Company'}
            </h1>
          </header>
        )

      case 'footer':
        return (
          <footer
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            <p style={{ margin: 0 }}>
              {component.content?.text || '© 2024 Your Company. All rights reserved.'}
            </p>
            {component.content?.links && component.content.links.length > 0 && (
              <div style={{ marginTop: '10px' }}>
                {component.content.links.map((link: any, index: number) => (
                  <a
                    key={index}
                    href={link.href}
                    style={{ marginRight: '15px', color: 'inherit' }}
                  >
                    {link.text}
                  </a>
                ))}
              </div>
            )}
          </footer>
        )

      case 'product-card':
        return (
          <div
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            {component.content?.image && (
              <img
                src={component.content.image}
                alt={component.content?.title || 'Product'}
                style={{ width: '100%', marginBottom: '10px' }}
              />
            )}
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>
              {component.content?.title || 'Product Name'}
            </h3>
            <p style={{ margin: '0 0 10px 0', color: '#666' }}>
              {component.content?.description || 'Product description here...'}
            </p>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ fontSize: '20px', fontWeight: 'bold' }}>
                {component.content?.price || '$99.99'}
              </span>
              <a
                href="#"
                style={{
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  padding: '8px 16px',
                  textDecoration: 'none',
                  borderRadius: '4px'
                }}
              >
                {component.content?.buttonText || 'Buy Now'}
              </a>
            </div>
          </div>
        )

      case 'social-media':
        const platforms = component.content?.platforms || ['facebook', 'twitter', 'instagram']
        return (
          <div
            style={component.styles}
            className={`${isSelected ? 'outline-none' : ''}`}
          >
            <div style={{ display: 'flex', justifyContent: 'center', gap: '15px' }}>
              {platforms.map((platform: string) => (
                <a
                  key={platform}
                  href="#"
                  style={{
                    display: 'inline-block',
                    width: '32px',
                    height: '32px',
                    backgroundColor: getSocialColor(platform),
                    borderRadius: '50%',
                    textAlign: 'center',
                    lineHeight: '32px',
                    color: 'white',
                    textDecoration: 'none'
                  }}
                >
                  {getSocialIcon(platform)}
                </a>
              ))}
            </div>
          </div>
        )

      default:
        return (
          <div
            style={component.styles}
            className={`p-4 border border-dashed border-gray-300 ${isSelected ? 'outline-none' : ''}`}
          >
            Unknown component type: {component.type}
          </div>
        )
    }
  }

  return renderComponent()
}

const getSocialColor = (platform: string): string => {
  const colors: Record<string, string> = {
    facebook: '#1877f2',
    twitter: '#1da1f2',
    instagram: '#e4405f',
    linkedin: '#0077b5',
    youtube: '#ff0000',
    pinterest: '#bd081c'
  }
  return colors[platform] || '#666666'
}

const getSocialIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    facebook: 'f',
    twitter: 't',
    instagram: 'i',
    linkedin: 'in',
    youtube: 'y',
    pinterest: 'p'
  }
  return icons[platform] || '?'
}
