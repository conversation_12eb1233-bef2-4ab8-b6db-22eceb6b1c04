import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Plus, Mail, Play, Pause, Edit, Trash2, Bar<PERSON>hart3, Loader2 } from 'lucide-react'
import { api } from '../services/api'
import { useNotifications } from '../hooks/useNotifications'
import { Campaign } from '../types'

const Campaigns: React.FC = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { showError, showSuccess } = useNotifications()

  useEffect(() => {
    fetchCampaigns()
  }, [])

  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await api.get<Campaign[]>('/campaigns')
      setCampaigns(response || [])
    } catch (err: any) {
      console.error('Failed to fetch campaigns:', err)
      setError('Failed to load campaigns')
      showError('Failed to load campaigns')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCampaign = async (campaignId: number) => {
    if (!confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
      return
    }

    try {
      await api.delete(`/campaigns/${campaignId}`)
      setCampaigns(campaigns.filter(c => c.id !== campaignId))
      showSuccess('Campaign deleted successfully')
    } catch (err: any) {
      console.error('Failed to delete campaign:', err)
      showError('Failed to delete campaign')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800'
      case 'sending':
        return 'bg-blue-100 text-blue-800'
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'paused':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sending':
        return <Play className="w-4 h-4" />
      case 'paused':
        return <Pause className="w-4 h-4" />
      default:
        return <Mail className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">Loading campaigns...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Mail className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to load campaigns
          </h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={fetchCampaigns}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Email Campaigns</h1>
          <p className="text-gray-600">Create and manage your email campaigns</p>
        </div>
        <Link
          to="/campaigns/new"
          className="btn btn-primary flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Campaign
        </Link>
      </div>

      {/* Campaigns Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campaign
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Recipients
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Delivered
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Opened
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Clicked
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {campaigns.map((campaign) => (
                <tr key={campaign.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {campaign.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {campaign.subject}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                      {getStatusIcon(campaign.status)}
                      <span className="ml-1 capitalize">{campaign.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.total_recipients > 0 ? campaign.total_recipients.toLocaleString() : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.delivered_count > 0 ? (
                      <>
                        {campaign.delivered_count.toLocaleString()}
                        {campaign.total_recipients > 0 && (
                          <span className="text-gray-500 ml-1">
                            ({((campaign.delivered_count / campaign.total_recipients) * 100).toFixed(1)}%)
                          </span>
                        )}
                      </>
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.opened_count > 0 ? (
                      <>
                        {campaign.opened_count.toLocaleString()}
                        {campaign.delivered_count > 0 && (
                          <span className="text-gray-500 ml-1">
                            ({((campaign.opened_count / campaign.delivered_count) * 100).toFixed(1)}%)
                          </span>
                        )}
                      </>
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.clicked_count > 0 ? (
                      <>
                        {campaign.clicked_count.toLocaleString()}
                        {campaign.delivered_count > 0 && (
                          <span className="text-gray-500 ml-1">
                            ({((campaign.clicked_count / campaign.delivered_count) * 100).toFixed(1)}%)
                          </span>
                        )}
                      </>
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {campaign.sent_at ? (
                      <div>
                        <div>Sent</div>
                        <div>{new Date(campaign.sent_at).toLocaleDateString()}</div>
                      </div>
                    ) : campaign.scheduled_at ? (
                      <div>
                        <div>Scheduled</div>
                        <div>{new Date(campaign.scheduled_at).toLocaleDateString()}</div>
                      </div>
                    ) : (
                      <div>
                        <div>Created</div>
                        <div>{new Date(campaign.created_at).toLocaleDateString()}</div>
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      {campaign.status === 'sent' && (
                        <button className="text-blue-600 hover:text-blue-900">
                          <BarChart3 className="w-4 h-4" />
                        </button>
                      )}
                      {(campaign.status === 'draft' || campaign.status === 'scheduled') && (
                        <Link
                          to={`/campaigns/edit/${campaign.id}`}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          <Edit className="w-4 h-4" />
                        </Link>
                      )}
                      <button
                        onClick={() => handleDeleteCampaign(campaign.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete campaign"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {campaigns.length === 0 && (
        <div className="text-center py-12">
          <Mail className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No campaigns yet
          </h3>
          <p className="text-gray-600 mb-6">
            Get started by creating your first email campaign
          </p>
          <Link
            to="/campaigns/new"
            className="btn btn-primary"
          >
            Create Your First Campaign
          </Link>
        </div>
      )}
    </div>
  )
}

export default Campaigns
