<?php
// Simple test script to verify the API is working

echo "Testing Backend API...\n\n";

// Test 1: Login
echo "1. Testing login endpoint...\n";
$loginData = json_encode([
    'email' => '<EMAIL>',
    'password' => 'password'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => $loginData
    ]
]);

$response = file_get_contents('http://localhost:8000/api/auth/login', false, $context);
if ($response) {
    echo "✅ Login response: " . $response . "\n\n";
    
    $loginResult = json_decode($response, true);
    if ($loginResult['success']) {
        $token = $loginResult['data']['token'];
        
        // Test 2: Dashboard stats
        echo "2. Testing dashboard stats...\n";
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => "Authorization: Bearer $token\r\n"
            ]
        ]);
        
        $statsResponse = file_get_contents('http://localhost:8000/api/dashboard/stats', false, $context);
        if ($statsResponse) {
            echo "✅ Dashboard stats: " . $statsResponse . "\n\n";
        } else {
            echo "❌ Dashboard stats failed\n\n";
        }
        
        // Test 3: Templates
        echo "3. Testing templates...\n";
        $templatesResponse = file_get_contents('http://localhost:8000/api/templates', false, $context);
        if ($templatesResponse) {
            echo "✅ Templates: " . $templatesResponse . "\n\n";
        } else {
            echo "❌ Templates failed\n\n";
        }
    }
} else {
    echo "❌ Login failed\n";
}

echo "Test completed!\n";
?>
