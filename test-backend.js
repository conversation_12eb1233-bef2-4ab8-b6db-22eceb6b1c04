// Simple test script to verify backend API is working
const axios = require('axios');

const API_BASE = 'http://localhost:8000/api';

async function testBackend() {
  console.log('🧪 Testing Backend API...\n');

  try {
    // Test 1: Login
    console.log('1. Testing login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      const token = loginResponse.data.data.token;
      
      // Test 2: Dashboard stats
      console.log('2. Testing dashboard stats...');
      const statsResponse = await axios.get(`${API_BASE}/dashboard/stats`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (statsResponse.data.success) {
        console.log('✅ Dashboard stats working');
        console.log('   Data:', JSON.stringify(statsResponse.data.data, null, 2));
      } else {
        console.log('❌ Dashboard stats failed:', statsResponse.data.message);
      }
      
      // Test 3: Templates
      console.log('3. Testing templates...');
      const templatesResponse = await axios.get(`${API_BASE}/templates`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (templatesResponse.data.success) {
        console.log('✅ Templates working');
        console.log(`   Found ${templatesResponse.data.data.length} templates`);
      } else {
        console.log('❌ Templates failed:', templatesResponse.data.message);
      }
      
      // Test 4: Contacts
      console.log('4. Testing contacts...');
      const contactsResponse = await axios.get(`${API_BASE}/contacts`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (contactsResponse.data.success) {
        console.log('✅ Contacts working');
        console.log(`   Found ${contactsResponse.data.data.length} contacts`);
      } else {
        console.log('❌ Contacts failed:', contactsResponse.data.message);
      }
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }
    
  } catch (error) {
    console.log('❌ Backend test failed:', error.message);
    if (error.response) {
      console.log('   Response:', error.response.data);
    }
  }
}

// Run the test
testBackend();
