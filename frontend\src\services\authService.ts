import { apiService } from './api'
import { User, AuthResponse, LoginCredentials, RegisterCredentials } from '../types'

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/login', credentials)
  }

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/register', credentials)
  }

  async verifyToken(_token: string): Promise<User> {
    return apiService.get<User>('/auth/verify')
  }

  async logout(): Promise<void> {
    return apiService.post<void>('/auth/logout')
  }
}

export const authService = new AuthService()
