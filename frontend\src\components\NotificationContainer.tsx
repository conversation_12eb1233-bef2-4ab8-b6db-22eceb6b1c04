import React from 'react'
import Notification, { NotificationProps } from './Notification'

interface NotificationContainerProps {
  notifications: NotificationProps[]
  onClose: (id: string) => void
}

const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onClose
}) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          {...notification}
          onClose={onClose}
        />
      ))}
    </div>
  )
}

export default NotificationContainer
