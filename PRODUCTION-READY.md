# 📧 Email Marketing Platform - Production Ready

A comprehensive, production-ready email marketing platform built with React, TypeScript, PHP, and modern web technologies. Create beautiful email templates, manage contacts, and run successful email campaigns with real email delivery.

## ✨ New Production Features

### 🎨 Enhanced Visual Template Editor
- **Advanced Object Controls**: Resize, move, and manipulate elements with precision
- **Manual Selection**: Click to select, drag to move, resize handles like Adobe Illustrator
- **Image Upload**: Built-in image upload and management system
- **Auto-save**: Automatic template saving with persistence
- **Size Controls**: Increase/decrease object sizes with dedicated controls
- **Real-time Preview**: Live preview with desktop, tablet, and mobile views

### 📧 Real Email Functionality
- **PHPMailer Integration**: Send real emails through SMTP
- **Test Email**: Send test emails to verify templates before campaigns
- **Campaign Delivery**: Bulk email sending with delivery tracking
- **Email Configuration**: Proper SMTP setup and validation
- **Error Handling**: Comprehensive error handling and user feedback

### 👥 Advanced Contact Management
- **CSV Import**: Bulk import contacts from CSV files with validation
- **Real API Integration**: Connected to PHP backend instead of mock data
- **Contact CRUD**: Full create, read, update, delete operations
- **Export Functionality**: Export contacts to CSV
- **Status Management**: Track contact status (active, unsubscribed, bounced)

### 🏗️ Production Infrastructure
- **PHP Backend**: Switched from Node.js mock to production PHP backend
- **Real Database**: SQLite database with proper schema and relationships
- **File Upload**: Image upload system for template assets
- **Email Service**: Complete email service with PHPMailer
- **Error Handling**: Proper validation and error handling throughout
- **Security**: Input validation, SQL injection prevention

## 🚀 Quick Start

### Prerequisites
- PHP 8.0 or higher
- Node.js 18 or higher
- Composer (for PHP dependencies)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd email-marketing-platform
   ```

2. **Setup Backend**
   ```bash
   cd backend
   composer install
   php setup.php
   ```

3. **Setup Frontend**
   ```bash
   cd frontend
   npm install
   ```

4. **Configure Email (Optional)**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your SMTP settings
   ```

5. **Start the Application**
   ```bash
   # Use the provided batch file (Windows)
   start-app.bat
   
   # Or start manually:
   # Terminal 1 - Backend
   cd backend && php -S localhost:8000 -t public
   
   # Terminal 2 - Frontend
   cd frontend && npm run dev
   ```

6. **Access the Application**
   - Frontend: http://localhost:3001
   - Backend API: http://localhost:8000
   - Demo Login: <EMAIL> / password

## 📧 Email Configuration

To enable real email sending, configure your SMTP settings in `backend/.env`:

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_ENCRYPTION=tls
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Your App Name
```

### Gmail Setup
1. Enable 2-factor authentication
2. Generate an app password
3. Use the app password in SMTP_PASSWORD

## 🎯 What's Working Now

### ✅ Template Editor
- Drag and drop components
- Real-time editing and preview
- Image upload and management
- Object selection with resize handles
- Size controls (increase/decrease)
- Auto-save functionality
- Persistence across page reloads

### ✅ Email Functionality
- Send test emails
- Real SMTP integration
- Email validation and error handling
- Campaign email delivery

### ✅ Contact Management
- CSV import with validation
- Real database integration
- Contact CRUD operations
- Export functionality
- Status management

### ✅ Production Features
- Real PHP backend
- Proper database schema
- File upload system
- Error handling
- Security measures
- User authentication

## 🛠️ Technical Architecture

### Frontend Stack
- **React 19** with TypeScript
- **Vite** for fast development
- **Tailwind CSS** for styling
- **React DnD** for drag and drop
- **Axios** for API calls

### Backend Stack
- **PHP 8+** with modern features
- **SQLite** database (easily switchable to MySQL)
- **PHPMailer** for email sending
- **JWT** for authentication
- **RESTful API** design

### Database Schema
- Users with authentication
- Templates with components and settings
- Contacts with status management
- Campaigns with recipient tracking
- File uploads for images
- Email logs for tracking

## 📱 Features Overview

### Template Editor
- Visual drag-and-drop interface
- Component library (text, images, buttons, headers, footers)
- Real-time preview modes (desktop, tablet, mobile)
- Image upload and management
- Advanced object controls and selection
- Auto-save and persistence

### Contact Management
- Import contacts from CSV
- Manual contact creation
- Contact status tracking
- Export functionality
- Search and filtering

### Campaign Management
- Create campaigns with templates
- Send test emails
- Bulk email delivery
- Campaign tracking and analytics

### Email Delivery
- SMTP integration with PHPMailer
- Email validation and testing
- Delivery tracking
- Error handling and reporting

## 🔧 Development

### Backend Development
```bash
cd backend
php -S localhost:8000 -t public
```

### Frontend Development
```bash
cd frontend
npm run dev
```

### Database Reset
```bash
cd backend
rm database.sqlite
php setup.php
```

## 📝 API Endpoints

### Authentication
- POST `/api/auth/login` - User login
- POST `/api/auth/register` - User registration
- GET `/api/auth/verify` - Verify token

### Templates
- GET `/api/templates` - List templates
- POST `/api/templates` - Create template
- GET `/api/templates/{id}` - Get template
- PUT `/api/templates/{id}` - Update template
- DELETE `/api/templates/{id}` - Delete template

### Contacts
- GET `/api/contacts` - List contacts
- POST `/api/contacts` - Create contact
- POST `/api/contacts/import` - Import CSV
- PUT `/api/contacts/{id}` - Update contact
- DELETE `/api/contacts/{id}` - Delete contact

### Email
- POST `/api/email/test` - Send test email
- GET `/api/email/config/validate` - Validate email config

### Uploads
- POST `/api/uploads/image` - Upload image
- GET `/api/uploads` - List uploads
- GET `/api/uploads/{filename}` - Get image
- DELETE `/api/uploads/{id}` - Delete upload

## 🎉 Ready for Production

This email marketing platform is now production-ready with:
- Real email sending capabilities
- Persistent data storage
- Advanced template editor
- Contact management system
- File upload functionality
- Proper error handling
- Security measures
- Professional UI/UX

You can now use this platform to create and send real email campaigns!
