import { apiService } from './api'

export interface UploadedImage {
  id: number
  filename: string
  original_name: string
  url: string
  size: number
  type: string
}

export const uploadService = {
  // Upload an image file
  uploadImage: async (file: File): Promise<UploadedImage> => {
    const formData = new FormData()
    formData.append('image', file)
    
    return apiService.upload<UploadedImage>('/uploads/image', formData)
  },

  // Get list of uploaded images
  getUploads: async (): Promise<UploadedImage[]> => {
    return apiService.get<UploadedImage[]>('/uploads')
  },

  // Delete an uploaded image
  deleteUpload: async (id: number): Promise<void> => {
    return apiService.delete<void>(`/uploads/${id}`)
  },

  // Get image URL
  getImageUrl: (filename: string): string => {
    return `/api/uploads/${filename}`
  }
}
