<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class UserController extends BaseController
{
    public function getProfile(): void
    {
        $user = $this->requireAuth();
        
        // Remove password from response
        unset($user['password']);
        
        $this->successResponse($user);
    }
    
    public function updateProfile(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        $errors = $this->validateRequired($data, ['name', 'email']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        if (!$this->validateEmail($data['email'])) {
            $errors['email'] = ['Please provide a valid email address'];
        }
        
        // Check if email is already taken by another user
        if ($data['email'] !== $user['email']) {
            $stmt = $this->db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$data['email'], $user['id']]);
            if ($stmt->fetch()) {
                $errors['email'] = ['This email address is already in use'];
            }
        }
        
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET name = ?, email = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$data['name'], $data['email'], $user['id']]);
            
            // Get updated user
            $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $updatedUser = $stmt->fetch();
            
            // Remove password from response
            unset($updatedUser['password']);
            
            $this->successResponse([
                'user' => $updatedUser
            ], 'Profile updated successfully');
            
        } catch (\Exception $e) {
            $this->errorResponse('Failed to update profile', 500);
        }
    }
    
    public function updatePassword(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        $errors = $this->validateRequired($data, ['current_password', 'new_password']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        // Verify current password
        if (!password_verify($data['current_password'], $user['password'])) {
            $this->errorResponse('Current password is incorrect', 400);
            return;
        }
        
        // Validate new password
        if (strlen($data['new_password']) < 6) {
            $this->errorResponse('New password must be at least 6 characters long', 400);
            return;
        }
        
        try {
            $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
            
            $stmt = $this->db->prepare("
                UPDATE users 
                SET password = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$hashedPassword, $user['id']]);
            
            $this->successResponse(null, 'Password updated successfully');
            
        } catch (\Exception $e) {
            $this->errorResponse('Failed to update password', 500);
        }
    }
    
    public function getNotificationSettings(): void
    {
        $user = $this->requireAuth();
        
        try {
            $stmt = $this->db->prepare("
                SELECT notification_settings FROM users WHERE id = ?
            ");
            $stmt->execute([$user['id']]);
            $result = $stmt->fetch();
            
            $settings = $result['notification_settings'] 
                ? json_decode($result['notification_settings'], true)
                : [
                    'emailNotifications' => true,
                    'campaignUpdates' => true,
                    'systemAlerts' => true,
                    'weeklyReports' => false
                ];
            
            $this->successResponse($settings);
            
        } catch (\Exception $e) {
            $this->errorResponse('Failed to fetch notification settings', 500);
        }
    }
    
    public function updateNotificationSettings(): void
    {
        $user = $this->requireAuth();
        $data = $this->getJsonInput();
        
        // Validate notification settings structure
        $validKeys = ['emailNotifications', 'campaignUpdates', 'systemAlerts', 'weeklyReports'];
        $settings = [];
        
        foreach ($validKeys as $key) {
            $settings[$key] = isset($data[$key]) ? (bool)$data[$key] : false;
        }
        
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET notification_settings = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([json_encode($settings), $user['id']]);
            
            $this->successResponse($settings, 'Notification settings updated successfully');
            
        } catch (\Exception $e) {
            $this->errorResponse('Failed to update notification settings', 500);
        }
    }
}
